using System.ComponentModel.DataAnnotations;

namespace EduVerse.Application.DTOs.Payment
{
    /// <summary>
    /// Request DTO for creating a new payment order
    /// </summary>
    public class CreateOrderRequestDto
    {
        /// <summary>
        /// Course ID for which payment is being made
        /// </summary>
        [Required]
        public int CourseId { get; set; }

        /// <summary>
        /// User ID making the payment
        /// </summary>
        public int UserId { get; set; }

        /// <summary>
        /// Billing address for the payment
        /// </summary>
        public string BillingAddress { get; set; }

        /// <summary>
        /// Notes or additional information for the payment
        /// </summary>
        public string Notes { get; set; }
    }
}
