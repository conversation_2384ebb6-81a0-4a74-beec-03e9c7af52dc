using EduVerse.Core.Entities;
using EduVerse.Core.Interfaces;
using EduVerse.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace EduVerse.Infrastructure.Repositories
{
    /// <summary>
    /// Repository for discussion entity
    /// </summary>
    public class DiscussionRepository : Repository<Discussion>, IDiscussionRepository
    {
        private readonly ApplicationDbContext _context;

        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="context">Database context</param>
        public DiscussionRepository(ApplicationDbContext context) : base(context)
        {
            _context = context;
        }

        /// <summary>
        /// Get discussions by user ID
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>List of discussions</returns>
        public async Task<IEnumerable<Discussion>> GetByUserIdAsync(int userId)
        {
            return await _context.Discussions
                .Where(d => d.UserId == userId)
                .Include(d => d.User)
                .Include(d => d.Course)
                .OrderByDescending(d => d.CreatedAt)
                .ToListAsync();
        }

        /// <summary>
        /// Get discussions by course ID
        /// </summary>
        /// <param name="courseId">Course ID</param>
        /// <returns>List of discussions</returns>
        public async Task<IEnumerable<Discussion>> GetByCourseIdAsync(int courseId)
        {
            return await _context.Discussions
                .Where(d => d.CourseId == courseId)
                .Include(d => d.User)
                .Include(d => d.Course)
                .OrderByDescending(d => d.CreatedAt)
                .ToListAsync();
        }

        /// <summary>
        /// Get discussion with replies
        /// </summary>
        /// <param name="id">Discussion ID</param>
        /// <returns>Discussion with replies</returns>
        public async Task<Discussion> GetWithRepliesAsync(int id)
        {
            return await _context.Discussions
                .Where(d => d.Id == id)
                .Include(d => d.User)
                .Include(d => d.Course)
                .Include(d => d.Replies)
                    .ThenInclude(r => r.User)
                .Include(d => d.Replies)
                    .ThenInclude(r => r.ParentReply)
                .FirstOrDefaultAsync();
        }

        /// <summary>
        /// Mark discussion as resolved
        /// </summary>
        /// <param name="id">Discussion ID</param>
        /// <returns>True if successful</returns>
        public async Task<bool> MarkAsResolvedAsync(int id)
        {
            var discussion = await _context.Discussions.FindAsync(id);
            if (discussion == null)
                return false;

            discussion.IsResolved = true;
            await _context.SaveChangesAsync();
            return true;
        }
    }
}
