<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Dashboard - EduVerse</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        h1 {
            color: #DF2771;
        }
        button {
            background-color: #DF2771;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 0;
        }
        button:hover {
            background-color: #FA4B37;
        }
        pre {
            background-color: #f0f0f0;
            padding: 10px;
            border-radius: 5px;
            overflow: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Dashboard Test Page</h1>
        <p>This page helps test the admin dashboard functionality.</p>

        <h2>Authentication Status</h2>
        <div id="auth-status">Checking...</div>

        <h2>Actions</h2>
        <button id="mock-login">Perform Mock Login</button>
        <button id="clear-storage">Clear localStorage</button>
        <button id="go-dashboard">Go to Dashboard</button>

        <h2>Direct Links</h2>
        <p>Use these links if the buttons don't work:</p>
        <ul>
            <li><a href="dashboard.html">Dashboard (relative path)</a></li>
            <li><a href="/admin/dashboard.html">Dashboard (root-relative path)</a></li>
            <li><a href="../admin/dashboard.html">Dashboard (parent path)</a></li>
        </ul>

        <h2>localStorage Contents</h2>
        <pre id="storage-contents">Loading...</pre>
    </div>

    <script>
        // Mock admin login function
        function mockAdminLogin() {
            console.log('Using mock admin login');

            // Create a mock admin user
            const mockAdminUser = {
                id: 1,
                username: 'admin',
                fullName: 'Admin User',
                email: '<EMAIL>',
                Role: 'Admin',
                role: 'Admin'
            };

            // Create a mock token
            const mockToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxIiwibmFtZSI6IkFkbWluIFVzZXIiLCJyb2xlIjoiQWRtaW4ifQ.8tat9AtQmHePmf';

            // Store in localStorage
            localStorage.setItem('user', JSON.stringify(mockAdminUser));
            localStorage.setItem('token', mockToken);

            console.log('Mock admin login completed');
            updateUI();
            return true;
        }

        // Update UI with current status
        function updateUI() {
            // Update auth status
            const token = localStorage.getItem('token');
            const user = JSON.parse(localStorage.getItem('user') || '{}');
            const authStatus = document.getElementById('auth-status');

            if (token) {
                authStatus.innerHTML = `
                    <p style="color: green;">✓ Authenticated</p>
                    <p>Token: ${token.substring(0, 20)}...</p>
                    <p>User: ${user.username || 'N/A'}</p>
                    <p>Role: ${user.Role || user.role || 'N/A'}</p>
                `;
            } else {
                authStatus.innerHTML = '<p style="color: red;">✗ Not authenticated</p>';
            }

            // Update localStorage contents
            const storageContents = document.getElementById('storage-contents');
            let contents = '';

            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                let value = localStorage.getItem(key);

                // Truncate long values
                if (value.length > 100) {
                    value = value.substring(0, 100) + '...';
                }

                contents += `${key}: ${value}\n`;
            }

            storageContents.textContent = contents || 'No items in localStorage';
        }

        // Set up event listeners
        document.addEventListener('DOMContentLoaded', function() {
            updateUI();

            document.getElementById('mock-login').addEventListener('click', function() {
                mockAdminLogin();
            });

            document.getElementById('clear-storage').addEventListener('click', function() {
                localStorage.clear();
                updateUI();
            });

            document.getElementById('go-dashboard').addEventListener('click', function() {
                // Use the correct path to the dashboard
                window.location.href = 'dashboard.html';

                // Log the current path for debugging
                console.log('Current path:', window.location.pathname);
                console.log('Navigating to dashboard at:', window.location.origin + window.location.pathname.replace('test-dashboard.html', 'dashboard.html'));
            });
        });
    </script>
</body>
</html>
