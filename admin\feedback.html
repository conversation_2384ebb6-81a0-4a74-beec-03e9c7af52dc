<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Feedback Management - EduVerse Admin</title>
    <link rel="shortcut icon" type="png" href="../images/icon/favicon.png">
    <link rel="stylesheet" href="css/admin-style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.4.1/jquery.min.js"></script>
    <style>
        /* Additional styles for feedback management */
        .action-buttons {
            display: flex;
            gap: 10px;
        }

        .btn {
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            border: none;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .btn-secondary {
            background-color: #2196F3;
            color: white;
        }

        .btn-success {
            background-color: #4CAF50;
            color: white;
        }

        .btn-danger {
            background-color: #F44336;
            color: white;
        }

        .btn:hover {
            opacity: 0.8;
        }

        .filter-section {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
            align-items: center;
        }

        .search-input {
            padding: 10px;
            border-radius: 5px;
            border: 1px solid #ddd;
            width: 300px;
        }

        .status-filter {
            padding: 10px;
            border-radius: 5px;
            border: 1px solid #ddd;
        }

        .date-filter {
            padding: 10px;
            border-radius: 5px;
            border: 1px solid #ddd;
        }

        .feedback-cards {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .feedback-card {
            background-color: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
            padding: 20px;
        }

        .feedback-card:hover {
            transform: translateY(-5px);
        }

        .feedback-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
        }

        .user-info {
            display: flex;
            align-items: center;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            margin-right: 10px;
            object-fit: cover;
        }

        .user-name {
            font-weight: 600;
        }

        .feedback-date {
            color: #777;
            font-size: 14px;
        }

        .feedback-content {
            margin-bottom: 15px;
            line-height: 1.5;
        }

        .feedback-rating {
            display: flex;
            margin-bottom: 15px;
        }

        .star {
            color: #FFD700;
            margin-right: 2px;
        }

        .feedback-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            font-size: 14px;
            color: #777;
        }

        .feedback-course {
            font-weight: 500;
        }

        .feedback-status {
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 12px;
        }

        .status-responded {
            background-color: rgba(76, 175, 80, 0.1);
            color: #4CAF50;
        }

        .status-pending {
            background-color: rgba(255, 152, 0, 0.1);
            color: #FF9800;
        }

        .feedback-actions {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }

        .pagination {
            display: flex;
            justify-content: center;
            margin-top: 20px;
        }

        .pagination button {
            padding: 8px 15px;
            margin: 0 5px;
            border-radius: 5px;
            border: 1px solid #ddd;
            background-color: white;
            cursor: pointer;
        }

        .pagination button.active {
            background-color: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            overflow-y: auto;
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 20px;
            border-radius: 10px;
            width: 600px;
            max-width: 90%;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .modal-header h2 {
            color: var(--primary-color);
        }

        .close {
            font-size: 24px;
            cursor: pointer;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }

        .form-group textarea {
            width: 100%;
            padding: 10px;
            border-radius: 5px;
            border: 1px solid #ddd;
            min-height: 100px;
            resize: vertical;
        }

        .form-actions {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            margin-top: 20px;
        }

        .summary-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .summary-card {
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .summary-card h3 {
            font-size: 14px;
            color: #666;
            margin-bottom: 10px;
        }

        .summary-card .value {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .summary-card .change {
            font-size: 12px;
        }

        .summary-card .positive {
            color: #4CAF50;
        }

        .summary-card .negative {
            color: #F44336;
        }
    </style>
    <script>
        // Mock data for feedback
        const mockFeedback = [
            { id: 1, userId: 2, userName: 'John Doe', courseId: 1, courseName: 'Web Development Bootcamp', rating: 5, message: 'Great course! I learned a lot and the instructor was very helpful.', status: 'Responded', response: 'Thank you for your feedback! We\'re glad you enjoyed the course.', createdAt: '2023-05-01', respondedAt: '2023-05-02' },
            { id: 2, userId: 3, userName: 'Jane Smith', courseId: 2, courseName: 'Data Science Fundamentals', rating: 4, message: 'Very informative course. Could use more practical examples.', status: 'Pending', response: '', createdAt: '2023-05-02', respondedAt: null },
            { id: 3, userId: 4, userName: 'Bob Johnson', courseId: 3, courseName: 'Mobile App Development', rating: 5, message: 'Excellent course! The instructor explained complex concepts in a simple way.', status: 'Responded', response: 'Thank you for your kind words! We\'re happy to hear you found the course helpful.', createdAt: '2023-05-03', respondedAt: '2023-05-04' },
            { id: 4, userId: 5, userName: 'Alice Williams', courseId: 4, courseName: 'UI/UX Design Principles', rating: 3, message: 'The course content was good, but the pace was too fast for beginners.', status: 'Pending', response: '', createdAt: '2023-05-04', respondedAt: null },
            { id: 5, userId: 6, userName: 'Charlie Brown', courseId: 5, courseName: 'Python Programming', rating: 5, message: 'One of the best programming courses I\'ve taken. Very comprehensive and well-structured.', status: 'Responded', response: 'We appreciate your feedback! We put a lot of effort into structuring our courses for optimal learning.', createdAt: '2023-05-05', respondedAt: '2023-05-06' },
            { id: 6, userId: 7, userName: 'David Miller', courseId: 1, courseName: 'Web Development Bootcamp', rating: 4, message: 'Good course overall. The projects were challenging but rewarding.', status: 'Pending', response: '', createdAt: '2023-05-06', respondedAt: null }
        ];
    </script>
</head>
<body>
    <div class="admin-container">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="logo">
                <img src="../images/icon/logo.png" alt="EduVerse Logo">
                <h2>EduVerse</h2>
            </div>
            <div class="menu">
                <ul>
                    <li>
                        <a href="dashboard.html">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>Dashboard</span>
                        </a>
                    </li>
                    <li>
                        <a href="users.html">
                            <i class="fas fa-users"></i>
                            <span>Users</span>
                        </a>
                    </li>
                    <li>
                        <a href="courses.html">
                            <i class="fas fa-book"></i>
                            <span>Courses</span>
                        </a>
                    </li>
                    <li>
                        <a href="payments.html">
                            <i class="fas fa-credit-card"></i>
                            <span>Payments</span>
                        </a>
                    </li>
                    <li class="active">
                        <a href="feedback.html">
                            <i class="fas fa-comments"></i>
                            <span>Feedback</span>
                        </a>
                    </li>
                    <li>
                        <a href="settings.html">
                            <i class="fas fa-cog"></i>
                            <span>Settings</span>
                        </a>
                    </li>
                </ul>
            </div>
            <div class="logout">
                <a href="#" id="admin-logout">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Logout</span>
                </a>
            </div>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Top Bar -->
            <div class="top-bar">
                <div class="toggle-menu">
                    <i class="fas fa-bars"></i>
                </div>
                <div class="search-box">
                    <input type="text" placeholder="Search...">
                    <i class="fas fa-search"></i>
                </div>
                <div class="user-info">
                    <div class="notifications">
                        <i class="fas fa-bell"></i>
                        <span class="badge">3</span>
                    </div>
                    <div class="profile">
                        <img src="../images/icon/user.png" alt="Admin" id="admin-avatar">
                        <span class="admin-name">Admin User</span>
                    </div>
                </div>
            </div>

            <!-- Feedback Content -->
            <div class="dashboard-content">
                <h1>Feedback Management</h1>

                <div class="summary-cards">
                    <div class="summary-card">
                        <h3>Total Feedback</h3>
                        <div class="value" id="total-feedback">0</div>
                        <div class="change positive">+3 this week</div>
                    </div>
                    <div class="summary-card">
                        <h3>Average Rating</h3>
                        <div class="value" id="average-rating">0.0</div>
                        <div class="change positive">+0.2 from last month</div>
                    </div>
                    <div class="summary-card">
                        <h3>Pending Responses</h3>
                        <div class="value" id="pending-responses">0</div>
                        <div class="change negative">+1 from yesterday</div>
                    </div>
                    <div class="summary-card">
                        <h3>Response Rate</h3>
                        <div class="value" id="response-rate">0%</div>
                        <div class="change positive">+5% from last month</div>
                    </div>
                </div>

                <div class="filter-section">
                    <div>
                        <button class="btn btn-primary" id="export-btn">
                            <i class="fas fa-download"></i> Export Feedback
                        </button>
                    </div>
                    <div style="display: flex; gap: 10px;">
                        <input type="text" class="search-input" id="search-feedback" placeholder="Search feedback...">
                        <select class="status-filter" id="status-filter">
                            <option value="">All Status</option>
                            <option value="Responded">Responded</option>
                            <option value="Pending">Pending</option>
                        </select>
                        <select class="rating-filter" id="rating-filter">
                            <option value="">All Ratings</option>
                            <option value="5">5 Stars</option>
                            <option value="4">4 Stars</option>
                            <option value="3">3 Stars</option>
                            <option value="2">2 Stars</option>
                            <option value="1">1 Star</option>
                        </select>
                    </div>
                </div>

                <div class="feedback-cards" id="feedback-cards">
                    <!-- Will be populated by JavaScript -->
                </div>

                <div class="pagination" id="pagination">
                    <!-- Will be populated by JavaScript -->
                </div>
            </div>
        </div>
    </div>

    <!-- Response Modal -->
    <div id="response-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Respond to Feedback</h2>
                <span class="close">&times;</span>
            </div>
            <div class="feedback-details" id="feedback-details">
                <!-- Will be populated by JavaScript -->
            </div>
            <div class="form-group">
                <label for="response-text">Your Response</label>
                <textarea id="response-text" placeholder="Type your response here..."></textarea>
            </div>
            <div class="form-actions">
                <button type="button" class="btn btn-secondary" id="cancel-btn">Cancel</button>
                <button type="button" class="btn btn-primary" id="submit-response-btn">Submit Response</button>
            </div>
        </div>
    </div>

    <script src="js/admin-navigation.js"></script>
    <script src="js/menu-fix.js"></script>
    <script src="js/feedback.js"></script>
</body>
</html>
