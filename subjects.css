/* Google Fonts */
@import url('https://fonts.googleapis.com/css?family=Montserrat:500&display=swap');
@import url('https://fonts.googleapis.com/css?family=Dancing+Script&display=swap');
@import url('https://fonts.googleapis.com/css?family=Open+Sans&display=swap');

/* Global Reset */
* {
	box-sizing: border-box;
	margin: 0;
	padding: 0;
}
html {
	scroll-behavior: smooth;
}
body {
	background: #fff;
	font-family: 'Open Sans', sans-serif;
}


/* Navbar */
nav {
	width: 100%;
	padding: 20px 50px;
	background: #dc3333;
	display: flex;
	justify-content: space-between;
	align-items: center;
	position: sticky;
	top: 0;
	box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
	z-index: 1000;
  }

  .active{
	color:#110303;
	font-size: 18px;
  }
  
  nav .logo img {
	width: 150px;
  }
  
  nav ul {
	display: flex;
	list-style: none;
  }
  
  nav ul li {
	margin: 0 20px;
  }
  
  nav ul li a {
	color: #222;
	text-decoration: none;
	font-weight: 600;
	transition: color 0.3s;
  }
  
  nav ul li a:hover {
	color: #dbd703;
  }

/* Title */
.title,
.title2 {
	margin-left: 50px;
	padding-top: 10px;
}
.title span {
	font-weight: 700;
	font-size: 60px;
	color: #2E3D49;
}
.title .shortdesc {
	font-size:25px;
	color: #2E3D49;
	margin-bottom: 50px;
}
.title2 span {
	font-weight: 700;
	font-size: 30px;
	color: #2E3D49;
}
.title2 .shortdesc2 {
	font-size: 15px;
	color: #2E3D49;
	margin-bottom: 10px;
}

/* Quick Links */
.course {
	display: grid;
	justify-content: center;
}
.cbox {
	display: inline-flex;
	flex-wrap: wrap;
	justify-content: center;
}
.cbox .det {
	height: 80px;
	margin: 10px;
	background: #fff;
	border-radius: 50px;
	cursor: pointer;
}
.cbox .det a {
	display: flex;
	align-items: center;
	justify-content: center;
	width: 100%;
	padding: 20px;
	border-radius: 50px;
	border: 1px solid #FA4B37;
	font-size: 30px;
	color: #272529;
	font-family: cursive;
	text-decoration: none;
}
.cbox .det a:hover {
	background: linear-gradient(to right, #FA4B37, #DF2771);
	color: #fff;
}
.inbt {
	padding: 150px 0 0 0;
	font-size: 50px;
	color: #2E3D49;
	margin: 100px 0 50px 0;
	text-align: center;
}

/* Courses */
.ccard,
.ccardbox {
	display: flex;
	flex-wrap: wrap;
	justify-content: center;
	align-items: center;
	cursor: pointer;
}
.dcard {
	margin: 10px;
	width: 400px;
	height: 550px;
	background: linear-gradient(to right, #FA4B37, #DF2771);
	border-radius: 10px;
	box-shadow: 0 0 20px rgba(0, 0, 0, 0.4);
	overflow: hidden;
}
.dcard .fpart {
	width: 400px;
	height: 500px;
}
.dcard .fpart img {
	width: 400px;
	height: 500px;
	transition: 0.8s ease;
}
.dcard:hover .fpart img {
	transform: scale(1.2);
}
.dcard .spart {
	padding: 10px;
	color: #fff;
	cursor: pointer;
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.dcard .spart {
	font-size: 20PX;
	color: #000;
}

/* Videos */
.ccardbox2 {
	display: flex;
	justify-content: center;
	flex-wrap: wrap;
}
.dcard2 {
	margin: 20px;
	width: 300px;
	height: 160px;
	background: linear-gradient(to right, #FA4B37, #DF2771);
	border-radius: 10px;
}
.dcard2 .fpart2 {
	height: 180px;
	background: #000;
	transform: translateY(-19px);
	border-top-right-radius: 100px;
	overflow: hidden;
	box-shadow: 0 0 20px rgba(0, 0, 0, 0.4);
}
.dcard2 .fpart2 img {
	width: 100%;
	height: 100%;
}
.dcard2:hover .fpart2 img {
	display: none;
}
.fpart2 iframe {
	width: 100%;
	height: 100%;
}
.dcard2 .tag {
	position: absolute;
	top: 10px;
	right: 10px;
	color: #fff;
}

/* Playlist Link */
.click-me {
	display: flex;
	justify-content: center;
}
.click-me a {
	color: #DF2771;
	padding: 10px;
	text-decoration: none;
	transition: 0.5s;
}
.click-me a:hover {
	background: #DF2771;
	color: #fff;
}

/* Footer */
/* === Footer === */
footer {
	background: #222;
	color: white;
	padding : 30px 1200px 100px 50px; /* top, right, bottom, left */
  }
  
  .footer-container {
	display: flex;
	justify-content: space-around;
  }
  
  .left-col img,
  .right-col img {
	margin-bottom: -5px;
  }
  
  .social-media a img {
	width: 30px;
	margin-right: 06px;
  }
  
 .info
 {
	padding: 0px 0px 0px 10px;
	font-size: 20px;
 }


/* Responsive Design */
@media screen and (max-width: 1366px) {
	.search {
		display: none;
		margin-bottom: 10px;
	}
}
@media screen and (max-width: 1000px) {
	.nav ul, .nav .search {
		display: none;
	}
	.nav #learned-logo {
		margin-left: 40%;
		transform: scale(1.5);
		transition: 1s ease;
	}
	.nav .switch-tab,
	.nav .check-box {
		visibility: visible;
	}
	.search {
		visibility: visible;
		margin: 30px 0 0 30px;
	}
}
@media screen and (max-width: 960px) {
	.footer-container {
		max-width: 600px;
	}
	.right-col,
	.left-col {
		width: 100%;
		text-align: center;
		margin-bottom: 60px;
	}
}
@media screen and (max-width: 700px) {
	footer .btn {
		width: 100%;
		margin: 20px 0 0 0;
	}
}
