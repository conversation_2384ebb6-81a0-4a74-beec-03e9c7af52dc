using EduVerse.Application.DTOs;
using EduVerse.Application.Interfaces;
using EduVerse.Core.Entities;
using EduVerse.Core.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace EduVerse.Infrastructure.Services
{
    /// <summary>
    /// Service for feedback
    /// </summary>
    public class FeedbackService : IFeedbackService
    {
        private readonly IFeedbackRepository _feedbackRepository;

        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="feedbackRepository">Feedback repository</param>
        public FeedbackService(IFeedbackRepository feedbackRepository)
        {
            _feedbackRepository = feedbackRepository;
        }

        /// <summary>
        /// Submit feedback
        /// </summary>
        /// <param name="feedbackDto">Feedback data</param>
        /// <returns>Submitted feedback</returns>
        public async Task<FeedbackDto> SubmitFeedbackAsync(FeedbackDto feedbackDto)
        {
            try
            {
                // Create a new feedback entity with all required fields
                var feedback = new Feedback
                {
                    Name = feedbackDto.Name,
                    Email = feedbackDto.Email,
                    Message = feedbackDto.Message,
                    // Set Response to empty string (not null) as required by the database
                    Response = "",
                    // Set ResponseDate to null by default
                    ResponseDate = null,
                    // Set CreatedAt to current UTC time
                    CreatedAt = DateTime.UtcNow,
                    // Do not store user ID
                    UserId = null
                };

                // Save the feedback to the database
                await _feedbackRepository.AddAsync(feedback);

                // Return the saved feedback as a DTO
                var result = MapFeedbackToDto(feedback);

                // Ensure Response is null in the returned DTO
                result.Response = null;

                return result;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error submitting feedback: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Submit feedback from frontend
        /// </summary>
        /// <param name="submitDto">Feedback submission data</param>
        /// <returns>Submitted feedback</returns>
        public async Task<FeedbackDto> SubmitFrontendFeedbackAsync(FeedbackSubmitDto submitDto)
        {
            try
            {
                // Create a new feedback entity with all required fields
                var feedback = new Feedback
                {
                    Name = submitDto.Name,
                    Email = submitDto.Email,
                    Message = submitDto.Message,
                    // Set Response to empty string (not null) as required by the database
                    Response = "",
                    // Set ResponseDate to null by default
                    ResponseDate = null,
                    // Set CreatedAt to current UTC time
                    CreatedAt = DateTime.UtcNow,
                    // Do not store user ID
                    UserId = null
                };

                // Save the feedback to the database
                await _feedbackRepository.AddAsync(feedback);

                // Return the saved feedback as a DTO
                var result = MapFeedbackToDto(feedback);

                // Ensure Response is null in the returned DTO
                result.Response = null;

                return result;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error submitting frontend feedback: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Get all feedback
        /// </summary>
        /// <returns>List of all feedback</returns>
        public async Task<IEnumerable<FeedbackDto>> GetAllFeedbacksAsync()
        {
            var feedbacks = await _feedbackRepository.GetAllAsync();
            return feedbacks.Select(MapFeedbackToDto);
        }

        /// <summary>
        /// Get recent feedback
        /// </summary>
        /// <param name="limit">Number of feedback items to return</param>
        /// <returns>List of recent feedback</returns>
        public async Task<IEnumerable<FeedbackDto>> GetRecentFeedbackAsync(int limit = 5)
        {
            var feedbacks = await _feedbackRepository.GetRecentFeedbackAsync(limit);
            return feedbacks.Select(MapFeedbackToDto);
        }

        /// <summary>
        /// Get all feedback with pagination
        /// </summary>
        /// <param name="page">Page number</param>
        /// <param name="limit">Items per page</param>
        /// <returns>Paginated list of feedback</returns>
        public async Task<PaginatedResponseDto<FeedbackDto>> GetAllFeedbackAsync(int page = 1, int limit = 10)
        {
            var (feedbacks, totalCount) = await _feedbackRepository.GetAllFeedbackPaginatedAsync(page, limit);

            var feedbackDtos = feedbacks.Select(MapFeedbackToDto).ToList();

            return new PaginatedResponseDto<FeedbackDto>
            {
                Items = feedbackDtos,
                TotalItems = totalCount,
                CurrentPage = page,
                PageSize = limit,
                TotalPages = (totalCount + limit - 1) / limit
            };
        }

        /// <summary>
        /// Respond to feedback
        /// </summary>
        /// <param name="id">Feedback ID</param>
        /// <param name="response">Response text</param>
        /// <returns>Result with updated feedback</returns>
        public async Task<ApiResponseDto<FeedbackDto>> RespondToFeedbackAsync(int id, string response)
        {
            var feedback = await _feedbackRepository.GetByIdAsync(id);
            if (feedback == null)
                return ApiResponseDto<FeedbackDto>.FailureResponse("Feedback not found");

            feedback.Response = response;
            // ResponseDate will be set in the AddTimestamps method in ApplicationDbContext

            await _feedbackRepository.UpdateAsync(feedback);

            return ApiResponseDto<FeedbackDto>.SuccessResponse(
                MapFeedbackToDto(feedback),
                "Response added successfully");
        }

        /// <summary>
        /// Map feedback entity to DTO
        /// </summary>
        /// <param name="feedback">Feedback entity</param>
        /// <returns>Feedback DTO</returns>
        private static FeedbackDto MapFeedbackToDto(Feedback feedback)
        {
            return new FeedbackDto
            {
                Id = feedback.Id,
                Name = feedback.Name,
                Email = feedback.Email,
                Message = feedback.Message,
                Response = feedback.Response, // This will be empty string for new feedback
                ResponseDate = feedback.ResponseDate,
                CreatedAt = feedback.CreatedAt
                // User ID property removed as per requirements
            };
        }
    }
}