using System;
using System.ComponentModel.DataAnnotations;

namespace EduVerse.Application.DTOs
{
    /// <summary>
    /// DTO for discussion reply entity
    /// </summary>
    public class DiscussionReplyDto
    {
        /// <summary>
        /// Reply ID
        /// </summary>
        public int Id { get; set; }
        
        /// <summary>
        /// Reply content
        /// </summary>
        public string Content { get; set; }
        
        /// <summary>
        /// Creation date
        /// </summary>
        public DateTime CreatedAt { get; set; }
        
        /// <summary>
        /// Last update date
        /// </summary>
        public DateTime? UpdatedAt { get; set; }
        
        /// <summary>
        /// User ID
        /// </summary>
        public int UserId { get; set; }
        
        /// <summary>
        /// User name
        /// </summary>
        public string UserName { get; set; }
        
        /// <summary>
        /// Discussion ID
        /// </summary>
        public int DiscussionId { get; set; }
        
        /// <summary>
        /// Is reply marked as answer
        /// </summary>
        public bool IsAnswer { get; set; }
        
        /// <summary>
        /// Parent reply ID
        /// </summary>
        public int? ParentReplyId { get; set; }
    }
    
    /// <summary>
    /// DTO for creating a discussion reply
    /// </summary>
    public class CreateDiscussionReplyDto
    {
        /// <summary>
        /// Reply content
        /// </summary>
        [Required]
        public string Content { get; set; }
        
        /// <summary>
        /// User ID
        /// </summary>
        [Required]
        public int UserId { get; set; }
        
        /// <summary>
        /// Parent reply ID
        /// </summary>
        public int? ParentReplyId { get; set; }
    }
    
    /// <summary>
    /// DTO for updating a discussion reply
    /// </summary>
    public class UpdateDiscussionReplyDto
    {
        /// <summary>
        /// Reply content
        /// </summary>
        public string Content { get; set; }
        
        /// <summary>
        /// Is reply marked as answer
        /// </summary>
        public bool? IsAnswer { get; set; }
    }
}
