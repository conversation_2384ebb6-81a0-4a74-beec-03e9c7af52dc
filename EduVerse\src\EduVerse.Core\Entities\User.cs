using System;
using System.Collections.Generic;

namespace EduVerse.Core.Entities
{
    /// <summary>
    /// User entity
    /// </summary>
    public class User
    {
        /// <summary>
        /// User ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// Full name
        /// </summary>
        public string FullName { get; set; }

        /// <summary>
        /// Email address
        /// </summary>
        public string Email { get; set; }

        /// <summary>
        /// Username
        /// </summary>
        public string Username { get; set; }

        /// <summary>
        /// Password hash
        /// </summary>
        public string PasswordHash { get; set; }

        /// <summary>
        /// Creation date
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// Last login date
        /// </summary>
        public DateTime? LastLogin { get; set; }

        /// <summary>
        /// User role (Student or Admin)
        /// </summary>
        public string Role { get; set; } = "Student";

        // Navigation properties
        /// <summary>
        /// User statistics
        /// </summary>
        public UserStatistics Statistics { get; set; }

        /// <summary>
        /// Quiz attempts
        /// </summary>
        public ICollection<QuizAttempt> QuizAttempts { get; set; }

        /// <summary>
        /// Enrollments
        /// </summary>
        public ICollection<Enrollment> Enrollments { get; set; }

        /// <summary>
        /// Payments
        /// </summary>
        public ICollection<Payment> Payments { get; set; }

        /// <summary>
        /// Feedback
        /// </summary>
        public ICollection<Feedback> Feedback { get; set; }

        /// <summary>
        /// Discussions created by the user
        /// </summary>
        public ICollection<Discussion> Discussions { get; set; }

        /// <summary>
        /// Discussion replies by the user
        /// </summary>
        public ICollection<DiscussionReply> DiscussionReplies { get; set; }
    }
}
