using System;
using System.IO;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using EduVerse.Infrastructure.Data;

namespace EduVerse.API.Scripts
{
    public class RunSqlScript
    {
        public static async Task RunAsync(string[] args)
        {
            var host = CreateHostBuilder(args).Build();

            using (var scope = host.Services.CreateScope())
            {
                var services = scope.ServiceProvider;
                try
                {
                    var dbContext = services.GetRequiredService<ApplicationDbContext>();
                    var scriptPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Scripts", "AddEnrollmentFields.sql");
                    var script = File.ReadAllText(scriptPath);
                    
                    Console.WriteLine("Executing SQL script...");
                    await dbContext.Database.ExecuteSqlRawAsync(script);
                    Console.WriteLine("SQL script executed successfully.");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"An error occurred while running the SQL script: {ex.Message}");
                }
            }
        }

        public static IHostBuilder CreateHostBuilder(string[] args) =>
            Host.CreateDefaultBuilder(args)
                .ConfigureServices((hostContext, services) =>
                {
                    var configuration = new ConfigurationBuilder()
                        .SetBasePath(Directory.GetCurrentDirectory())
                        .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                        .AddJsonFile($"appsettings.{hostContext.HostingEnvironment.EnvironmentName}.json", optional: true)
                        .AddEnvironmentVariables()
                        .Build();

                    services.AddDbContext<ApplicationDbContext>(options =>
                        options.UseMySql(
                            configuration.GetConnectionString("DefaultConnection"),
                            new MySqlServerVersion(new Version(8, 0, 21))
                        ));
                });
    }
}
