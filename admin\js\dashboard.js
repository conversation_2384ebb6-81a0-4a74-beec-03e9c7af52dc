// Dashboard JavaScript for EduVerse Admin Portal

// Mock admin login function for testing (duplicate from admin-auth.js)
function mockAdminLogin() {
    console.log('Using mock admin login in dashboard.js');

    // Create a mock admin user
    const mockAdminUser = {
        id: 1,
        username: 'admin',
        fullName: 'Admin User',
        email: '<EMAIL>',
        Role: 'Admin',
        role: 'Admin'
    };

    // Create a mock token
    const mockToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxIiwibmFtZSI6IkFkbWluIFVzZXIiLCJyb2xlIjoiQWRtaW4ifQ.8tat9AtQmHePmf';

    // Store in localStorage
    localStorage.setItem('user', JSON.stringify(mockAdminUser));
    localStorage.setItem('token', mockToken);

    console.log('Mock admin login completed in dashboard.js');
    return true;
}

document.addEventListener('DOMContentLoaded', function() {
    console.log('Dashboard DOM content loaded');

    // Check if we have admin credentials
    const user = JSON.parse(localStorage.getItem('user') || '{}');
    const token = localStorage.getItem('token');

    // If no token or user is not admin, use mock login
    if (!token || (user.role !== 'Admin' && user.Role !== 'Admin')) {
        console.log('No admin credentials found, using mock login');
        mockAdminLogin();
    } else {
        console.log('Admin credentials found:', user);
    }

    // Initialize dashboard without authentication check
    initDashboard();

    // Setup event listeners
    setupEventListeners();
});

// Initialize dashboard
async function initDashboard() {
    try {
        // Update admin name first (this doesn't require API calls)
        updateAdminInfo();

        try {
            // Try to load dashboard statistics
            await loadDashboardStats();
        } catch (error) {
            console.error('Error loading dashboard stats:', error);
            // Continue with other sections even if this fails
        }

       

        try {
            // Try to load recent data
            await loadRecentEnrollments();
            await loadRecentFeedback();
        } catch (error) {
            console.error('Error loading recent data:', error);
            // Continue with other sections even if this fails
        }
    } catch (error) {
        console.error('Error initializing dashboard:', error);
        // Don't show alert to avoid blocking the UI
        console.warn('Some dashboard data could not be loaded. The dashboard will continue to function with limited data.');
    }
}

// Load dashboard statistics
async function loadDashboardStats() {
    try {
        // Check if AdminDashboardService exists
        if (typeof AdminDashboardService === 'undefined' || !AdminDashboardService.getDashboardStats) {
            console.error('AdminDashboardService is not defined or missing getDashboardStats method');
            // Use default values
            document.getElementById('total-users').textContent = '0';
            document.getElementById('total-courses').textContent = '0';
            document.getElementById('total-revenue').textContent = '$0';
            document.getElementById('total-enrollments').textContent = '0';
            return;
        }

        const stats = await AdminDashboardService.getDashboardStats();

        // Update stats cards with safe access
        if (document.getElementById('total-users')) {
            document.getElementById('total-users').textContent = stats?.totalUsers || 0;
        }
        if (document.getElementById('total-courses')) {
            document.getElementById('total-courses').textContent = stats?.totalCourses || 0;
        }
        if (document.getElementById('total-revenue')) {
            document.getElementById('total-revenue').textContent = `$${stats?.totalRevenue || 0}`;
        }
        if (document.getElementById('total-enrollments')) {
            document.getElementById('total-enrollments').textContent = stats?.totalEnrollments || 0;
        }
    } catch (error) {
        console.error('Error loading dashboard stats:', error);
        // Use default values instead of throwing
        document.getElementById('total-users').textContent = '0';
        document.getElementById('total-courses').textContent = '0';
        document.getElementById('total-revenue').textContent = '$0';
        document.getElementById('total-enrollments').textContent = '0';
    }
}



// Load recent enrollments
async function loadRecentEnrollments() {
    try {
        const enrollments = await AdminDashboardService.getRecentEnrollments();
        const tableBody = document.getElementById('recent-enrollments-table');

        if (enrollments && enrollments.length > 0) {
            tableBody.innerHTML = '';

            enrollments.forEach(enrollment => {
                const row = document.createElement('tr');

                // Format date
                const enrollmentDate = new Date(enrollment.enrollmentDate);
                const formattedDate = enrollmentDate.toLocaleDateString();

                // Determine status class
                let statusClass = '';
                switch (enrollment.status.toLowerCase()) {
                    case 'completed':
                        statusClass = 'completed';
                        break;
                    case 'pending':
                        statusClass = 'pending';
                        break;
                    case 'failed':
                        statusClass = 'failed';
                        break;
                    default:
                        statusClass = '';
                }

                row.innerHTML = `
                    <td>${enrollment.userName}</td>
                    <td>${enrollment.courseName}</td>
                    <td>${formattedDate}</td>
                    <td>$${enrollment.amount}</td>
                    <td><span class="status ${statusClass}">${enrollment.status}</span></td>
                `;

                tableBody.appendChild(row);
            });
        } else {
            tableBody.innerHTML = '<tr><td colspan="5">No recent enrollments</td></tr>';
        }
    } catch (error) {
        console.error('Error loading recent enrollments:', error);
        throw error;
    }
}

// Load recent feedback
async function loadRecentFeedback() {
    try {
        const feedbackList = await AdminDashboardService.getRecentFeedback();
        const feedbackContainer = document.getElementById('recent-feedback-list');

        if (feedbackList && feedbackList.length > 0) {
            feedbackContainer.innerHTML = '';

            feedbackList.forEach(feedback => {
                // Format date
                const feedbackDate = new Date(feedback.createdAt);
                const formattedDate = feedbackDate.toLocaleDateString();

                const feedbackItem = document.createElement('div');
                feedbackItem.className = 'feedback-item';

                feedbackItem.innerHTML = `
                    <div class="feedback-header">
                        <div class="feedback-user">${feedback.name}</div>
                        <div class="feedback-date">${formattedDate}</div>
                    </div>
                    <div class="feedback-message">${feedback.message}</div>
                `;

                feedbackContainer.appendChild(feedbackItem);
            });
        } else {
            feedbackContainer.innerHTML = '<p>No recent feedback</p>';
        }
    } catch (error) {
        console.error('Error loading recent feedback:', error);
        throw error;
    }
}

// Update admin info
function updateAdminInfo() {
    const user = JSON.parse(localStorage.getItem('user') || '{}');
    const adminNameElement = document.querySelector('.admin-name');

    if (adminNameElement && user.fullName) {
        adminNameElement.textContent = user.fullName;
    }
}

// Setup event listeners
function setupEventListeners() {
    // Toggle sidebar
    const toggleMenu = document.querySelector('.toggle-menu');
    if (toggleMenu) {
        toggleMenu.addEventListener('click', function() {
            document.querySelector('.admin-container').classList.toggle('sidebar-collapsed');
        });
    }

    // Admin logout
    const logoutBtn = document.getElementById('admin-logout');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', function(event) {
            event.preventDefault();

            // Clear localStorage
            localStorage.removeItem('token');
            localStorage.removeItem('user');

            console.log('Admin logged out, redirecting to login page');

            // Redirect to admin login page
            window.location.href = '../admin-login.html';
        });
    }
}
