using AutoMapper;
using EduVerse.Application.Configuration;
using EduVerse.Application.DTOs;
using EduVerse.Application.DTOs.Admin;
using EduVerse.Application.DTOs.Payment;
using EduVerse.Application.Interfaces;
using EduVerse.Core.Entities;
using EduVerse.Core.Interfaces;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Razorpay.Api;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;

namespace EduVerse.Infrastructure.Services
{
    /// <summary>
    /// Service for handling payments through Razorpay
    /// </summary>
    public class PaymentService : IPaymentService
    {
        private readonly IPaymentRepository _paymentRepository;
        private readonly ICourseRepository _courseRepository;
        private readonly IUserRepository _userRepository;
        private readonly IEnrollmentRepository _enrollmentRepository;
        private readonly IMapper _mapper;
        private readonly ILogger<PaymentService> _logger;
        private readonly RazorpaySettings _razorpaySettings;
        private readonly RazorpayClient _razorpayClient;

        /// <summary>
        /// Constructor
        /// </summary>
        public PaymentService(
            IPaymentRepository paymentRepository,
            ICourseRepository courseRepository,
            IUserRepository userRepository,
            IEnrollmentRepository enrollmentRepository,
            IMapper mapper,
            ILogger<PaymentService> logger,
            IOptions<RazorpaySettings> razorpaySettings)
        {
            _paymentRepository = paymentRepository;
            _courseRepository = courseRepository;
            _userRepository = userRepository;
            _enrollmentRepository = enrollmentRepository;
            _mapper = mapper;
            _logger = logger;
            _razorpaySettings = razorpaySettings.Value;
            _razorpayClient = new RazorpayClient(_razorpaySettings.KeyId, _razorpaySettings.KeySecret);
        }

        /// <summary>
        /// Create a new payment order
        /// </summary>
        public async Task<ApiResponseDto<CreateOrderResponseDto>> CreateOrderAsync(CreateOrderRequestDto request)
        {
            try
            {
                // Validate request
                if (request == null)
                {
                    return new ApiResponseDto<CreateOrderResponseDto>
                    {
                        Success = false,
                        Message = "Request cannot be null"
                    };
                }

                // Get course details
                var course = await _courseRepository.GetByIdAsync(request.CourseId);
                if (course == null)
                {
                    return new ApiResponseDto<CreateOrderResponseDto>
                    {
                        Success = false,
                        Message = $"Course with ID {request.CourseId} not found"
                    };
                }

                // Get user details
                var user = await _userRepository.GetByIdAsync(request.UserId);
                if (user == null)
                {
                    return new ApiResponseDto<CreateOrderResponseDto>
                    {
                        Success = false,
                        Message = $"User with ID {request.UserId} not found"
                    };
                }

                // Check if user is already enrolled in the course
                try
                {
                    var existingEnrollments = await _enrollmentRepository.GetAllAsync();
                    var existingEnrollment = existingEnrollments
                        .FirstOrDefault(e => e.UserId == request.UserId && e.CourseId == request.CourseId);

                    if (existingEnrollment != null)
                    {
                        // Check if the enrollment is active
                        if (existingEnrollment.IsActive)
                        {
                            return new ApiResponseDto<CreateOrderResponseDto>
                            {
                                Success = false,
                                Message = "User is already enrolled in this course"
                            };
                        }
                    }
                }
                catch (Exception ex)
                {
                    // Log the error but continue with the payment process
                    _logger.LogWarning(ex, "Error checking existing enrollments, proceeding with payment");
                }

                // Convert amount to paise (smallest currency unit for INR)
                var amountInPaise = (int)(course.Price * 100);

                // Ensure minimum order amount (Razorpay requires minimum 100 paise / ₹1)
                const int MINIMUM_ORDER_AMOUNT = 100; // 100 paise = ₹1
                if (amountInPaise < MINIMUM_ORDER_AMOUNT)
                {
                    _logger.LogWarning($"Course price {course.Price} is below minimum required amount. Setting to minimum amount.");
                    amountInPaise = MINIMUM_ORDER_AMOUNT;
                }

                // Create order in Razorpay
                Dictionary<string, object> options = new Dictionary<string, object>
                {
                    { "amount", amountInPaise },
                    { "currency", _razorpaySettings.Currency },
                    { "receipt", $"rcpt_{Guid.NewGuid().ToString("N").Substring(0, 10)}" },
                    { "payment_capture", 1 },
                    { "notes", new Dictionary<string, string>
                        {
                            { "course_id", request.CourseId.ToString() },
                            { "user_id", request.UserId.ToString() },
                            { "course_name", course.Title }
                        }
                    }
                };

                // Create order
                Order order = _razorpayClient.Order.Create(options);
                string orderId = order["id"].ToString();

                // Create response
                var response = new CreateOrderResponseDto
                {
                    OrderId = orderId,
                    RazorpayKeyId = _razorpaySettings.KeyId,
                    Amount = amountInPaise,
                    Currency = _razorpaySettings.Currency,
                    CourseId = course.Id,
                    CourseName = course.Title,
                    CourseDescription = course.Description,
                    UserId = user.Id,
                    UserName = user.Username,
                    UserEmail = user.Email,
                    UserContact = ""
                };

                return new ApiResponseDto<CreateOrderResponseDto>
                {
                    Success = true,
                    Message = "Order created successfully",
                    Data = response
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating Razorpay order");
                return new ApiResponseDto<CreateOrderResponseDto>
                {
                    Success = false,
                    Message = $"Error creating order: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// Verify payment after completion
        /// </summary>
        public async Task<ApiResponseDto<PaymentVerificationResponseDto>> VerifyPaymentAsync(PaymentVerificationRequestDto request)
        {
            try
            {
                // Validate request
                if (request == null || string.IsNullOrEmpty(request.OrderId) ||
                    string.IsNullOrEmpty(request.PaymentId) || string.IsNullOrEmpty(request.Signature))
                {
                    return new ApiResponseDto<PaymentVerificationResponseDto>
                    {
                        Success = false,
                        Message = "Invalid payment verification request"
                    };
                }

                // Verify signature
                string generatedSignature = GenerateSignature(request.OrderId, request.PaymentId);
                bool isValidSignature = request.Signature.Equals(generatedSignature, StringComparison.OrdinalIgnoreCase);

                if (!isValidSignature)
                {
                    _logger.LogWarning("Invalid payment signature detected");
                    return new ApiResponseDto<PaymentVerificationResponseDto>
                    {
                        Success = false,
                        Message = "Payment verification failed: Invalid signature"
                    };
                }

                // Get course and user details
                var course = await _courseRepository.GetByIdAsync(request.CourseId);
                var user = await _userRepository.GetByIdAsync(request.UserId);

                if (course == null || user == null)
                {
                    return new ApiResponseDto<PaymentVerificationResponseDto>
                    {
                        Success = false,
                        Message = "Course or user not found"
                    };
                }

                // Create payment record
                var payment = new EduVerse.Core.Entities.Payment
                {
                    UserId = request.UserId,
                    CourseId = request.CourseId,
                    Amount = course.Price,
                    Date = DateTime.UtcNow,
                    PaymentMethod = "Razorpay",
                    Status = "completed",
                    TransactionId = request.PaymentId,
                    RazorpayOrderId = request.OrderId,
                    BillingAddress = ""
                };

                await _paymentRepository.AddAsync(payment);

                // Check if enrollment already exists
                var existingEnrollments = await _enrollmentRepository.GetAllAsync();
                var existingEnrollment = existingEnrollments
                    .FirstOrDefault(e => e.UserId == request.UserId && e.CourseId == request.CourseId);

                Enrollment enrollment;

                if (existingEnrollment != null)
                {
                    // Update existing enrollment
                    existingEnrollment.IsActive = true;
                    existingEnrollment.Status = "active";
                    existingEnrollment.PaymentId = payment.Id;
                    existingEnrollment.ProgressPercentage = 0;

                    await _enrollmentRepository.UpdateAsync(existingEnrollment);
                    enrollment = existingEnrollment;
                }
                else
                {
                    // Create new enrollment
                    enrollment = new Enrollment
                    {
                        UserId = request.UserId,
                        CourseId = request.CourseId,
                        EnrollmentDate = DateTime.UtcNow,
                        IsActive = true,
                        Status = "active",
                        PaymentId = payment.Id,
                        ProgressPercentage = 0
                    };

                    await _enrollmentRepository.AddAsync(enrollment);
                }

                // Create response
                var response = new PaymentVerificationResponseDto
                {
                    Success = true,
                    Message = "Payment verified successfully",
                    PaymentId = payment.Id,
                    OrderId = request.OrderId,
                    RazorpayPaymentId = request.PaymentId,
                    CourseId = course.Id,
                    CourseName = course.Title,
                    EnrollmentId = enrollment.Id
                };

                return new ApiResponseDto<PaymentVerificationResponseDto>
                {
                    Success = true,
                    Message = "Payment verified successfully",
                    Data = response
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error verifying payment");
                return new ApiResponseDto<PaymentVerificationResponseDto>
                {
                    Success = false,
                    Message = $"Error verifying payment: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// Handle Razorpay webhook events
        /// </summary>
        public async Task<ApiResponseDto<string>> HandleWebhookAsync(string payload, string signature)
        {
            try
            {
                // Verify webhook signature
                bool isValidSignature = VerifyWebhookSignature(payload, signature);
                if (!isValidSignature)
                {
                    _logger.LogWarning("Invalid webhook signature detected");
                    return new ApiResponseDto<string>
                    {
                        Success = false,
                        Message = "Invalid webhook signature"
                    };
                }

                // Parse payload
                var webhookData = JObject.Parse(payload);
                var eventType = webhookData["event"].ToString();

                _logger.LogInformation($"Received Razorpay webhook: {eventType}");

                // Handle different event types
                switch (eventType)
                {
                    case "payment.authorized":
                        await HandlePaymentAuthorized(webhookData);
                        break;
                    case "payment.failed":
                        await HandlePaymentFailed(webhookData);
                        break;
                    default:
                        _logger.LogInformation($"Unhandled webhook event type: {eventType}");
                        break;
                }

                return new ApiResponseDto<string>
                {
                    Success = true,
                    Message = "Webhook processed successfully",
                    Data = "OK"
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing webhook");
                return new ApiResponseDto<string>
                {
                    Success = false,
                    Message = $"Error processing webhook: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// Get payment details by ID
        /// </summary>
        public async Task<ApiResponseDto<PaymentDto>> GetPaymentByIdAsync(int id)
        {
            try
            {
                var payment = await _paymentRepository.GetPaymentByIdWithDetailsAsync(id);
                if (payment == null)
                {
                    return new ApiResponseDto<PaymentDto>
                    {
                        Success = false,
                        Message = $"Payment with ID {id} not found"
                    };
                }

                var paymentDto = _mapper.Map<PaymentDto>(payment);
                return new ApiResponseDto<PaymentDto>
                {
                    Success = true,
                    Data = paymentDto
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error getting payment with ID {id}");
                return new ApiResponseDto<PaymentDto>
                {
                    Success = false,
                    Message = $"Error getting payment: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// Get user's payment history
        /// </summary>
        public async Task<PaginatedResponseDto<PaymentDto>> GetUserPaymentsAsync(int userId, int page = 1, int limit = 10)
        {
            try
            {
                // Get all payments for the user
                var allPayments = await _paymentRepository.GetAllAsync();
                var payments = allPayments.Where(p => p.UserId == userId).ToList();
                var totalCount = payments.Count;

                // Apply pagination
                var paginatedPayments = payments
                    .OrderByDescending(p => p.Date)
                    .Skip((page - 1) * limit)
                    .Take(limit)
                    .ToList();

                // Map to DTOs
                var paymentDtos = _mapper.Map<List<PaymentDto>>(paginatedPayments);

                // Create paginated response
                var response = new PaginatedResponseDto<PaymentDto>
                {
                    Items = paymentDtos,
                    TotalItems = totalCount,
                    CurrentPage = page,
                    PageSize = limit,
                    TotalPages = (int)Math.Ceiling(totalCount / (double)limit)
                };

                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error getting payments for user {userId}");
                throw;
            }
        }

        #region Private Helper Methods

        /// <summary>
        /// Generate signature for payment verification
        /// </summary>
        private string GenerateSignature(string orderId, string paymentId)
        {
            string payload = $"{orderId}|{paymentId}";
            using (HMACSHA256 hmac = new HMACSHA256(Encoding.UTF8.GetBytes(_razorpaySettings.KeySecret)))
            {
                byte[] hash = hmac.ComputeHash(Encoding.UTF8.GetBytes(payload));
                return BitConverter.ToString(hash).Replace("-", "").ToLower();
            }
        }

        /// <summary>
        /// Verify webhook signature
        /// </summary>
        private bool VerifyWebhookSignature(string payload, string signature)
        {
            using (HMACSHA256 hmac = new HMACSHA256(Encoding.UTF8.GetBytes(_razorpaySettings.WebhookSecret)))
            {
                byte[] hash = hmac.ComputeHash(Encoding.UTF8.GetBytes(payload));
                string computedSignature = BitConverter.ToString(hash).Replace("-", "").ToLower();
                return signature.Equals(computedSignature, StringComparison.OrdinalIgnoreCase);
            }
        }

        /// <summary>
        /// Handle payment.authorized webhook event
        /// </summary>
        private async Task HandlePaymentAuthorized(JObject webhookData)
        {
            var paymentEntity = webhookData["payload"]["payment"]["entity"];
            var orderId = paymentEntity["order_id"].ToString();
            var paymentId = paymentEntity["id"].ToString();
            var amount = decimal.Parse(paymentEntity["amount"].ToString()) / 100; // Convert from paise to rupees

            // Get notes from the order to extract course and user IDs
            var notes = paymentEntity["notes"] as JObject;
            if (notes != null && notes.ContainsKey("course_id") && notes.ContainsKey("user_id"))
            {
                int courseId = int.Parse(notes["course_id"].ToString());
                int userId = int.Parse(notes["user_id"].ToString());

                // Check if payment already exists
                var allPayments = await _paymentRepository.GetAllAsync();
                var existingPayment = allPayments.FirstOrDefault(p => p.TransactionId == paymentId);

                if (existingPayment == null)
                {
                    // Create payment record
                    var payment = new EduVerse.Core.Entities.Payment
                    {
                        UserId = userId,
                        CourseId = courseId,
                        Amount = amount,
                        Date = DateTime.UtcNow,
                        PaymentMethod = "Razorpay",
                        Status = "completed",
                        TransactionId = paymentId,
                        RazorpayOrderId = orderId,
                        BillingAddress = ""
                    };

                    await _paymentRepository.AddAsync(payment);

                    // Check if enrollment already exists
                    var existingEnrollments = await _enrollmentRepository.GetAllAsync();
                    var existingEnrollment = existingEnrollments
                        .FirstOrDefault(e => e.UserId == userId && e.CourseId == courseId);

                    if (existingEnrollment != null)
                    {
                        // Update existing enrollment
                        existingEnrollment.IsActive = true;
                        existingEnrollment.Status = "active";
                        existingEnrollment.PaymentId = payment.Id;
                        existingEnrollment.ProgressPercentage = 0;

                        await _enrollmentRepository.UpdateAsync(existingEnrollment);
                    }
                    else
                    {
                        // Create new enrollment
                        var enrollment = new Enrollment
                        {
                            UserId = userId,
                            CourseId = courseId,
                            EnrollmentDate = DateTime.UtcNow,
                            IsActive = true,
                            Status = "active",
                            PaymentId = payment.Id,
                            ProgressPercentage = 0
                        };

                        await _enrollmentRepository.AddAsync(enrollment);
                    }
                }
            }
        }

        /// <summary>
        /// Handle payment.failed webhook event
        /// </summary>
        private async Task HandlePaymentFailed(JObject webhookData)
        {
            var paymentEntity = webhookData["payload"]["payment"]["entity"];
            var paymentId = paymentEntity["id"].ToString();

            // Check if payment exists in our system
            var allPayments = await _paymentRepository.GetAllAsync();
            var existingPayment = allPayments.FirstOrDefault(p => p.TransactionId == paymentId);

            if (existingPayment != null)
            {
                // Update payment status
                existingPayment.Status = "failed";
                await _paymentRepository.UpdateAsync(existingPayment);
            }
        }

        #endregion
    }
}
