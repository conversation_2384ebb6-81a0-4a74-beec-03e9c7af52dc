<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - EduVerse (Simple)</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
        }
        .admin-container {
            display: flex;
            min-height: 100vh;
        }
        .sidebar {
            width: 250px;
            background: linear-gradient(to bottom, #DF2771, #FA4B37);
            color: white;
            position: fixed;
            height: 100vh;
        }
        .logo {
            display: flex;
            align-items: center;
            padding: 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        .logo h2 {
            font-size: 20px;
            margin: 0;
            margin-left: 10px;
        }
        .menu {
            padding: 20px 0;
        }
        .menu ul {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        .menu li {
            margin-bottom: 5px;
        }
        .menu li.active {
            background-color: rgba(255, 255, 255, 0.1);
        }
        .menu a {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            color: white;
            text-decoration: none;
        }
        .menu a span {
            margin-left: 10px;
        }
        .main-content {
            flex: 1;
            margin-left: 250px;
        }
        .top-bar {
            height: 70px;
            background-color: white;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }
        .dashboard-content {
            padding: 20px;
        }
        .dashboard-content h1 {
            margin-bottom: 20px;
            color: #DF2771;
        }
        .stats-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .card {
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            display: flex;
            align-items: center;
        }
        .card-icon {
            width: 60px;
            height: 60px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            color: white;
            font-size: 24px;
        }
        .card-icon.users { background-color: #4CAF50; }
        .card-icon.courses { background-color: #2196F3; }
        .card-icon.revenue { background-color: #FF9800; }
        .card-icon.enrollments { background-color: #9C27B0; }
        .card-info h3 {
            font-size: 14px;
            color: #777;
            margin: 0 0 5px 0;
        }
        .card-info h2 {
            font-size: 24px;
            margin: 0 0 5px 0;
        }
        .card-info p {
            font-size: 12px;
            color: #777;
            margin: 0;
        }
        .positive { color: #4CAF50; }
    </style>
</head>
<body>
    <div class="admin-container">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="logo">
                <h2>EduVerse Admin</h2>
            </div>
            <div class="menu">
                <ul>
                    <li class="active">
                        <a href="dashboard-simple.html">
                            <span>Dashboard</span>
                        </a>
                    </li>
                    <li>
                        <a href="#">
                            <span>Courses</span>
                        </a>
                    </li>
                    <li>
                        <a href="#">
                            <span>Users</span>
                        </a>
                    </li>
                    <li>
                        <a href="#">
                            <span>Payments</span>
                        </a>
                    </li>
                </ul>
            </div>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Top Bar -->
            <div class="top-bar">
                <div>Admin Dashboard</div>
                <div>Admin User</div>
            </div>

            <!-- Dashboard Content -->
            <div class="dashboard-content">
                <h1>Dashboard</h1>
                <div class="stats-cards">
                    <div class="card">
                        <div class="card-icon users">👥</div>
                        <div class="card-info">
                            <h3>Total Users</h3>
                            <h2>120</h2>
                            <p><span class="positive">+5%</span> from last month</p>
                        </div>
                    </div>
                    <div class="card">
                        <div class="card-icon courses">📚</div>
                        <div class="card-info">
                            <h3>Total Courses</h3>
                            <h2>25</h2>
                            <p><span class="positive">+2%</span> from last month</p>
                        </div>
                    </div>
                    <div class="card">
                        <div class="card-icon revenue">💲</div>
                        <div class="card-info">
                            <h3>Total Revenue</h3>
                            <h2>$15,750</h2>
                            <p><span class="positive">+8%</span> from last month</p>
                        </div>
                    </div>
                    <div class="card">
                        <div class="card-icon enrollments">🎓</div>
                        <div class="card-info">
                            <h3>Enrollments</h3>
                            <h2>350</h2>
                            <p><span class="positive">+12%</span> from last month</p>
                        </div>
                    </div>
                </div>
                
                <div>
                    <h2>Debug Information</h2>
                    <p>This is a simplified dashboard that doesn't rely on external resources.</p>
                    <p>Current path: <span id="current-path"></span></p>
                    <p><a href="../admin-test.html">Go back to test page</a></p>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.getElementById('current-path').textContent = window.location.pathname;
    </script>
</body>
</html>
