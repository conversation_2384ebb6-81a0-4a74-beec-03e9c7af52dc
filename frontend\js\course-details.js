// Course Details JavaScript for EduVerse Learning Hub

document.addEventListener('DOMContentLoaded', function() {
    // Check authentication state
    updateAuthUI();

    // Load course details
    loadCourseDetails();

    // Setup logout functionality
    setupLogout();
});

// Update UI based on authentication state
function updateAuthUI() {
    const isAuthenticated = AuthService.isAuthenticated();
    const loginBtn = document.querySelector('.login-btn');
    const userInfo = document.querySelector('.user-info');

    if (loginBtn) {
        if (isAuthenticated) {
            // User is logged in
            loginBtn.style.display = 'none';

            // Show user info if element exists
            if (userInfo) {
                const user = JSON.parse(localStorage.getItem('user'));
                userInfo.style.display = 'block';
                userInfo.querySelector('.username').textContent = user.fullName || user.username;
            }
        } else {
            // User is not logged in
            loginBtn.style.display = 'block';

            // Hide user info if element exists
            if (userInfo) {
                userInfo.style.display = 'none';
            }
        }
    }
}

// Load course details from API
async function loadCourseDetails() {
    try {
        // Get course ID from URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        const courseId = urlParams.get('id');

        // If no course ID in URL, check localStorage
        const storedCourseId = localStorage.getItem('currentCourseId');

        // Use URL param or stored ID
        const id = courseId || storedCourseId;

        if (!id) {
            console.error('No course ID found');
            return;
        }

        // Fetch course details from API
        const course = await CourseService.getCourseById(id);

        if (course) {
            // Update page title
            document.title = `${course.title} - EduVerse`;

            // Update course title
            const titleElement = document.querySelector('.title span');
            if (titleElement) {
                titleElement.textContent = course.title;
            }

            // Update course description
            const descElement = document.querySelector('.shortdesc p');
            if (descElement) {
                descElement.textContent = course.description;
            }

            // Update video URL if it exists
            const videoFrame = document.querySelector('iframe');
            if (videoFrame && course.videoUrl) {
                videoFrame.src = course.videoUrl;
            }

            // Add enroll button if course has a price
            if (course.price > 0) {
                const enrollButton = document.createElement('button');
                enrollButton.className = 'enroll-btn';
                enrollButton.textContent = `Enroll Now - ₹${course.price.toFixed(2)}`;
                enrollButton.addEventListener('click', function() {
                    // Redirect to payment page
                    window.location.href = `payment.html?courseId=${id}`;
                });

                // Find a suitable place to add the button
                const shortdescElement = document.querySelector('.shortdesc');
                if (shortdescElement) {
                    shortdescElement.appendChild(enrollButton);
                }
            }

            // Load quizzes for this course
            loadQuizzesForCourse(id);
        }
    } catch (error) {
        console.error('Error loading course details:', error);
    }
}

// Load quizzes for course
async function loadQuizzesForCourse(courseId) {
    try {
        // Fetch quizzes for the course
        const quizzes = await QuizService.getQuizzesByCourse(courseId);

        if (quizzes && quizzes.length > 0) {
            // Find quiz section
            const quizSection = document.querySelector('.quiz-section');

            if (quizSection) {
                // Create quiz list
                const quizList = document.createElement('div');
                quizList.className = 'quiz-list';

                // Add heading
                const heading = document.createElement('h3');
                heading.textContent = 'Available Quizzes';
                quizList.appendChild(heading);

                // Add quizzes
                const list = document.createElement('ul');

                quizzes.forEach(quiz => {
                    const item = document.createElement('li');
                    item.innerHTML = `
                        <a href="javascript:void(0)" onclick="startQuiz(${quiz.id})">
                            ${quiz.title} (${quiz.timeLimit} minutes)
                        </a>
                    `;
                    list.appendChild(item);
                });

                quizList.appendChild(list);
                quizSection.appendChild(quizList);
            }
        }
    } catch (error) {
        console.error('Error loading quizzes for course:', error);
    }
}

// Start quiz
async function startQuiz(quizId) {
    try {
        // Check if user is authenticated
        if (!AuthService.isAuthenticated()) {
            alert('Please login to take quizzes');
            window.location.href = 'login.html';
            return;
        }

        // Start a quiz attempt
        const attempt = await QuizService.startQuizAttempt(quizId);

        if (attempt) {
            // Store attempt ID in localStorage
            localStorage.setItem('currentAttemptId', attempt.id);
            localStorage.setItem('currentQuizId', quizId);

            // Redirect to quiz page
            window.location.href = 'quiz.html';
        } else {
            alert('Failed to start quiz. Please try again.');
        }
    } catch (error) {
        console.error('Error starting quiz:', error);
        alert('Failed to start quiz. Please try again later.');
    }
}

// Setup logout functionality
function setupLogout() {
    const logoutBtn = document.querySelector('.logout-btn');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', function(event) {
            event.preventDefault();
            try {
                if (typeof AuthService !== 'undefined') {
                    AuthService.logout();
                } else {
                    console.warn('AuthService is not defined, using mock logout');
                    localStorage.removeItem('token');
                    localStorage.removeItem('user');
                }
                updateAuthUI();
            } catch (error) {
                console.warn('Error in logout:', error);
                localStorage.removeItem('token');
                localStorage.removeItem('user');
            }
            window.location.href = 'index.html';
        });
    }
}

// Check if enroll buttons are visible
function checkEnrollButtons() {
    console.log('Checking enroll buttons visibility');

    // Check Java enroll button
    const javaEnrollBtn = document.getElementById('java-enroll-btn');
    if (javaEnrollBtn) {
        console.log('Java enroll button found:', javaEnrollBtn);
    } else {
        console.warn('Java enroll button not found');
    }

    // Check Python enroll button
    const pythonEnrollBtn = document.getElementById('python-enroll-btn');
    if (pythonEnrollBtn) {
        console.log('Python enroll button found:', pythonEnrollBtn);
    } else {
        console.warn('Python enroll button not found');
    }

    // Check all enroll buttons with class
    const allEnrollBtns = document.querySelectorAll('.enroll-btn');
    console.log(`Found ${allEnrollBtns.length} enroll buttons with class 'enroll-btn'`);
}

// Check enrollment status and show/hide video content
async function checkEnrollmentStatus() {
    console.log('Checking enrollment status');

    // Determine which course page we're on
    let courseId = null;
    let videoUrl = null;

    if (window.location.pathname.includes('java.html')) {
        courseId = 1;
        videoUrl = "https://www.youtube.com/embed/videoseries?list=PLfqMhTWNBTe3LtFWcvwpqTkUSlB32kJop";
    } else if (window.location.pathname.includes('python.html')) {
        courseId = 2;
        videoUrl = "https://www.youtube.com/embed/videoseries?list=PLGjplNEQ1it8-0CmoljS5yeV-GlKSUEt0";
    }

    if (!courseId) {
        console.warn('Could not determine course ID from page URL');
        return;
    }

    try {
        // Check if user is enrolled
        let isEnrolled = false;

        // Get current user ID
        const userJson = localStorage.getItem('user');
        let userId = null;

        if (userJson) {
            try {
                const user = JSON.parse(userJson);
                userId = user.id || user.Id;
                console.log(`Current user ID: ${userId}`);
            } catch (error) {
                console.error('Error parsing user JSON:', error);
            }
        } else {
            console.log('No user logged in');
        }

        if (userId) {
            // First check for the user-specific permanent enrollment flag
            const permanentEnrollment = localStorage.getItem(`user_${userId}_purchased_course_${courseId}`);
            if (permanentEnrollment === 'true') {
                console.log(`User ${userId} has permanently purchased course ${courseId}`);
                isEnrolled = true;
            } else if (typeof EnrollmentService !== 'undefined') {
                isEnrolled = await EnrollmentService.isEnrolled(courseId);
            } else {
                // Fallback to user-specific local storage
                const enrollmentsJson = localStorage.getItem(`user_${userId}_enrollments`);
                if (enrollmentsJson) {
                    const enrollments = JSON.parse(enrollmentsJson);
                    isEnrolled = enrollments.includes(courseId.toString());
                }
            }

            // If user is enrolled, make sure it's saved in user-specific enrollments for consistency
            if (isEnrolled) {
                try {
                    const enrollmentsJson = localStorage.getItem(`user_${userId}_enrollments`);
                    let enrollments = enrollmentsJson ? JSON.parse(enrollmentsJson) : [];

                    if (!enrollments.includes(courseId.toString())) {
                        enrollments.push(courseId.toString());
                        localStorage.setItem(`user_${userId}_enrollments`, JSON.stringify(enrollments));
                        console.log(`Added course ${courseId} to user ${userId}'s enrollments for consistency`);
                    }
                } catch (error) {
                    console.error('Error updating user-specific enrollments:', error);
                }
            }
        } else {
            // No user logged in, definitely not enrolled
            isEnrolled = false;
        }

        console.log(`User is ${isEnrolled ? '' : 'not '}enrolled in course ${courseId}`);

        // Get elements
        const videoContainer = document.getElementById('video-container');
        const lockedContent = document.getElementById('locked-content');
        const unlockBtn = document.getElementById('unlock-btn');

        if (!videoContainer || !lockedContent) {
            console.warn('Video container or locked content element not found');
            return;
        }

        if (isEnrolled) {
            // User is enrolled, show video
            videoContainer.innerHTML = `<iframe src="${videoUrl}" title="Course Playlist" allowfullscreen></iframe>`;
            videoContainer.style.display = 'block';
            lockedContent.style.display = 'none';

            // Hide the enroll buttons since user is already enrolled
            const javaEnrollBtn = document.getElementById('java-enroll-btn');
            const pythonEnrollBtn = document.getElementById('python-enroll-btn');

            // Hide the appropriate enroll button based on course ID
            if (courseId == 1 && javaEnrollBtn) {
                javaEnrollBtn.style.display = 'none';
                console.log('Hiding Java enroll button because user is already enrolled');
            } else if (courseId == 2 && pythonEnrollBtn) {
                pythonEnrollBtn.style.display = 'none';
                console.log('Hiding Python enroll button because user is already enrolled');
            }

            // Add a "View Course" button instead
            const courseInfoDiv = document.querySelector('.course-info');
            if (courseInfoDiv) {
                // Check if "View Course" button already exists
                if (!document.getElementById('view-course-btn')) {
                    const viewCourseBtn = document.createElement('button');
                    viewCourseBtn.id = 'view-course-btn';
                    viewCourseBtn.className = 'enroll-btn';
                    viewCourseBtn.style.backgroundColor = '#28a745';
                    viewCourseBtn.textContent = 'View Course';
                    viewCourseBtn.style.display = 'inline-block';

                    // Add the button to the course info div
                    courseInfoDiv.appendChild(viewCourseBtn);

                    // Scroll to video section when clicked
                    viewCourseBtn.addEventListener('click', function() {
                        videoContainer.scrollIntoView({ behavior: 'smooth' });
                    });
                }
            }
        } else {
            // User is not enrolled, show locked content
            videoContainer.innerHTML = '';
            videoContainer.style.display = 'none';
            lockedContent.style.display = 'block';

            // Add event listener to unlock button
            if (unlockBtn) {
                unlockBtn.addEventListener('click', function() {
                    // Store course ID
                    localStorage.setItem('currentCourseId', courseId.toString());

                    // Redirect to payment page
                    window.location.href = `payment.html?courseId=${courseId}`;
                });
            }
        }
    } catch (error) {
        console.error('Error checking enrollment status:', error);
    }
}
