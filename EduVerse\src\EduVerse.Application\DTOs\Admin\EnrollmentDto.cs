using System;

namespace EduVerse.Application.DTOs.Admin
{
    /// <summary>
    /// DTO for enrollment information
    /// </summary>
    public class EnrollmentDto
    {
        /// <summary>
        /// Enrollment ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// User ID
        /// </summary>
        public int UserId { get; set; }

        /// <summary>
        /// User name
        /// </summary>
        public string UserName { get; set; }

        /// <summary>
        /// Course ID
        /// </summary>
        public int CourseId { get; set; }

        /// <summary>
        /// Course name
        /// </summary>
        public string CourseName { get; set; }

        /// <summary>
        /// Enrollment date
        /// </summary>
        public DateTime EnrollmentDate { get; set; }

        /// <summary>
        /// Payment amount
        /// </summary>
        public decimal Amount { get; set; }

        /// <summary>
        /// Payment status
        /// </summary>
        public string Status { get; set; }
    }
}
