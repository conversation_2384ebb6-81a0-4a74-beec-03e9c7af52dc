// Quiz Service for EduVerse Learning Hub

// API base URL
const QUIZ_API_BASE_URL = 'http://localhost:5217'; // Match the API URL used in api-service.js

// Quiz Service
const QuizService = {
    // Get quizzes by course
    getQuizzesByCourse: async (courseId) => {
        try {
            // For demo purposes, return hardcoded quizzes for Java and Python courses
            if (courseId == 1) { // Java course
                return [
                    {
                        id: 101,
                        title: "Java Basics Quiz",
                        description: "Test your knowledge of Java basics",
                        timeLimit: 15,
                        courseId: 1
                    },
                    {
                        id: 102,
                        title: "Java OOP Concepts",
                        description: "Test your understanding of Object-Oriented Programming in Java",
                        timeLimit: 20,
                        courseId: 1
                    }
                ];
            } else if (courseId == 2) { // Python course
                return [
                    {
                        id: 201,
                        title: "Python Fundamentals",
                        description: "Test your knowledge of Python fundamentals",
                        timeLimit: 15,
                        courseId: 2
                    },
                    {
                        id: 202,
                        title: "Python Data Structures",
                        description: "Test your understanding of Python data structures",
                        timeLimit: 20,
                        courseId: 2
                    }
                ];
            }

            // For other courses, fetch from API
            const token = localStorage.getItem('token');
            if (!token) {
                throw new Error('Authentication required');
            }

            const response = await fetch(`${QUIZ_API_BASE_URL}/api/quizzes/course/${courseId}`, {
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });

            if (!response.ok) {
                throw new Error(`API Error (${response.status}): ${await response.text()}`);
            }

            const data = await response.json();
            return data.data;
        } catch (error) {
            console.error(`Error fetching quizzes for course ${courseId}:`, error);
            return [];
        }
    },

    // Get quiz by ID
    getQuizById: async (quizId) => {
        try {
            const token = localStorage.getItem('token');
            if (!token) {
                throw new Error('Authentication required');
            }

            const response = await fetch(`${QUIZ_API_BASE_URL}/api/quizzes/${quizId}`, {
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });

            if (!response.ok) {
                throw new Error(`API Error (${response.status}): ${await response.text()}`);
            }

            const data = await response.json();
            return data.data;
        } catch (error) {
            console.error(`Error fetching quiz ${quizId}:`, error);
            return null;
        }
    },

    // Start quiz attempt
    startQuizAttempt: async (quizId) => {
        try {
            const token = localStorage.getItem('token');
            if (!token) {
                throw new Error('Authentication required');
            }

            const response = await fetch(`${QUIZ_API_BASE_URL}/api/quiz-attempts/start`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`
                },
                body: JSON.stringify({
                    quizId
                })
            });

            if (!response.ok) {
                throw new Error(`API Error (${response.status}): ${await response.text()}`);
            }

            const data = await response.json();
            return data.data;
        } catch (error) {
            console.error(`Error starting quiz attempt for quiz ${quizId}:`, error);
            return null;
        }
    },

    // Submit quiz attempt
    submitQuizAttempt: async (attemptId, answers) => {
        try {
            const token = localStorage.getItem('token');
            if (!token) {
                throw new Error('Authentication required');
            }

            const response = await fetch(`${QUIZ_API_BASE_URL}/api/quiz-attempts/${attemptId}/submit`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`
                },
                body: JSON.stringify({
                    answers
                })
            });

            if (!response.ok) {
                throw new Error(`API Error (${response.status}): ${await response.text()}`);
            }

            const data = await response.json();
            return data.data;
        } catch (error) {
            console.error(`Error submitting quiz attempt ${attemptId}:`, error);
            return null;
        }
    },

    // Get quiz attempt result
    getQuizAttemptResult: async (attemptId) => {
        try {
            const token = localStorage.getItem('token');
            if (!token) {
                throw new Error('Authentication required');
            }

            const response = await fetch(`${QUIZ_API_BASE_URL}/api/quiz-attempts/${attemptId}/result`, {
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });

            if (!response.ok) {
                throw new Error(`API Error (${response.status}): ${await response.text()}`);
            }

            const data = await response.json();
            return data.data;
        } catch (error) {
            console.error(`Error fetching result for quiz attempt ${attemptId}:`, error);
            return null;
        }
    }
};
