// Courses JavaScript for EduVerse Learning Hub

document.addEventListener('DOMContentLoaded', function() {
    // Check authentication state
    updateAuthUI();
    
    // Load courses
    loadCourses();
    
    // Load categories
    loadCategories();
    
    // Setup logout functionality
    setupLogout();
});

// Update UI based on authentication state
function updateAuthUI() {
    const isAuthenticated = AuthService.isAuthenticated();
    const loginBtn = document.querySelector('.login-btn');
    const userInfo = document.querySelector('.user-info');
    
    if (loginBtn) {
        if (isAuthenticated) {
            // User is logged in
            loginBtn.style.display = 'none';
            
            // Show user info if element exists
            if (userInfo) {
                const user = JSON.parse(localStorage.getItem('user'));
                userInfo.style.display = 'block';
                userInfo.querySelector('.username').textContent = user.fullName || user.username;
            }
        } else {
            // User is not logged in
            loginBtn.style.display = 'block';
            
            // Hide user info if element exists
            if (userInfo) {
                userInfo.style.display = 'none';
            }
        }
    }
}

// Load courses from API
async function loadCourses() {
    try {
        const coursesContainer = document.querySelector('.ccard');
        if (!coursesContainer) return;
        
        // Show loading state
        const loadingDiv = document.createElement('div');
        loadingDiv.className = 'loading';
        loadingDiv.innerHTML = '<p>Loading courses...</p>';
        coursesContainer.appendChild(loadingDiv);
        
        // Fetch courses from API
        const courses = await CourseService.getAllCourses();
        
        // Remove loading message
        coursesContainer.removeChild(loadingDiv);
        
        if (courses && courses.length > 0) {
            // Clear existing content
            coursesContainer.innerHTML = '';
            
            // Create course card boxes
            const ccardbox1 = document.createElement('div');
            ccardbox1.className = 'ccardbox';
            
            const ccardbox2 = document.createElement('div');
            ccardbox2.className = 'ccardbox';
            
            // Add courses to the container
            courses.forEach((course, index) => {
                const courseElement = createCourseElement(course);
                
                // Add first 4 courses to first box, rest to second box
                if (index < 4) {
                    ccardbox1.appendChild(courseElement);
                } else {
                    ccardbox2.appendChild(courseElement);
                }
            });
            
            // Add card boxes to container
            const centerElement = document.createElement('center');
            centerElement.appendChild(ccardbox1);
            centerElement.appendChild(document.createElement('br'));
            centerElement.appendChild(document.createElement('br'));
            centerElement.appendChild(document.createElement('br'));
            centerElement.appendChild(ccardbox2);
            
            coursesContainer.appendChild(centerElement);
        } else {
            coursesContainer.innerHTML = '<center><p>No courses available at the moment.</p></center>';
        }
    } catch (error) {
        console.error('Error loading courses:', error);
        const coursesContainer = document.querySelector('.ccard');
        if (coursesContainer) {
            coursesContainer.innerHTML = '<center><p>Failed to load courses. Please try again later.</p></center>';
        }
    }
}

// Create course element
function createCourseElement(course) {
    const courseDiv = document.createElement('div');
    courseDiv.className = 'dcard';
    
    // Determine the course page based on the course title
    let coursePage = 'javascript.html';
    if (course.title.toLowerCase().includes('java') && !course.title.toLowerCase().includes('javascript')) {
        coursePage = 'java.html';
    } else if (course.title.toLowerCase().includes('python')) {
        coursePage = 'python.html';
    } else if (course.title.toLowerCase().includes('c++')) {
        coursePage = 'c++.html';
    } else if (course.title.toLowerCase().includes('html') || course.title.toLowerCase().includes('css')) {
        coursePage = 'htcs.html';
    } else if (course.title.toLowerCase().includes('web')) {
        coursePage = 'web.html';
    } else if (course.title.toLowerCase().includes('data')) {
        coursePage = 'structure.html';
    } else if (course.title.toLowerCase().includes('algorithm')) {
        coursePage = 'algo.html';
    }
    
    courseDiv.innerHTML = `
        <div class="fpart"><a href="${coursePage}?id=${course.id}"><img src="http://localhost:5217${course.imageUrl || '/images/courses/default.jpg'}"></a></div>
        <div class="spart">${course.title}<img src="images/icon/right-arrow.png"></div>
    `;
    
    return courseDiv;
}

// Load categories from API
async function loadCategories() {
    try {
        const categoriesContainer = document.querySelector('.cbox');
        if (!categoriesContainer) return;
        
        // Fetch categories from API
        const categories = await CourseService.getAllCategories();
        
        if (categories && categories.length > 0) {
            // Clear existing content
            categoriesContainer.innerHTML = '';
            
            // Add categories to the container
            categories.forEach(category => {
                const categoryElement = document.createElement('div');
                categoryElement.className = 'det';
                categoryElement.innerHTML = `<a href="javascript:void(0)" onclick="filterByCategory(${category.id})">${category.name}</a>`;
                categoriesContainer.appendChild(categoryElement);
            });
        }
    } catch (error) {
        console.error('Error loading categories:', error);
    }
}

// Filter courses by category
async function filterByCategory(categoryId) {
    try {
        const coursesContainer = document.querySelector('.ccard');
        if (!coursesContainer) return;
        
        // Show loading state
        coursesContainer.innerHTML = '<center><p>Loading courses...</p></center>';
        
        // Fetch courses by category from API
        const courses = await CourseService.getCoursesByCategory(categoryId);
        
        if (courses && courses.length > 0) {
            // Clear existing content
            coursesContainer.innerHTML = '';
            
            // Create course card boxes
            const ccardbox1 = document.createElement('div');
            ccardbox1.className = 'ccardbox';
            
            const ccardbox2 = document.createElement('div');
            ccardbox2.className = 'ccardbox';
            
            // Add courses to the container
            courses.forEach((course, index) => {
                const courseElement = createCourseElement(course);
                
                // Add first 4 courses to first box, rest to second box
                if (index < 4) {
                    ccardbox1.appendChild(courseElement);
                } else {
                    ccardbox2.appendChild(courseElement);
                }
            });
            
            // Add card boxes to container
            const centerElement = document.createElement('center');
            centerElement.appendChild(ccardbox1);
            centerElement.appendChild(document.createElement('br'));
            centerElement.appendChild(document.createElement('br'));
            centerElement.appendChild(document.createElement('br'));
            centerElement.appendChild(ccardbox2);
            
            coursesContainer.appendChild(centerElement);
        } else {
            coursesContainer.innerHTML = '<center><p>No courses available in this category.</p></center>';
        }
    } catch (error) {
        console.error('Error loading courses by category:', error);
        const coursesContainer = document.querySelector('.ccard');
        if (coursesContainer) {
            coursesContainer.innerHTML = '<center><p>Failed to load courses. Please try again later.</p></center>';
        }
    }
}

// Setup logout functionality
function setupLogout() {
    const logoutBtn = document.querySelector('.logout-btn');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', function(event) {
            event.preventDefault();
            AuthService.logout();
            updateAuthUI();
            window.location.href = 'index.html';
        });
    }
}
