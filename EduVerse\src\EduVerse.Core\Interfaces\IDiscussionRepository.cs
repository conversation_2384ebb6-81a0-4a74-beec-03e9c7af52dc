using EduVerse.Core.Entities;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace EduVerse.Core.Interfaces
{
    /// <summary>
    /// Interface for discussion repository
    /// </summary>
    public interface IDiscussionRepository : IRepository<Discussion>
    {
        /// <summary>
        /// Get discussions by user ID
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>List of discussions</returns>
        Task<IEnumerable<Discussion>> GetByUserIdAsync(int userId);
        
        /// <summary>
        /// Get discussions by course ID
        /// </summary>
        /// <param name="courseId">Course ID</param>
        /// <returns>List of discussions</returns>
        Task<IEnumerable<Discussion>> GetByCourseIdAsync(int courseId);
        
        /// <summary>
        /// Get discussion with replies
        /// </summary>
        /// <param name="id">Discussion ID</param>
        /// <returns>Discussion with replies</returns>
        Task<Discussion> GetWithRepliesAsync(int id);
        
        /// <summary>
        /// Mark discussion as resolved
        /// </summary>
        /// <param name="id">Discussion ID</param>
        /// <returns>True if successful</returns>
        Task<bool> MarkAsResolvedAsync(int id);
    }
}
