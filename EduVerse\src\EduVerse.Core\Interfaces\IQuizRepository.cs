using EduVerse.Core.Entities;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace EduVerse.Core.Interfaces
{
    public interface IQuizRepository : IRepository<Quiz>
    {
        Task<Quiz> GetQuizWithQuestionsAsync(int quizId);
        Task<IReadOnlyList<Quiz>> GetQuizzesByCourseAsync(int courseId);
        Task<QuizAttempt> GetAttemptWithAnswersAsync(int attemptId);
        Task<IReadOnlyList<QuizAttempt>> GetUserAttemptsAsync(int userId);
    }
}
