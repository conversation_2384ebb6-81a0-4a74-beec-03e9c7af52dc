using EduVerse.Application.DTOs;
using EduVerse.Application.DTOs.Admin;
using EduVerse.Application.DTOs.Payment;
using EduVerse.Application.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.IO;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;

namespace EduVerse.API.Controllers
{
    /// <summary>
    /// Controller for payment operations
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    [Produces("application/json")]
    public class PaymentsController : ControllerBase
    {
        private readonly IPaymentService _paymentService;
        private readonly ILogger<PaymentsController> _logger;

        /// <summary>
        /// Constructor
        /// </summary>
        public PaymentsController(IPaymentService paymentService, ILogger<PaymentsController> logger)
        {
            _paymentService = paymentService;
            _logger = logger;
        }

        /// <summary>
        /// Create a new payment order
        /// </summary>
        /// <param name="request">Order creation request</param>
        /// <returns>Order creation response</returns>
        [HttpPost("create-order")]
        [Authorize]
        [ProducesResponseType(typeof(ApiResponseDto<CreateOrderResponseDto>), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<ActionResult<ApiResponseDto<CreateOrderResponseDto>>> CreateOrder(CreateOrderRequestDto request)
        {
            try
            {
                // Get user ID from token
                var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier);
                if (userIdClaim == null || !int.TryParse(userIdClaim.Value, out int userId))
                {
                    return Unauthorized(new ApiResponseDto<CreateOrderResponseDto>
                    {
                        Success = false,
                        Message = "User not authenticated"
                    });
                }

                // Set user ID from token
                request.UserId = userId;

                var result = await _paymentService.CreateOrderAsync(request);
                if (!result.Success)
                {
                    return BadRequest(result);
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating payment order");
                return StatusCode(500, new ApiResponseDto<CreateOrderResponseDto>
                {
                    Success = false,
                    Message = $"An error occurred: {ex.Message}"
                });
            }
        }

        /// <summary>
        /// Verify payment after completion
        /// </summary>
        /// <param name="request">Payment verification request</param>
        /// <returns>Payment verification response</returns>
        [HttpPost("verify")]
        [Authorize]
        [ProducesResponseType(typeof(ApiResponseDto<PaymentVerificationResponseDto>), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<ActionResult<ApiResponseDto<PaymentVerificationResponseDto>>> VerifyPayment(PaymentVerificationRequestDto request)
        {
            try
            {
                // Get user ID from token
                var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier);
                if (userIdClaim == null || !int.TryParse(userIdClaim.Value, out int userId))
                {
                    return Unauthorized(new ApiResponseDto<PaymentVerificationResponseDto>
                    {
                        Success = false,
                        Message = "User not authenticated"
                    });
                }

                // Set user ID from token
                request.UserId = userId;

                var result = await _paymentService.VerifyPaymentAsync(request);
                if (!result.Success)
                {
                    return BadRequest(result);
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error verifying payment");
                return StatusCode(500, new ApiResponseDto<PaymentVerificationResponseDto>
                {
                    Success = false,
                    Message = $"An error occurred: {ex.Message}"
                });
            }
        }

        /// <summary>
        /// Handle Razorpay webhook events
        /// </summary>
        /// <returns>Success response</returns>
        [HttpPost("webhook")]
        [ProducesResponseType(typeof(ApiResponseDto<string>), 200)]
        [ProducesResponseType(400)]
        public async Task<ActionResult<ApiResponseDto<string>>> HandleWebhook()
        {
            try
            {
                // Read request body
                using var reader = new StreamReader(Request.Body);
                var payload = await reader.ReadToEndAsync();

                // Get signature from header
                if (!Request.Headers.TryGetValue("X-Razorpay-Signature", out var signatureValues))
                {
                    return BadRequest(new ApiResponseDto<string>
                    {
                        Success = false,
                        Message = "Missing webhook signature"
                    });
                }

                var signature = signatureValues.FirstOrDefault();
                if (string.IsNullOrEmpty(signature))
                {
                    return BadRequest(new ApiResponseDto<string>
                    {
                        Success = false,
                        Message = "Invalid webhook signature"
                    });
                }

                var result = await _paymentService.HandleWebhookAsync(payload, signature);
                if (!result.Success)
                {
                    return BadRequest(result);
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error handling webhook");
                return StatusCode(500, new ApiResponseDto<string>
                {
                    Success = false,
                    Message = $"An error occurred: {ex.Message}"
                });
            }
        }

        /// <summary>
        /// Get user's payment history
        /// </summary>
        /// <param name="page">Page number</param>
        /// <param name="limit">Items per page</param>
        /// <returns>Paginated list of payments</returns>
        [HttpGet("history")]
        [Authorize]
        [ProducesResponseType(typeof(PaginatedResponseDto<PaymentDto>), 200)]
        [ProducesResponseType(401)]
        public async Task<ActionResult<PaginatedResponseDto<PaymentDto>>> GetPaymentHistory(
            [FromQuery] int page = 1,
            [FromQuery] int limit = 10)
        {
            try
            {
                // Get user ID from token
                var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier);
                if (userIdClaim == null || !int.TryParse(userIdClaim.Value, out int userId))
                {
                    return Unauthorized(new { success = false, message = "User not authenticated" });
                }

                var result = await _paymentService.GetUserPaymentsAsync(userId, page, limit);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting payment history");
                return StatusCode(500, new { success = false, message = $"An error occurred: {ex.Message}" });
            }
        }

        /// <summary>
        /// Get payment details by ID
        /// </summary>
        /// <param name="id">Payment ID</param>
        /// <returns>Payment details</returns>
        [HttpGet("{id}")]
        [Authorize]
        [ProducesResponseType(typeof(ApiResponseDto<PaymentDto>), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        [ProducesResponseType(404)]
        public async Task<ActionResult<ApiResponseDto<PaymentDto>>> GetPayment(int id)
        {
            try
            {
                // Get user ID from token
                var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier);
                if (userIdClaim == null || !int.TryParse(userIdClaim.Value, out int userId))
                {
                    return Unauthorized(new ApiResponseDto<PaymentDto>
                    {
                        Success = false,
                        Message = "User not authenticated"
                    });
                }

                var result = await _paymentService.GetPaymentByIdAsync(id);
                if (!result.Success)
                {
                    return NotFound(result);
                }

                // Check if payment belongs to the user
                if (result.Data.UserId != userId)
                {
                    return Unauthorized(new ApiResponseDto<PaymentDto>
                    {
                        Success = false,
                        Message = "You are not authorized to view this payment"
                    });
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error getting payment with ID {id}");
                return StatusCode(500, new ApiResponseDto<PaymentDto>
                {
                    Success = false,
                    Message = $"An error occurred: {ex.Message}"
                });
            }
        }
    }
}
