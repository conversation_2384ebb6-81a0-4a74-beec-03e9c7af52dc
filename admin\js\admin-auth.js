// Admin Authentication handling for EduVerse Learning Hub

// Define AuthService if it doesn't exist
if (typeof AuthService === 'undefined') {
    console.log('AuthService not found, creating a minimal version');
    const AuthService = {
        isAuthenticated: () => {
            return localStorage.getItem('token') !== null;
        },
        logout: () => {
            localStorage.removeItem('token');
            localStorage.removeItem('user');
        }
    };
    window.AuthService = AuthService;
}

// Admin Service extension
const AdminService = {
    // Login with admin credentials
    login: async (credentials) => {
        try {
            console.log('Checking API server status...');

            // First, check if the API server is running
            try {
                const healthCheck = await fetch(`${API_BASE_URL}/api/health`, {
                    method: 'GET',
                    mode: 'cors',
                    cache: 'no-cache'
                });
                console.log('API server health check:', healthCheck.status);
            } catch (e) {
                console.error('API server health check failed:', e);
                return {
                    success: false,
                    message: 'Cannot connect to the server. Please make sure the API server is running.'
                };
            }

            console.log('Sending login request to:', `${API_BASE_URL}/api/admin/login`);
            console.log('With credentials:', JSON.stringify(credentials));

            const response = await fetch(`${API_BASE_URL}/api/admin/login`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(credentials),
                mode: 'cors',
                cache: 'no-cache'
            });

            console.log('Response status:', response.status);
            console.log('Response headers:', [...response.headers.entries()]);

            if (!response.ok) {
                console.error('Login failed with status:', response.status);
                const errorText = await response.text();
                console.error('Error response:', errorText);
                try {
                    return JSON.parse(errorText);
                } catch (e) {
                    return { success: false, message: `Server error: ${response.status}` };
                }
            }

            const data = await response.json();
            console.log('Response data:', data);
            return data;
        } catch (error) {
            console.error('Admin login error:', error);
            return {
                success: false,
                message: `Network error: ${error.message}. Please make sure the API server is running at ${API_BASE_URL}.`
            };
        }
    },

    // Check if user is admin
    isAdmin: () => {
        const userJson = localStorage.getItem('user');
        console.log('User JSON from localStorage:', userJson);

        const user = JSON.parse(userJson || '{}');
        console.log('Parsed user object:', user);
        console.log('User role:', user.Role);

        // Check both lowercase and uppercase property names for compatibility
        const isAdmin = (user.Role === 'Admin' || user.role === 'Admin');
        console.log('Is admin?', isAdmin);

        return isAdmin;
    },

    // Verify admin authentication
    verifyAdminAuth: () => {
        try {
            console.log('Verifying admin authentication...');
            console.log('AuthService exists?', typeof AuthService !== 'undefined');

            // Check token directly
            const token = localStorage.getItem('token');
            console.log('Token exists?', token !== null);

            // Use our own isAuthenticated method if AuthService is not available
            const isAuthenticated = (typeof AuthService !== 'undefined' && AuthService.isAuthenticated)
                ? AuthService.isAuthenticated()
                : AdminService.isAuthenticated();

            console.log('Is authenticated?', isAuthenticated);

            const isAdmin = AdminService.isAdmin();
            console.log('Is admin?', isAdmin);

            if (!isAuthenticated || !isAdmin) {
                console.log('Not authenticated or not admin, redirecting to login');

                // For debugging, don't redirect yet
                // window.location.href = '../admin-login.html';

                // Instead, show what's in localStorage
                console.log('localStorage contents:');
                for (let i = 0; i < localStorage.length; i++) {
                    const key = localStorage.key(i);
                    console.log(`${key}: ${localStorage.getItem(key)}`);
                }

                // For testing: Comment out the redirect to allow dashboard to load anyway
                // window.location.href = '../admin-login.html';
                return false;
            }

            console.log('Admin authentication verified successfully');
            return true;
        } catch (error) {
            console.error('Error in verifyAdminAuth:', error);

            // For debugging, don't redirect yet
            console.log('Error details:', error.message, error.stack);

            // For testing: Comment out the redirect to allow dashboard to load anyway
            // window.location.href = '../admin-login.html';
            return false;
        }
    },

    // Check if user is authenticated
    isAuthenticated: () => {
        return localStorage.getItem('token') !== null;
    }
};

// Mock admin login function for testing
function mockAdminLogin() {
    console.log('Using mock admin login');

    // Create a mock admin user
    const mockAdminUser = {
        id: 1,
        username: 'admin',
        fullName: 'Admin User',
        email: '<EMAIL>',
        Role: 'Admin',
        role: 'Admin'
    };

    // Create a mock token
    const mockToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxIiwibmFtZSI6IkFkbWluIFVzZXIiLCJyb2xlIjoiQWRtaW4ifQ.8tat9AtQmHePmf';

    // Store in localStorage
    localStorage.setItem('user', JSON.stringify(mockAdminUser));
    localStorage.setItem('token', mockToken);

    console.log('Mock admin login completed');
    return true;
}

document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM content loaded');

    // For testing: Uncomment to use mock login
    // mockAdminLogin();

    // Check if AuthService exists and admin is already logged in
    if (typeof AuthService !== 'undefined' && AuthService.isAuthenticated && AuthService.isAuthenticated() && AdminService.isAdmin()) {
        console.log('Already logged in as admin, redirecting to dashboard');
        window.location.href = 'dashboard.html';
    }

    // Admin login form submission
    const adminLoginForm = document.getElementById('admin-login');
    if (adminLoginForm) {
        adminLoginForm.addEventListener('submit', async function(event) {
            event.preventDefault();

            const username = document.getElementById('admin-email').value;
            const password = document.getElementById('admin-password').value;

            try {
                // Show loading state
                document.querySelector('.submit-btn').textContent = 'Logging in...';

                console.log('Attempting login with:', username);

                // For testing: Use mock login instead of actual API call
                // Comment this out to use the real API
                if (username === 'admin' && password === 'Admin@123') {
                    console.log('Using mock login for admin');
                    mockAdminLogin();
                    console.log('Mock login successful');

                    // Redirect to admin dashboard with correct path
                    window.location.href = 'dashboard.html';
                    return;
                }

                // Real API call (uncomment when API is ready)
                const response = await AdminService.login({
                    username: username,
                    password: password
                });

                console.log('Login response:', response);

                if (response.success) {
                    // Store token and user info in localStorage
                    localStorage.setItem('token', response.token);
                    localStorage.setItem('user', JSON.stringify(response.user));

                    console.log('Login successful:', response.user);
                    console.log('User role:', response.user.Role);

                    // Redirect to admin dashboard with correct path
                    window.location.href = 'dashboard.html';
                } else {
                    console.error('Login failed:', response);
                    document.querySelector('.submit-btn').textContent = 'Login to Admin Portal';
                    alert(response.message || 'Admin login failed. Please check your credentials.');
                }
            } catch (error) {
                console.error('Admin login error:', error);
                document.querySelector('.submit-btn').textContent = 'Login to Admin Portal';
                alert('An error occurred during login. Please try again.');
            }
        });
    }
});
