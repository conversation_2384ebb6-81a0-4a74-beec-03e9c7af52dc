namespace EduVerse.Application.DTOs.Payment
{
    /// <summary>
    /// Response DTO for order creation
    /// </summary>
    public class CreateOrderResponseDto
    {
        /// <summary>
        /// Razorpay Order ID
        /// </summary>
        public string OrderId { get; set; }

        /// <summary>
        /// Razorpay Key ID (public key)
        /// </summary>
        public string RazorpayKeyId { get; set; }

        /// <summary>
        /// Amount in smallest currency unit (paise for INR)
        /// </summary>
        public int Amount { get; set; }

        /// <summary>
        /// Currency code
        /// </summary>
        public string Currency { get; set; }

        /// <summary>
        /// Course ID
        /// </summary>
        public int CourseId { get; set; }

        /// <summary>
        /// Course name
        /// </summary>
        public string CourseName { get; set; }

        /// <summary>
        /// Course description
        /// </summary>
        public string CourseDescription { get; set; }

        /// <summary>
        /// User ID
        /// </summary>
        public int UserId { get; set; }

        /// <summary>
        /// User name
        /// </summary>
        public string UserName { get; set; }

        /// <summary>
        /// User email
        /// </summary>
        public string UserEmail { get; set; }

        /// <summary>
        /// User contact number
        /// </summary>
        public string UserContact { get; set; }
    }
}
