using EduVerse.Application.DTOs;
using EduVerse.Application.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace EduVerse.API.Controllers
{
    /// <summary>
    /// Controller for discussion functionality
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class DiscussionController : ControllerBase
    {
        private readonly IDiscussionService _discussionService;

        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="discussionService">Discussion service</param>
        public DiscussionController(IDiscussionService discussionService)
        {
            _discussionService = discussionService;
        }

        /// <summary>
        /// Get all discussions
        /// </summary>
        /// <returns>List of discussions</returns>
        [HttpGet]
        [ProducesResponseType(typeof(IEnumerable<DiscussionDto>), 200)]
        public async Task<IActionResult> GetAll()
        {
            var discussions = await _discussionService.GetAllAsync();
            return Ok(discussions);
        }

        /// <summary>
        /// Get discussion by ID
        /// </summary>
        /// <param name="id">Discussion ID</param>
        /// <returns>Discussion</returns>
        [HttpGet("{id}")]
        [ProducesResponseType(typeof(DiscussionDto), 200)]
        [ProducesResponseType(404)]
        public async Task<IActionResult> GetById(int id)
        {
            var discussion = await _discussionService.GetByIdAsync(id);
            if (discussion == null)
                return NotFound();

            return Ok(discussion);
        }

        /// <summary>
        /// Get discussions by user ID
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>List of discussions</returns>
        [HttpGet("user/{userId}")]
        [ProducesResponseType(typeof(IEnumerable<DiscussionDto>), 200)]
        public async Task<IActionResult> GetByUserId(int userId)
        {
            var discussions = await _discussionService.GetByUserIdAsync(userId);
            return Ok(discussions);
        }

        /// <summary>
        /// Get discussions by course ID
        /// </summary>
        /// <param name="courseId">Course ID</param>
        /// <returns>List of discussions</returns>
        [HttpGet("course/{courseId}")]
        [ProducesResponseType(typeof(IEnumerable<DiscussionDto>), 200)]
        public async Task<IActionResult> GetByCourseId(int courseId)
        {
            var discussions = await _discussionService.GetByCourseIdAsync(courseId);
            return Ok(discussions);
        }

        /// <summary>
        /// Create a new discussion
        /// </summary>
        /// <param name="discussionDto">Discussion data</param>
        /// <returns>Created discussion</returns>
        [HttpPost]
        [ProducesResponseType(typeof(DiscussionDto), 201)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> Create([FromBody] CreateDiscussionDto discussionDto)
        {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            try
            {
                var discussion = await _discussionService.CreateAsync(discussionDto);
                return CreatedAtAction(nameof(GetById), new { id = discussion.Id }, discussion);
            }
            catch (System.ArgumentException ex)
            {
                return BadRequest(ex.Message);
            }
        }

        /// <summary>
        /// Update a discussion
        /// </summary>
        /// <param name="id">Discussion ID</param>
        /// <param name="discussionDto">Discussion data</param>
        /// <returns>Updated discussion</returns>
        [HttpPut("{id}")]
        [ProducesResponseType(typeof(DiscussionDto), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        public async Task<IActionResult> Update(int id, [FromBody] UpdateDiscussionDto discussionDto)
        {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            var discussion = await _discussionService.UpdateAsync(id, discussionDto);
            if (discussion == null)
                return NotFound();

            return Ok(discussion);
        }

        /// <summary>
        /// Delete a discussion
        /// </summary>
        /// <param name="id">Discussion ID</param>
        /// <returns>No content</returns>
        [HttpDelete("{id}")]
        [ProducesResponseType(204)]
        [ProducesResponseType(404)]
        public async Task<IActionResult> Delete(int id)
        {
            var result = await _discussionService.DeleteAsync(id);
            if (!result)
                return NotFound();

            return NoContent();
        }

        /// <summary>
        /// Mark discussion as resolved
        /// </summary>
        /// <param name="id">Discussion ID</param>
        /// <returns>No content</returns>
        [HttpPatch("{id}/resolve")]
        [ProducesResponseType(204)]
        [ProducesResponseType(404)]
        public async Task<IActionResult> MarkAsResolved(int id)
        {
            var result = await _discussionService.MarkAsResolvedAsync(id);
            if (!result)
                return NotFound();

            return NoContent();
        }

        /// <summary>
        /// Get replies for discussion
        /// </summary>
        /// <param name="discussionId">Discussion ID</param>
        /// <returns>List of replies</returns>
        [HttpGet("{discussionId}/replies")]
        [ProducesResponseType(typeof(IEnumerable<DiscussionReplyDto>), 200)]
        public async Task<IActionResult> GetReplies(int discussionId)
        {
            var replies = await _discussionService.GetRepliesAsync(discussionId);
            return Ok(replies);
        }

        /// <summary>
        /// Add reply to discussion
        /// </summary>
        /// <param name="discussionId">Discussion ID</param>
        /// <param name="replyDto">Reply data</param>
        /// <returns>Created reply</returns>
        [HttpPost("{discussionId}/replies")]
        [ProducesResponseType(typeof(DiscussionReplyDto), 201)]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        public async Task<IActionResult> AddReply(int discussionId, [FromBody] CreateDiscussionReplyDto replyDto)
        {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            try
            {
                var reply = await _discussionService.AddReplyAsync(discussionId, replyDto);
                return CreatedAtAction(nameof(GetReplies), new { discussionId }, reply);
            }
            catch (System.ArgumentException ex)
            {
                return BadRequest(ex.Message);
            }
        }

        /// <summary>
        /// Mark reply as answer
        /// </summary>
        /// <param name="replyId">Reply ID</param>
        /// <returns>No content</returns>
        [HttpPatch("replies/{replyId}/mark-as-answer")]
        [ProducesResponseType(204)]
        [ProducesResponseType(404)]
        public async Task<IActionResult> MarkReplyAsAnswer(int replyId)
        {
            var result = await _discussionService.MarkReplyAsAnswerAsync(replyId);
            if (!result)
                return NotFound();

            return NoContent();
        }
    }
}
