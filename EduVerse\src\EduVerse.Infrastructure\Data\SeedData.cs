using EduVerse.Core.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;

namespace EduVerse.Infrastructure.Data
{
    public static class SeedData
    {
        public static async Task InitializeAsync(IServiceProvider serviceProvider)
        {
            using var scope = serviceProvider.CreateScope();
            var services = scope.ServiceProvider;

            try
            {
                var context = services.GetRequiredService<ApplicationDbContext>();
                await context.Database.MigrateAsync();
                await SeedDatabaseAsync(context);
            }
            catch (Exception ex)
            {
                var logger = services.GetRequiredService<ILogger<ApplicationDbContext>>();
                logger.LogError(ex, "An error occurred while seeding the database.");
            }
        }

        private static async Task SeedDatabaseAsync(ApplicationDbContext context)
        {
            // Seed Course Categories
            if (!await context.CourseCategories.AnyAsync())
            {
                var categories = new List<CourseCategory>
                {
                    new CourseCategory { Name = "Programming Languages", Description = "Learn programming languages and concepts", ImageUrl = "/images/courses/programming.jpg" },
                    new CourseCategory { Name = "Web Development", Description = "Learn web development technologies", ImageUrl = "/images/courses/web.jpg" },
                    new CourseCategory { Name = "Data Structures", Description = "Learn data structures and algorithms", ImageUrl = "/images/courses/data.jpg" }
                };

                await context.CourseCategories.AddRangeAsync(categories);
                await context.SaveChangesAsync();
            }

            // Seed Courses
            if (!await context.Courses.AnyAsync())
            {
                var categories = await context.CourseCategories.ToListAsync();
                var programmingCategory = categories.FirstOrDefault(c => c.Name == "Programming Languages");
                var webCategory = categories.FirstOrDefault(c => c.Name == "Web Development");
                var dataCategory = categories.FirstOrDefault(c => c.Name == "Data Structures");

                var courses = new List<Course>
                {
                    new Course
                    {
                        Title = "Java Programming",
                        Description = "Learn Java programming language from scratch",
                        ImageUrl = "/images/courses/java-course.jpg",
                        VideoUrl = "https://www.youtube.com/embed/videoseries?list=PLfqMhTWNBTe3LtFWcvwpqTkUSlB32kJop",
                        Price = 199, // ₹199
                        Duration = 40,
                        CreatedAt = DateTime.UtcNow,
                        CategoryId = programmingCategory.Id
                    },
                    new Course
                    {
                        Title = "Python Programming",
                        Description = "Learn Python programming language from scratch",
                        ImageUrl = "/images/courses/python-course.png",
                        VideoUrl = "https://www.youtube.com/embed/videoseries?list=PLGjplNEQ1it8-0CmoljS5yeV-GlKSUEt0",
                        Price = 299, // ₹299
                        Duration = 35,
                        CreatedAt = DateTime.UtcNow,
                        CategoryId = programmingCategory.Id
                    },
                    new Course
                    {
                        Title = "C++ Programming",
                        Description = "Learn C++ programming language from scratch",
                        ImageUrl = "/images/courses/c-course.jpg",
                        VideoUrl = "https://www.youtube.com/embed/videoseries?list=PLfqMhTWNBTe0b2nM6JHVCnAkhQRGiZMSJ",
                        Price = 399, // ₹399
                        Duration = 45,
                        CreatedAt = DateTime.UtcNow,
                        CategoryId = programmingCategory.Id
                    },
                    new Course
                    {
                        Title = "HTML and CSS",
                        Description = "Learn HTML and CSS for web development",
                        ImageUrl = "/images/courses/html.jpg",
                        VideoUrl = "https://www.youtube.com/embed/videoseries?&list=PLwgFb6VsUj_mtXvKDupqdWB2JBiek8YPB",
                        Price = 249, // ₹249
                        Duration = 30,
                        CreatedAt = DateTime.UtcNow,
                        CategoryId = webCategory.Id
                    },
                    new Course
                    {
                        Title = "JavaScript",
                        Description = "Learn JavaScript for web development",
                        ImageUrl = "/images/courses/javascript.png",
                        VideoUrl = "https://www.youtube.com//embed/videoseries?&list=PLGjplNEQ1it_oTvuLRNqXfz_v_0pq6unW",
                        Price = 349, // ₹349
                        Duration = 35,
                        CreatedAt = DateTime.UtcNow,
                        CategoryId = webCategory.Id
                    },
                    new Course
                    {
                        Title = "Data Structures",
                        Description = "Learn data structures for efficient programming",
                        ImageUrl = "/images/courses/data-course.jpg",
                        VideoUrl = "https://www.youtube.com/embed/videoseries?list=PLu0W_9lII9ahIappRPN0MCAgtOu3lQjQi",
                        Price = 449, // ₹449
                        Duration = 40,
                        CreatedAt = DateTime.UtcNow,
                        CategoryId = dataCategory.Id
                    },
                    new Course
                    {
                        Title = "Algorithms",
                        Description = "Learn algorithms for problem solving",
                        ImageUrl = "/images/courses/algo-course.jpg",
                        VideoUrl = "https://www.youtube.com/embed/videoseries?&list=PLmXKhU9FNesQJ3rpOAFE6RTm-2u2diwKn",
                        Price = 499, // ₹499
                        Duration = 45,
                        CreatedAt = DateTime.UtcNow,
                        CategoryId = dataCategory.Id
                    }
                };

                await context.Courses.AddRangeAsync(courses);
                await context.SaveChangesAsync();
            }

            // Seed Admin User
            if (!await context.Users.AnyAsync(u => u.Role == "Admin"))
            {
                var adminUser = new User
                {
                    FullName = "Admin User",
                    Email = "<EMAIL>",
                    Username = "admin",
                    PasswordHash = HashPassword("Admin@123"),
                    CreatedAt = DateTime.UtcNow,
                    Role = "Admin",
                    Statistics = new UserStatistics
                    {
                        QuizzesCompleted = 0,
                        TotalScore = 0,
                        CoursesEnrolled = 0
                    }
                };

                await context.Users.AddAsync(adminUser);
                await context.SaveChangesAsync();
            }
        }

        private static string HashPassword(string password)
        {
            using (var sha256 = SHA256.Create())
            {
                var hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(password));
                return Convert.ToBase64String(hashedBytes);
            }
        }
    }
}
