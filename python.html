<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <link rel="shortcut icon" type="png" href="images/icon/favicon.png">
  <link rel="stylesheet" type="text/css" href="frontend/css/course-content.css">
  <link rel="stylesheet" type="text/css" href="frontend/css/course-quiz.css">
  <script src="https://code.jquery.com/jquery-3.2.1.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
  <script src="frontend/js/config.js"></script>
  <script src="frontend/js/api-service.js"></script>
  <script src="frontend/js/auth-service.js"></script>
  <script src="frontend/js/course-service.js"></script>
  <script src="frontend/js/quiz-service.js"></script>
  <script src="frontend/js/payment-service.js"></script>
  <script src="frontend/js/enrollment-service.js"></script>
  <script src="frontend/js/course-details.js"></script>
  <script src="frontend/js/course-quiz.js"></script>
  <script type="text/javascript" src="script.js"></script>
  <title>Python Tutorial</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background: linear-gradient(to right, #2c3e50, #3498db);
      color: #fff;
      min-height: 100vh;
    }

    header {
      background-color: rgba(0, 0, 0, 0.7);
      padding: 20px;
      text-align: center;
      box-shadow: 0 4px 8px rgba(0,0,0,0.3);
    }

    header h1 {
      font-size: 2.5rem;
      color: #f1c40f;
    }

    .container {
      padding: 30px 10%;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
    }

    .video-box {
      position: relative;
      width: 100%;
      padding-bottom: 56.25%; /* 16:9 Aspect Ratio */
      height: 0;
      box-shadow: 0 0 20px rgba(0,0,0,0.5);
      border-radius: 15px;
      overflow: hidden;
      margin-top: 30px;
    }

    .video-box iframe {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      border: none;
    }

    footer {
      margin-top: 50px;
      text-align: center;
      padding: 20px;
      background: rgba(0, 0, 0, 0.4);
      font-size: 0.9rem;
    }
  </style>
</head>
<body>
  <header>
    <h1>Python Tutorial Playlist</h1>
    <nav>
      <ul style="list-style: none; display: flex; justify-content: center; margin-top: 10px;">
        <li style="margin: 0 10px;"><a href="index.html" style="color: white; text-decoration: none;">HOME</a></li>
        <li style="margin: 0 10px;"><a href="computer_courses.html" style="color: white; text-decoration: none;">COURSES</a></li>
        <li style="margin: 0 10px;"><a class="login-btn" href="login.html" style="color: white; text-decoration: none;">LOGIN</a></li>
        <li class="user-info" style="display: none; margin: 0 10px;">
          <span class="username" style="color: #f1c40f;"></span>
          <a href="#" class="logout-btn" style="color: white; text-decoration: none; margin-left: 10px;">LOGOUT</a>
        </li>
      </ul>
    </nav>
  </header>
  <div class="container">
    <div class="title">
     <center> <span>Python Programming</span></center>
      <div class="shortdesc">
        <center><p>Learn Python programming language from scratch</p>
        <p style="margin-top: 10px; color: #f1c40f;">Instructor: Shradha Khapra</p></center>
      </div>
    </div>
    <div class="course-info" style="margin-top: 20px; text-align: center;">
      <div class="course-price" style="font-size: 1.5rem; margin-bottom: 15px;">
        <span>Price: </span>
        <span style="color: #f1c40f; font-weight: bold;">₹299</span>
      </div>
    </div>
    <div class="video-box" id="video-container">
      <!-- Video will be loaded here after checking enrollment status -->
    </div>

    <!-- Locked content message -->
    <div id="locked-content" style="display: none; text-align: center; padding: 40px 20px; background-color: #f8f9fa; border-radius: 8px; margin: 20px 0;">
      <img src="C:\newProject\images\icon\yt5.jpg" alt="Locked Content" style="width: 150px; margin-bottom: 20px;">
      <h3 style="color: #333; margin-bottom: 15px;">Pay to watch this Content</h3>
      <p style="color: #666; margin-bottom: 20px;">Please enroll in this course to access the video content.</p>
      <button id="unlock-btn" class="enroll-btn" style="background-color: #DF2771; color: white; border: none; padding: 12px 25px; border-radius: 5px; font-size: 1.1rem; cursor: pointer; transition: all 0.3s ease; box-shadow: 0 4px 8px rgba(0,0,0,0.2);">Enroll Now - ₹299</button>
    </div>
    <div class="quiz-section" style="margin-top: 30px; width: 100%; text-align: center;">
      <h2 style="color: #f1c40f; margin-bottom: 20px;">Test Your Knowledge</h2>
      <p style="color: #fff; margin-bottom: 20px;">Take the quiz to test your understanding of Python programming concepts. Score at least 70% to earn your certificate!</p>
      <!-- Test button and quiz containers will be added here by JavaScript -->
    </div>
  </div>
  <footer>
    EduVerse - Learning Hub
    <br>Gogte College of Commerce Belagavi
  </footer>

  <script>
    // Add event listener for the Enroll Now button
    document.addEventListener('DOMContentLoaded', function() {
      console.log('DOM content loaded for Python course page');

      // Check if enroll buttons are visible
      try {
        if (typeof checkEnrollButtons === 'function') {
          checkEnrollButtons();
        }

        // Add View Course button if user is enrolled
        if (typeof EnrollmentService !== 'undefined' && typeof EnrollmentService.addViewCourseButton === 'function') {
          EnrollmentService.addViewCourseButton(2);
        }
      } catch (error) {
        console.warn('Error checking enroll buttons:', error);
      }

      // Check enrollment status and show/hide video content
      try {
        // Get current user ID
        let userId = null;
        const userJson = localStorage.getItem('user');

        if (userJson) {
          try {
            const user = JSON.parse(userJson);
            userId = user.id || user.Id;
            console.log(`Current user ID: ${userId}`);
          } catch (error) {
            console.error('Error parsing user JSON:', error);
          }
        } else {
          console.log('No user logged in');
        }

        // Check if this course was just purchased by the current user
        let justPurchased = false;

        if (userId) {
          // Check user-specific flag first
          justPurchased = localStorage.getItem(`user_${userId}_just_purchased_course_2`) === 'true';

          if (justPurchased) {
            // Clear the user-specific flag
            localStorage.removeItem(`user_${userId}_just_purchased_course_2`);
          }
        }

        // For backward compatibility, also check the non-user-specific flag
        if (!justPurchased) {
          justPurchased = localStorage.getItem('just_purchased_course_2') === 'true';

          if (justPurchased) {
            // Clear the non-user-specific flag
            localStorage.removeItem('just_purchased_course_2');
          }
        }

        if (justPurchased && userId) {
          console.log('This course was just purchased by the current user, showing video content');

          // Make sure the course is in user-specific enrollments
          const enrollmentsJson = localStorage.getItem(`user_${userId}_enrollments`);
          let enrollments = enrollmentsJson ? JSON.parse(enrollmentsJson) : [];

          if (!enrollments.includes('2')) {
            enrollments.push('2');
            localStorage.setItem(`user_${userId}_enrollments`, JSON.stringify(enrollments));
          }

          // Set the user-specific permanent enrollment flag
          localStorage.setItem(`user_${userId}_purchased_course_2`, 'true');
          console.log(`Set user_${userId}_purchased_course_2 flag to true`);

          // Show video content
          const videoContainer = document.getElementById('video-container');
          const lockedContent = document.getElementById('locked-content');
          const pythonEnrollBtn = document.getElementById('python-enroll-btn');

          if (videoContainer && lockedContent) {
            videoContainer.innerHTML = `<iframe src="https://www.youtube.com/embed/videoseries?list=PLGjplNEQ1it8-0CmoljS5yeV-GlKSUEt0" title="Course Playlist" allowfullscreen></iframe>`;
            videoContainer.style.display = 'block';
            lockedContent.style.display = 'none';

            // Hide enroll button
            if (pythonEnrollBtn) {
              pythonEnrollBtn.style.display = 'none';
            }

            // Scroll to video
            videoContainer.scrollIntoView({ behavior: 'smooth' });
          }
        } else if (typeof checkEnrollmentStatus === 'function') {
          checkEnrollmentStatus();
        }
      } catch (error) {
        console.warn('Error checking enrollment status:', error);

        // Fallback to regular enrollment check
        if (typeof checkEnrollmentStatus === 'function') {
          checkEnrollmentStatus();
        }
      }

      // Update authentication UI
      try {
        if (typeof updateAuthUI === 'function') {
          updateAuthUI();
        }
      } catch (error) {
        console.warn('Error updating auth UI:', error);
      }

      // Setup Enroll Now button
      const enrollBtn = document.getElementById('python-enroll-btn');
      if (enrollBtn) {
        enrollBtn.addEventListener('click', function() {
          try {
            // Check if user is authenticated
            if (typeof AuthService !== 'undefined' && !AuthService.isAuthenticated()) {
              alert('Please login to enroll in this course');
              window.location.href = 'login.html?redirect=python.html';
              return;
            }

            // Store course ID (Python course ID = 2)
            localStorage.setItem('currentCourseId', '2');

            // Redirect to payment page
            window.location.href = 'payment.html?courseId=2';
          } catch (error) {
            console.error('Error in enroll button click handler:', error);
            // Fallback behavior
            localStorage.setItem('currentCourseId', '2');
            window.location.href = 'payment.html?courseId=2';
          }
        });
      }

      // Setup logout functionality
      try {
        if (typeof setupLogout === 'function') {
          setupLogout();
        }
      } catch (error) {
        console.warn('Error setting up logout:', error);
      }
    });
  </script>
</body>
</html>
