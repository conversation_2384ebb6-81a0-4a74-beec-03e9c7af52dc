/* Admin Portal Styles */
/* Use system fonts as fallback */
@font-face {
    font-family: 'Poppins';
    src: local('Poppin<PERSON>'), local('Segoe UI'), local('Arial'), local('sans-serif');
    font-weight: normal;
    font-style: normal;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Poppins', sans-serif;
}

:root {
    --primary-color: #DF2771;
    --secondary-color: #FA4B37;
    --text-color: #333;
    --light-text: #777;
    --bg-color: #f5f5f5;
    --card-bg: #fff;
    --sidebar-width: 250px;
    --header-height: 70px;
    --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

body {
    background-color: var(--bg-color);
    color: var(--text-color);
}

.admin-container {
    display: flex;
    min-height: 100vh;
}

/* Sidebar Styles */
.sidebar {
    width: var(--sidebar-width);
    background: linear-gradient(to bottom, var(--primary-color), var(--secondary-color));
    color: white;
    position: fixed;
    height: 100vh;
    transition: var(--transition);
    z-index: 1000;
}

.logo {
    display: flex;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.logo img {
    width: 40px;
    margin-right: 10px;
}

.logo h2 {
    font-size: 20px;
    font-weight: 600;
}

.menu {
    padding: 20px 0;
}

.menu ul {
    list-style: none;
}

.menu li {
    margin-bottom: 5px;
    border-radius: 5px;
    transition: var(--transition);
}

.menu li.active, .menu li:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.menu a {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    color: white;
    text-decoration: none;
    transition: var(--transition);
}

.menu a i {
    margin-right: 10px;
    font-size: 18px;
}

.logout {
    position: absolute;
    bottom: 20px;
    width: 100%;
    padding: 0 20px;
}

.logout a {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    color: white;
    text-decoration: none;
    border-radius: 5px;
    transition: var(--transition);
}

.logout a:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.logout a i {
    margin-right: 10px;
    font-size: 18px;
}

/* Main Content Styles */
.main-content {
    flex: 1;
    margin-left: var(--sidebar-width);
    transition: var(--transition);
}

/* Top Bar Styles */
.top-bar {
    height: var(--header-height);
    background-color: var(--card-bg);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    box-shadow: var(--shadow);
    position: sticky;
    top: 0;
    z-index: 100;
}

.toggle-menu i {
    font-size: 24px;
    cursor: pointer;
}

.search-box {
    display: flex;
    align-items: center;
    background-color: var(--bg-color);
    border-radius: 30px;
    padding: 5px 15px;
    width: 300px;
}

.search-box input {
    border: none;
    outline: none;
    background: transparent;
    width: 100%;
    padding: 5px;
}

.search-box i {
    color: var(--light-text);
}

.user-info {
    display: flex;
    align-items: center;
}

.notifications {
    position: relative;
    margin-right: 20px;
    cursor: pointer;
}

.notifications i {
    font-size: 20px;
}

.badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background-color: var(--primary-color);
    color: white;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
}

.profile {
    display: flex;
    align-items: center;
    cursor: pointer;
}

.profile img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
    margin-right: 10px;
}

/* Dashboard Content Styles */
.dashboard-content {
    padding: 20px;
}

.dashboard-content h1 {
    margin-bottom: 20px;
    color: var(--primary-color);
}

.stats-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.card {
    background-color: var(--card-bg);
    border-radius: 10px;
    padding: 20px;
    box-shadow: var(--shadow);
    display: flex;
    align-items: center;
}

.card-icon {
    width: 60px;
    height: 60px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
}

.card-icon i {
    font-size: 24px;
    color: white;
}

.card-icon.users {
    background-color: #4CAF50;
}

.card-icon.courses {
    background-color: #2196F3;
}

.card-icon.revenue {
    background-color: #FF9800;
}

.card-icon.enrollments {
    background-color: #9C27B0;
}

.card-info h3 {
    font-size: 14px;
    color: var(--light-text);
    margin-bottom: 5px;
}

.card-info h2 {
    font-size: 24px;
    margin-bottom: 5px;
}

.card-info p {
    font-size: 12px;
    color: var(--light-text);
}

.positive {
    color: #4CAF50;
}

.negative {
    color: #F44336;
}

.charts-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.chart-card {
    background-color: var(--card-bg);
    border-radius: 10px;
    padding: 20px;
    box-shadow: var(--shadow);
}

.chart-card h3 {
    margin-bottom: 15px;
    color: var(--text-color);
}

.recent-section {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
    gap: 20px;
}

.recent-enrollments, .recent-feedback {
    background-color: var(--card-bg);
    border-radius: 10px;
    padding: 20px;
    box-shadow: var(--shadow);
}

.recent-enrollments h3, .recent-feedback h3 {
    margin-bottom: 15px;
    color: var(--text-color);
}

table {
    width: 100%;
    border-collapse: collapse;
}

table th, table td {
    padding: 10px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

table th {
    font-weight: 600;
    color: var(--light-text);
}

.status {
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 12px;
}

.status.completed {
    background-color: rgba(76, 175, 80, 0.1);
    color: #4CAF50;
}

.status.pending {
    background-color: rgba(255, 152, 0, 0.1);
    color: #FF9800;
}

.status.failed {
    background-color: rgba(244, 67, 54, 0.1);
    color: #F44336;
}

.feedback-item {
    padding: 15px 0;
    border-bottom: 1px solid #eee;
}

.feedback-item:last-child {
    border-bottom: none;
}

.feedback-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 5px;
}

.feedback-user {
    font-weight: 600;
}

.feedback-date {
    font-size: 12px;
    color: var(--light-text);
}

.feedback-message {
    color: var(--text-color);
    font-size: 14px;
}

/* Responsive Styles */
@media (max-width: 992px) {
    .sidebar {
        width: 70px;
    }

    .sidebar .logo h2,
    .sidebar .menu a span,
    .sidebar .logout a span {
        display: none;
    }

    .main-content {
        margin-left: 70px;
    }

    .charts-container,
    .recent-section {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 576px) {
    .stats-cards {
        grid-template-columns: 1fr;
    }

    .search-box {
        display: none;
    }
}
