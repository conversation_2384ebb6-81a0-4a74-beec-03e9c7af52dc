using System.Collections.Generic;

namespace EduVerse.Application.DTOs.Admin
{
    /// <summary>
    /// DTO for admin courses response
    /// </summary>
    public class AdminCoursesResponseDto
    {
        /// <summary>
        /// List of courses
        /// </summary>
        public List<AdminCourseDto> Courses { get; set; }

        /// <summary>
        /// Total number of pages
        /// </summary>
        public int TotalPages { get; set; }

        /// <summary>
        /// Current page
        /// </summary>
        public int CurrentPage { get; set; }

        /// <summary>
        /// Total number of courses
        /// </summary>
        public int TotalCourses { get; set; }
    }

    /// <summary>
    /// DTO for admin course details
    /// </summary>
    public class AdminCourseDto
    {
        /// <summary>
        /// Course ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// Course title
        /// </summary>
        public string Title { get; set; }

        /// <summary>
        /// Course description
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// Category ID
        /// </summary>
        public int CategoryId { get; set; }

        /// <summary>
        /// Category name
        /// </summary>
        public string CategoryName { get; set; }

        /// <summary>
        /// Course price
        /// </summary>
        public decimal Price { get; set; }

        /// <summary>
        /// Course duration in hours
        /// </summary>
        public int Duration { get; set; }

        /// <summary>
        /// Course status (active, draft, archived)
        /// </summary>
        public string Status { get; set; }

        /// <summary>
        /// Course image URL
        /// </summary>
        public string ImageUrl { get; set; }

        /// <summary>
        /// Number of enrollments
        /// </summary>
        public int Enrollments { get; set; }
    }
}
