/* Discussion Page Styles for EduVerse Learning Hub */

/* Main Container */
.discussion-container {
    max-width: 1400px;
    margin: 120px auto 50px;
    padding: 20px;
    background-color: #f9f9f9;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.discussion-header {
    text-align: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #e0e0e0;
}

.discussion-header h1 {
    color: #DF2771;
    font-size: 2.5rem;
    margin-bottom: 10px;
}

.discussion-header p {
    color: #555;
    font-size: 1.1rem;
}

/* Discussion Content Layout */
.discussion-content {
    display: flex;
    min-height: 600px;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}

/* Tutors Sidebar */
.tutors-sidebar {
    width: 300px;
    background-color: #f0f0f0;
    border-right: 1px solid #e0e0e0;
    overflow-y: auto;
}

.tutors-sidebar h2 {
    padding: 20px;
    margin: 0;
    background-color: #DF2771;
    color: white;
    font-size: 1.3rem;
}

.tutor-list {
    padding: 10px;
}

.tutor {
    display: flex;
    align-items: center;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.tutor:hover {
    background-color: #e9e9e9;
}

.tutor.active {
    background-color: #f8e6ee;
    border-left: 4px solid #DF2771;
}

.tutor img {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    margin-right: 15px;
    object-fit: cover;
}

.tutor-info h3 {
    margin: 0;
    font-size: 1.1rem;
    color: #333;
}

.tutor-info p {
    margin: 5px 0;
    font-size: 0.9rem;
    color: #666;
}

.status {
    display: inline-block;
    font-size: 0.8rem;
    padding: 2px 8px;
    border-radius: 10px;
}

.status.online {
    background-color: #e7f9e7;
    color: #28a745;
}

.status.away {
    background-color: #fff3cd;
    color: #ffc107;
}

/* Chat Area */
.chat-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    background-color: white;
}

.chat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #e0e0e0;
}

.chat-header-info {
    display: flex;
    align-items: center;
}

.chat-header-info img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-right: 15px;
}

.chat-header-info h3 {
    margin: 0;
    font-size: 1.2rem;
    color: #333;
}

.chat-header-info p {
    margin: 5px 0 0;
    font-size: 0.9rem;
    color: #666;
}

.chat-header-actions button {
    background-color: #f8d7da;
    color: #dc3545;
    border: none;
    padding: 8px 15px;
    border-radius: 5px;
    cursor: pointer;
    font-weight: bold;
    transition: all 0.3s ease;
}

.chat-header-actions button:hover {
    background-color: #f5c6cb;
}

.chat-messages {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    background-color: #f9f9f9;
}

.message-welcome {
    text-align: center;
    padding: 20px;
    background-color: #e7f3ff;
    border-radius: 10px;
    margin-bottom: 20px;
}

.message {
    margin-bottom: 15px;
    max-width: 70%;
}

.message.sent {
    margin-left: auto;
    background-color: #dcf8c6;
    border-radius: 10px 0 10px 10px;
    padding: 10px 15px;
}

.message.received {
    margin-right: auto;
    background-color: white;
    border-radius: 0 10px 10px 10px;
    padding: 10px 15px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.message p {
    margin: 0;
    color: #333;
}

.message .time {
    font-size: 0.7rem;
    color: #999;
    text-align: right;
    margin-top: 5px;
}

.chat-input {
    display: flex;
    padding: 15px;
    background-color: #f8f9fa;
    border-top: 1px solid #e0e0e0;
}

.chat-input textarea {
    flex: 1;
    padding: 12px 15px;
    border: 1px solid #ddd;
    border-radius: 20px;
    resize: none;
    height: 50px;
    font-family: inherit;
    font-size: 0.95rem;
}

.chat-input button {
    background-color: #DF2771;
    color: white;
    border: none;
    padding: 0 20px;
    border-radius: 20px;
    margin-left: 10px;
    cursor: pointer;
    font-weight: bold;
    transition: all 0.3s ease;
}

.chat-input button:hover {
    background-color: #c71d5b;
}

/* Information Panel */
.info-panel {
    width: 300px;
    background-color: #f8f9fa;
    border-left: 1px solid #e0e0e0;
    overflow-y: auto;
}

.tutor-profile, .faq-section {
    padding: 20px;
    border-bottom: 1px solid #e0e0e0;
}

.tutor-profile h2, .faq-section h2 {
    margin-top: 0;
    color: #DF2771;
    font-size: 1.3rem;
    margin-bottom: 20px;
}

.profile-content {
    text-align: center;
}

.profile-content img {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    margin-bottom: 15px;
    object-fit: cover;
    border: 3px solid #DF2771;
}

.profile-content h3 {
    margin: 0;
    font-size: 1.2rem;
    color: #333;
}

.profile-content p {
    margin: 5px 0 15px;
    font-size: 0.9rem;
    color: #666;
}

.profile-details {
    text-align: left;
    background-color: #f0f0f0;
    padding: 15px;
    border-radius: 8px;
}

.profile-details p {
    margin: 10px 0;
    font-size: 0.9rem;
}

.faq-item {
    margin-bottom: 20px;
}

.faq-item h3 {
    font-size: 1rem;
    color: #333;
    margin-bottom: 8px;
}

.faq-item p {
    font-size: 0.9rem;
    color: #666;
    line-height: 1.5;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .info-panel {
        display: none;
    }
}

@media (max-width: 768px) {
    .discussion-content {
        flex-direction: column;
    }
    
    .tutors-sidebar {
        width: 100%;
        max-height: 300px;
    }
    
    .chat-area {
        height: 500px;
    }
}
