// Payments History JavaScript for EduVerse Admin Portal

// Global variables
let currentPage = 1;
let totalPages = 1;
let currentPayments = [];
let selectedPaymentId = null;

document.addEventListener('DOMContentLoaded', function() {
    // Verify admin authentication
    if (!AdminService.verifyAdminAuth()) {
        return;
    }

    // Initialize payments page
    initPaymentsPage();

    // Setup event listeners
    setupEventListeners();
});

// Initialize payments page
async function initPaymentsPage() {
    try {
        // Load payments
        await loadPayments();
        
        // Load payment summary
        await loadPaymentSummary();
        
        // Update admin name
        updateAdminInfo();
    } catch (error) {
        console.error('Error initializing payments page:', error);
        alert('Failed to load payments data. Please try again later.');
    }
}

// Load payments
async function loadPayments() {
    try {
        // Show loading state
        const tableBody = document.getElementById('payments-table-body');
        tableBody.innerHTML = '<tr><td colspan="8" style="text-align: center;">Loading payments...</td></tr>';
        
        // Get filter values
        const statusFilter = document.getElementById('status-filter').value;
        const paymentMethodFilter = document.getElementById('payment-method').value;
        const dateRangeFilter = document.getElementById('date-range').value;
        const searchQuery = document.getElementById('payment-search').value;
        
        // Get date range if custom is selected
        let startDate = null;
        let endDate = null;
        if (dateRangeFilter === 'custom') {
            startDate = document.getElementById('start-date').value;
            endDate = document.getElementById('end-date').value;
        }
        
        // Fetch payments from API
        const response = await AdminPaymentService.getAllPayments(currentPage, 10);
        
        if (response && response.payments) {
            currentPayments = response.payments;
            totalPages = response.totalPages || 1;
            
            // Apply filters
            let filteredPayments = currentPayments;
            
            if (statusFilter) {
                filteredPayments = filteredPayments.filter(payment => 
                    payment.status.toLowerCase() === statusFilter.toLowerCase()
                );
            }
            
            if (paymentMethodFilter) {
                filteredPayments = filteredPayments.filter(payment => 
                    payment.paymentMethod.toLowerCase() === paymentMethodFilter.toLowerCase()
                );
            }
            
            if (searchQuery) {
                const query = searchQuery.toLowerCase();
                filteredPayments = filteredPayments.filter(payment => 
                    payment.userName.toLowerCase().includes(query) || 
                    payment.courseName.toLowerCase().includes(query) ||
                    payment.transactionId.toLowerCase().includes(query)
                );
            }
            
            // Apply date filters
            if (dateRangeFilter !== 'all') {
                const now = new Date();
                let filterStartDate;
                
                switch (dateRangeFilter) {
                    case 'today':
                        filterStartDate = new Date(now.setHours(0, 0, 0, 0));
                        break;
                    case 'week':
                        filterStartDate = new Date(now.setDate(now.getDate() - now.getDay()));
                        break;
                    case 'month':
                        filterStartDate = new Date(now.getFullYear(), now.getMonth(), 1);
                        break;
                    case 'year':
                        filterStartDate = new Date(now.getFullYear(), 0, 1);
                        break;
                    case 'custom':
                        if (startDate && endDate) {
                            filterStartDate = new Date(startDate);
                            const filterEndDate = new Date(endDate);
                            filterEndDate.setHours(23, 59, 59, 999);
                            
                            filteredPayments = filteredPayments.filter(payment => {
                                const paymentDate = new Date(payment.date);
                                return paymentDate >= filterStartDate && paymentDate <= filterEndDate;
                            });
                        }
                        break;
                }
                
                if (dateRangeFilter !== 'custom' && filterStartDate) {
                    filteredPayments = filteredPayments.filter(payment => {
                        const paymentDate = new Date(payment.date);
                        return paymentDate >= filterStartDate;
                    });
                }
            }
            
            // Update table
            updatePaymentsTable(filteredPayments);
            
            // Update pagination
            updatePagination();
        } else {
            tableBody.innerHTML = '<tr><td colspan="8" style="text-align: center;">No payments found</td></tr>';
        }
    } catch (error) {
        console.error('Error loading payments:', error);
        const tableBody = document.getElementById('payments-table-body');
        tableBody.innerHTML = '<tr><td colspan="8" style="text-align: center;">Failed to load payments. Please try again later.</td></tr>';
    }
}

// Load payment summary
async function loadPaymentSummary() {
    try {
        // This would typically come from an API call
        // For now, we'll calculate from the current payments
        
        let totalRevenue = 0;
        let totalTransactions = currentPayments.length;
        let successfulPayments = 0;
        let failedPayments = 0;
        
        currentPayments.forEach(payment => {
            if (payment.status.toLowerCase() === 'completed') {
                totalRevenue += payment.amount;
                successfulPayments++;
            } else if (payment.status.toLowerCase() === 'failed') {
                failedPayments++;
            }
        });
        
        // Update summary cards
        document.getElementById('total-revenue').textContent = `$${totalRevenue.toFixed(2)}`;
        document.getElementById('total-transactions').textContent = totalTransactions;
        document.getElementById('successful-payments').textContent = successfulPayments;
        document.getElementById('failed-payments').textContent = failedPayments;
    } catch (error) {
        console.error('Error loading payment summary:', error);
    }
}

// Update payments table
function updatePaymentsTable(payments) {
    const tableBody = document.getElementById('payments-table-body');
    
    if (payments && payments.length > 0) {
        tableBody.innerHTML = '';
        
        payments.forEach(payment => {
            const row = document.createElement('tr');
            
            // Format date
            const paymentDate = new Date(payment.date);
            const formattedDate = paymentDate.toLocaleDateString();
            
            // Determine status class
            let statusClass = '';
            switch (payment.status.toLowerCase()) {
                case 'completed':
                    statusClass = 'status-completed';
                    break;
                case 'pending':
                    statusClass = 'status-pending';
                    break;
                case 'failed':
                    statusClass = 'status-failed';
                    break;
                case 'refunded':
                    statusClass = 'status-refunded';
                    break;
                default:
                    statusClass = '';
            }
            
            row.innerHTML = `
                <td>${payment.id}</td>
                <td>${payment.userName}</td>
                <td>${payment.courseName}</td>
                <td>$${payment.amount.toFixed(2)}</td>
                <td>${formattedDate}</td>
                <td>${formatPaymentMethod(payment.paymentMethod)}</td>
                <td><span class="payment-status ${statusClass}">${payment.status}</span></td>
                <td class="payment-actions">
                    <div class="action-btn view-btn" data-id="${payment.id}" title="View Details">
                        <i class="fas fa-eye"></i>
                    </div>
                    <div class="action-btn receipt-btn" data-id="${payment.id}" title="View Receipt">
                        <i class="fas fa-file-invoice"></i>
                    </div>
                </td>
            `;
            
            tableBody.appendChild(row);
        });
        
        // Add event listeners to action buttons
        addActionButtonListeners();
    } else {
        tableBody.innerHTML = '<tr><td colspan="8" style="text-align: center;">No payments found</td></tr>';
    }
}

// Format payment method
function formatPaymentMethod(method) {
    switch (method.toLowerCase()) {
        case 'credit_card':
            return 'Credit Card';
        case 'paypal':
            return 'PayPal';
        case 'bank_transfer':
            return 'Bank Transfer';
        default:
            return method;
    }
}

// Add event listeners to action buttons
function addActionButtonListeners() {
    // View buttons
    const viewButtons = document.querySelectorAll('.view-btn');
    viewButtons.forEach(button => {
        button.addEventListener('click', function() {
            const paymentId = this.getAttribute('data-id');
            viewPaymentDetails(paymentId);
        });
    });
    
    // Receipt buttons
    const receiptButtons = document.querySelectorAll('.receipt-btn');
    receiptButtons.forEach(button => {
        button.addEventListener('click', function() {
            const paymentId = this.getAttribute('data-id');
            viewPaymentReceipt(paymentId);
        });
    });
}

// View payment details
function viewPaymentDetails(paymentId) {
    // Find payment data
    const payment = currentPayments.find(p => p.id == paymentId);
    
    if (payment) {
        // Set selected payment ID
        selectedPaymentId = paymentId;
        
        // Format date
        const paymentDate = new Date(payment.date);
        const formattedDate = paymentDate.toLocaleString();
        
        // Update modal with payment details
        document.getElementById('detail-id').textContent = payment.id;
        document.getElementById('detail-user').textContent = payment.userName;
        document.getElementById('detail-course').textContent = payment.courseName;
        document.getElementById('detail-amount').textContent = `$${payment.amount.toFixed(2)}`;
        document.getElementById('detail-date').textContent = formattedDate;
        document.getElementById('detail-method').textContent = formatPaymentMethod(payment.paymentMethod);
        document.getElementById('detail-status').textContent = payment.status;
        document.getElementById('detail-transaction').textContent = payment.transactionId;
        document.getElementById('detail-address').textContent = payment.billingAddress || 'N/A';
        
        // Set current status in dropdown
        document.getElementById('update-status').value = payment.status.toLowerCase();
        
        // Show modal
        document.getElementById('payment-modal').style.display = 'block';
    }
}

// View payment receipt
function viewPaymentReceipt(paymentId) {
    // This would typically open a receipt in a new window or download it
    alert(`Receipt for payment ${paymentId} would be displayed or downloaded here.`);
}

// Update pagination
function updatePagination() {
    const pageInfo = document.getElementById('page-info');
    const prevButton = document.getElementById('prev-page');
    const nextButton = document.getElementById('next-page');
    
    pageInfo.textContent = `Page ${currentPage} of ${totalPages}`;
    
    prevButton.disabled = currentPage === 1;
    nextButton.disabled = currentPage === totalPages;
}

// Update admin info
function updateAdminInfo() {
    const user = JSON.parse(localStorage.getItem('user') || '{}');
    const adminNameElement = document.querySelector('.admin-name');
    
    if (adminNameElement && user.fullName) {
        adminNameElement.textContent = user.fullName;
    }
}

// Setup event listeners
function setupEventListeners() {
    // Toggle sidebar
    const toggleMenu = document.querySelector('.toggle-menu');
    if (toggleMenu) {
        toggleMenu.addEventListener('click', function() {
            document.querySelector('.admin-container').classList.toggle('sidebar-collapsed');
        });
    }
    
    // Admin logout
    const logoutBtn = document.getElementById('admin-logout');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', function(event) {
            event.preventDefault();
            AuthService.logout();
            window.location.href = '../admin-login.html';
        });
    }
    
    // Date range filter
    const dateRangeFilter = document.getElementById('date-range');
    if (dateRangeFilter) {
        dateRangeFilter.addEventListener('change', function() {
            const customDateRange = document.getElementById('custom-date-range');
            if (this.value === 'custom') {
                customDateRange.style.display = 'flex';
            } else {
                customDateRange.style.display = 'none';
            }
        });
    }
    
    // Apply filters button
    const applyFiltersBtn = document.getElementById('apply-filters');
    if (applyFiltersBtn) {
        applyFiltersBtn.addEventListener('click', loadPayments);
    }
    
    // Search input
    const searchInput = document.getElementById('payment-search');
    if (searchInput) {
        searchInput.addEventListener('input', debounce(loadPayments, 500));
    }
    
    // Pagination buttons
    document.getElementById('prev-page').addEventListener('click', function() {
        if (currentPage > 1) {
            currentPage--;
            loadPayments();
        }
    });
    
    document.getElementById('next-page').addEventListener('click', function() {
        if (currentPage < totalPages) {
            currentPage++;
            loadPayments();
        }
    });
    
    // Export buttons
    document.getElementById('export-csv').addEventListener('click', function() {
        exportPayments('csv');
    });
    
    document.getElementById('export-pdf').addEventListener('click', function() {
        exportPayments('pdf');
    });
    
    // Close modal button
    const closeButton = document.querySelector('.close');
    if (closeButton) {
        closeButton.addEventListener('click', function() {
            document.getElementById('payment-modal').style.display = 'none';
            selectedPaymentId = null;
        });
    }
    
    // Update payment status button
    const updateStatusBtn = document.getElementById('update-payment-status');
    if (updateStatusBtn) {
        updateStatusBtn.addEventListener('click', async function() {
            if (selectedPaymentId) {
                const newStatus = document.getElementById('update-status').value;
                
                try {
                    const response = await AdminPaymentService.updatePaymentStatus(selectedPaymentId, newStatus);
                    
                    if (response.success) {
                        alert('Payment status updated successfully!');
                        
                        // Close modal and reload payments
                        document.getElementById('payment-modal').style.display = 'none';
                        selectedPaymentId = null;
                        await loadPayments();
                    } else {
                        alert(response.message || 'Failed to update payment status. Please try again.');
                    }
                } catch (error) {
                    console.error('Error updating payment status:', error);
                    alert('An error occurred while updating the payment status. Please try again.');
                }
            }
        });
    }
}

// Export payments
function exportPayments(format) {
    // This would typically call an API endpoint to generate and download the export
    alert(`Payments would be exported in ${format.toUpperCase()} format here.`);
}

// Debounce function for search input
function debounce(func, delay) {
    let timeout;
    return function() {
        const context = this;
        const args = arguments;
        clearTimeout(timeout);
        timeout = setTimeout(() => func.apply(context, args), delay);
    };
}
