using EduVerse.API;
using EduVerse.Application.Configuration;
using EduVerse.Application.Interfaces;
using EduVerse.Application.Services;
using EduVerse.Core.Interfaces;
using EduVerse.Infrastructure.Data;
using EduVerse.Infrastructure.Repositories;
using EduVerse.Infrastructure.Services;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using System.Text;
using AutoMapper;

var builder = WebApplication.CreateBuilder(args);

// Check if we need to run the SQL script
if (args.Contains("--run-sql-script"))
{
    await EduVerse.API.Scripts.RunSqlScript.RunAsync(args);
    return;
}

// Add services to the container.
builder.Services.AddControllers();

// Configure MySQL database
var connectionString = builder.Configuration.GetConnectionString("DefaultConnection");
builder.Services.AddDbContext<ApplicationDbContext>(options =>
    options.UseMySql(connectionString, ServerVersion.AutoDetect(connectionString)));

// Add repositories
builder.Services.AddScoped(typeof(IRepository<>), typeof(Repository<>));
builder.Services.AddScoped<IUserRepository, UserRepository>();
builder.Services.AddScoped<ICourseRepository, CourseRepository>();
builder.Services.AddScoped<IQuizRepository, QuizRepository>();
builder.Services.AddScoped<IFeedbackRepository, FeedbackRepository>();
builder.Services.AddScoped<IPaymentRepository, PaymentRepository>();
builder.Services.AddScoped<IEnrollmentRepository, EnrollmentRepository>();
builder.Services.AddScoped<IDiscussionRepository, DiscussionRepository>();
builder.Services.AddScoped<IDiscussionReplyRepository, DiscussionReplyRepository>();

// Add AutoMapper
builder.Services.AddAutoMapper(typeof(Program).Assembly, typeof(IAdminService).Assembly);

// Add services
builder.Services.AddScoped<IAuthService, EduVerse.Infrastructure.Services.AuthService>();
builder.Services.AddScoped<ICourseService, EduVerse.Infrastructure.Services.CourseService>();
builder.Services.AddScoped<IFeedbackService, EduVerse.Infrastructure.Services.FeedbackService>();
builder.Services.AddScoped<IAdminService, EduVerse.Application.Services.AdminService>();
builder.Services.AddScoped<IPaymentService, EduVerse.Infrastructure.Services.PaymentService>();
builder.Services.AddScoped<IDiscussionService, EduVerse.Infrastructure.Services.DiscussionService>();

// Configure Razorpay settings
builder.Services.Configure<RazorpaySettings>(builder.Configuration.GetSection("Razorpay"));

// Configure JWT Authentication
builder.Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
    .AddJwtBearer(options =>
    {
        options.TokenValidationParameters = new TokenValidationParameters
        {
            ValidateIssuer = true,
            ValidateAudience = true,
            ValidateLifetime = true,
            ValidateIssuerSigningKey = true,
            ValidIssuer = builder.Configuration["Jwt:Issuer"],
            ValidAudience = builder.Configuration["Jwt:Audience"],
            IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(builder.Configuration["Jwt:Key"]))
        };
    });

// Configure CORS
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowAll", builder =>
    {
        builder.AllowAnyOrigin()
               .AllowAnyMethod()
               .AllowAnyHeader();
    });
});

// Configure Swagger
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new OpenApiInfo
    {
        Title = "EduVerse Learning Hub API",
        Version = "v1",
        Description = "API for EduVerse Learning Hub - An educational platform for online courses and quizzes",
        Contact = new OpenApiContact
        {
            Name = "EduVerse Team",
            Email = "<EMAIL>"
        }
    });

    // Configure Swagger to use JWT Authentication
    c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
    {
        Description = "JWT Authorization header using the Bearer scheme. Example: \"Authorization: Bearer {token}\"",
        Name = "Authorization",
        In = ParameterLocation.Header,
        Type = SecuritySchemeType.ApiKey,
        Scheme = "Bearer"
    });

    c.AddSecurityRequirement(new OpenApiSecurityRequirement
    {
        {
            new OpenApiSecurityScheme
            {
                Reference = new OpenApiReference
                {
                    Type = ReferenceType.SecurityScheme,
                    Id = "Bearer"
                }
            },
            new string[] {}
        }
    });

    // Group endpoints by controller
    c.TagActionsBy(api => new[] { api.GroupName });
    c.DocInclusionPredicate((name, api) => true);

    // Enable XML comments
    var xmlFile = $"{System.Reflection.Assembly.GetExecutingAssembly().GetName().Name}.xml";
    var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
    if (File.Exists(xmlPath))
    {
        c.IncludeXmlComments(xmlPath);
    }
});

var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI(c =>
    {
        c.SwaggerEndpoint("/swagger/v1/swagger.json", "EduVerse Learning Hub API v1");
        c.RoutePrefix = string.Empty; // Set Swagger UI at the app's root
        c.DocExpansion(Swashbuckle.AspNetCore.SwaggerUI.DocExpansion.None); // Collapse operations by default
        c.DefaultModelsExpandDepth(-1); // Hide schemas section
        c.EnableFilter(); // Enable filtering operations
        c.DisplayRequestDuration(); // Show request duration
    });
}

// Only use HTTPS redirection in production
if (!app.Environment.IsDevelopment())
{
    app.UseHttpsRedirection();
}

// Configure static files
app.UseStaticFiles();

app.UseCors("AllowAll");

app.UseAuthentication();
app.UseAuthorization();

app.MapControllers();

// Apply migrations at startup
app.ApplyMigrations();

// Seed quizzes
using (var scope = app.Services.CreateScope())
{
    var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
    await QuizSeeder.SeedQuizzes(context);
}

app.Run();
