// User Management JavaScript

// Global variables
let users = [];
let filteredUsers = [];
let currentPage = 1;
const usersPerPage = 5;
let selectedUserId = null;

// DOM Elements
const userTableBody = document.getElementById('users-table-body');
const pagination = document.getElementById('pagination');
const searchInput = document.getElementById('search-user');
const roleFilter = document.getElementById('role-filter');
const addUserBtn = document.getElementById('add-user-btn');
const userModal = document.getElementById('user-modal');
const deleteModal = document.getElementById('delete-modal');
const userForm = document.getElementById('user-form');
const modalTitle = document.getElementById('modal-title');
const closeModalBtns = document.querySelectorAll('.close');
const cancelBtn = document.getElementById('cancel-btn');
const cancelDeleteBtn = document.getElementById('cancel-delete-btn');
const confirmDeleteBtn = document.getElementById('confirm-delete-btn');

// Initialize the page
document.addEventListener('DOMContentLoaded', function() {
    // Load users
    loadUsers();
    
    // Setup event listeners
    setupEventListeners();
    
    // Update admin info
    updateAdminInfo();
});

// Load users from API or mock data
async function loadUsers() {
    try {
        // In a real application, you would fetch from an API
        // For now, use mock data
        users = [...mockUsers];
        
        // Apply filters
        applyFilters();
        
        // Render users
        renderUsers();
    } catch (error) {
        console.error('Error loading users:', error);
    }
}

// Apply search and role filters
function applyFilters() {
    const searchTerm = searchInput.value.toLowerCase();
    const roleValue = roleFilter.value;
    
    filteredUsers = users.filter(user => {
        const matchesSearch = 
            user.username.toLowerCase().includes(searchTerm) ||
            user.fullName.toLowerCase().includes(searchTerm) ||
            user.email.toLowerCase().includes(searchTerm);
        
        const matchesRole = roleValue === '' || user.role === roleValue;
        
        return matchesSearch && matchesRole;
    });
    
    // Reset to first page when filters change
    currentPage = 1;
    
    // Render the filtered users
    renderUsers();
}

// Render users to the table
function renderUsers() {
    // Clear the table
    userTableBody.innerHTML = '';
    
    // Calculate pagination
    const startIndex = (currentPage - 1) * usersPerPage;
    const endIndex = startIndex + usersPerPage;
    const paginatedUsers = filteredUsers.slice(startIndex, endIndex);
    
    // Check if there are users to display
    if (paginatedUsers.length === 0) {
        const noDataRow = document.createElement('tr');
        noDataRow.innerHTML = `<td colspan="7" style="text-align: center;">No users found</td>`;
        userTableBody.appendChild(noDataRow);
    } else {
        // Add users to the table
        paginatedUsers.forEach(user => {
            const row = document.createElement('tr');
            
            // Determine status class
            const statusClass = user.status === 'Active' ? 'completed' : 'failed';
            
            row.innerHTML = `
                <td>${user.id}</td>
                <td>${user.fullName}</td>
                <td>${user.email}</td>
                <td>${user.role}</td>
                <td><span class="status ${statusClass}">${user.status}</span></td>
                <td>${user.createdAt}</td>
                <td class="action-buttons">
                    <button class="btn btn-secondary edit-btn" data-id="${user.id}">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-danger delete-btn" data-id="${user.id}">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            `;
            
            userTableBody.appendChild(row);
        });
    }
    
    // Render pagination
    renderPagination();
    
    // Setup edit and delete buttons
    setupActionButtons();
}

// Render pagination controls
function renderPagination() {
    pagination.innerHTML = '';
    
    const totalPages = Math.ceil(filteredUsers.length / usersPerPage);
    
    // Previous button
    const prevButton = document.createElement('button');
    prevButton.innerHTML = '&laquo; Previous';
    prevButton.disabled = currentPage === 1;
    prevButton.addEventListener('click', () => {
        if (currentPage > 1) {
            currentPage--;
            renderUsers();
        }
    });
    pagination.appendChild(prevButton);
    
    // Page buttons
    for (let i = 1; i <= totalPages; i++) {
        const pageButton = document.createElement('button');
        pageButton.textContent = i;
        pageButton.classList.toggle('active', i === currentPage);
        pageButton.addEventListener('click', () => {
            currentPage = i;
            renderUsers();
        });
        pagination.appendChild(pageButton);
    }
    
    // Next button
    const nextButton = document.createElement('button');
    nextButton.innerHTML = 'Next &raquo;';
    nextButton.disabled = currentPage === totalPages || totalPages === 0;
    nextButton.addEventListener('click', () => {
        if (currentPage < totalPages) {
            currentPage++;
            renderUsers();
        }
    });
    pagination.appendChild(nextButton);
}

// Setup edit and delete buttons
function setupActionButtons() {
    // Edit buttons
    document.querySelectorAll('.edit-btn').forEach(button => {
        button.addEventListener('click', function() {
            const userId = parseInt(this.getAttribute('data-id'));
            editUser(userId);
        });
    });
    
    // Delete buttons
    document.querySelectorAll('.delete-btn').forEach(button => {
        button.addEventListener('click', function() {
            const userId = parseInt(this.getAttribute('data-id'));
            showDeleteConfirmation(userId);
        });
    });
}

// Show the user modal for editing
function editUser(userId) {
    const user = users.find(u => u.id === userId);
    if (!user) return;
    
    // Set the selected user ID
    selectedUserId = userId;
    
    // Update modal title
    modalTitle.textContent = 'Edit User';
    
    // Fill the form with user data
    document.getElementById('user-id').value = user.id;
    document.getElementById('username').value = user.username;
    document.getElementById('full-name').value = user.fullName;
    document.getElementById('email').value = user.email;
    document.getElementById('role').value = user.role;
    document.getElementById('status').value = user.status;
    
    // Hide password field for editing
    document.getElementById('password-group').style.display = 'none';
    
    // Show the modal
    userModal.style.display = 'block';
}

// Show the user modal for adding a new user
function showAddUserModal() {
    // Reset selected user ID
    selectedUserId = null;
    
    // Update modal title
    modalTitle.textContent = 'Add New User';
    
    // Reset the form
    userForm.reset();
    document.getElementById('user-id').value = '';
    
    // Show password field for new users
    document.getElementById('password-group').style.display = 'block';
    
    // Show the modal
    userModal.style.display = 'block';
}

// Show delete confirmation modal
function showDeleteConfirmation(userId) {
    selectedUserId = userId;
    deleteModal.style.display = 'block';
}

// Save user (create or update)
function saveUser(formData) {
    if (selectedUserId) {
        // Update existing user
        const index = users.findIndex(u => u.id === selectedUserId);
        if (index !== -1) {
            users[index] = {
                ...users[index],
                username: formData.username,
                fullName: formData.fullName,
                email: formData.email,
                role: formData.role,
                status: formData.status
            };
        }
    } else {
        // Create new user
        const newUser = {
            id: users.length > 0 ? Math.max(...users.map(u => u.id)) + 1 : 1,
            username: formData.username,
            fullName: formData.fullName,
            email: formData.email,
            role: formData.role,
            status: formData.status,
            createdAt: new Date().toISOString().split('T')[0]
        };
        
        users.push(newUser);
    }
    
    // Refresh the user list
    applyFilters();
}

// Delete user
function deleteUser() {
    if (selectedUserId) {
        users = users.filter(user => user.id !== selectedUserId);
        applyFilters();
    }
    
    // Close the modal
    deleteModal.style.display = 'none';
}

// Setup event listeners
function setupEventListeners() {
    // Search input
    searchInput.addEventListener('input', applyFilters);
    
    // Role filter
    roleFilter.addEventListener('change', applyFilters);
    
    // Add user button
    addUserBtn.addEventListener('click', showAddUserModal);
    
    // Close modal buttons
    closeModalBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            userModal.style.display = 'none';
            deleteModal.style.display = 'none';
        });
    });
    
    // Cancel buttons
    cancelBtn.addEventListener('click', function() {
        userModal.style.display = 'none';
    });
    
    cancelDeleteBtn.addEventListener('click', function() {
        deleteModal.style.display = 'none';
    });
    
    // Confirm delete button
    confirmDeleteBtn.addEventListener('click', deleteUser);
    
    // User form submission
    userForm.addEventListener('submit', function(event) {
        event.preventDefault();
        
        const formData = {
            username: document.getElementById('username').value,
            fullName: document.getElementById('full-name').value,
            email: document.getElementById('email').value,
            role: document.getElementById('role').value,
            status: document.getElementById('status').value,
            password: document.getElementById('password').value
        };
        
        saveUser(formData);
        userModal.style.display = 'none';
    });
    
    // Toggle sidebar
    const toggleMenu = document.querySelector('.toggle-menu');
    if (toggleMenu) {
        toggleMenu.addEventListener('click', function() {
            document.querySelector('.admin-container').classList.toggle('sidebar-collapsed');
        });
    }
    
    // Admin logout
    const logoutBtn = document.getElementById('admin-logout');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', function(event) {
            event.preventDefault();
            
            // Clear localStorage
            localStorage.removeItem('token');
            localStorage.removeItem('user');
            
            // Redirect to login page
            window.location.href = '../admin-login.html';
        });
    }
}

// Update admin info
function updateAdminInfo() {
    const user = JSON.parse(localStorage.getItem('user') || '{}');
    const adminNameElement = document.querySelector('.admin-name');
    
    if (adminNameElement && user.fullName) {
        adminNameElement.textContent = user.fullName;
    }
}
