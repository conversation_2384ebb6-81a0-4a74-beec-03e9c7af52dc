using AutoMapper;
using EduVerse.Application.DTOs;
using EduVerse.Application.DTOs.Admin;
using EduVerse.Application.Interfaces;
using EduVerse.Core.Entities;
using EduVerse.Core.Interfaces;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace EduVerse.Application.Services
{
    /// <summary>
    /// Service for admin operations
    /// </summary>
    public class AdminService : IAdminService
    {
        private readonly IUserRepository _userRepository;
        private readonly ICourseRepository _courseRepository;
        private readonly IPaymentRepository _paymentRepository;
        private readonly IEnrollmentRepository _enrollmentRepository;
        private readonly IMapper _mapper;

        /// <summary>
        /// Constructor
        /// </summary>
        public AdminService(
            IUserRepository userRepository,
            ICourseRepository courseRepository,
            IPaymentRepository paymentRepository,
            IEnrollmentRepository enrollmentRepository,
            IMapper mapper)
        {
            _userRepository = userRepository;
            _courseRepository = courseRepository;
            _paymentRepository = paymentRepository;
            _enrollmentRepository = enrollmentRepository;
            _mapper = mapper;
        }

        /// <summary>
        /// Get dashboard statistics
        /// </summary>
        /// <returns>Dashboard statistics</returns>
        public async Task<DashboardStatsDto> GetDashboardStatsAsync()
        {
            var totalUsers = await _userRepository.GetTotalUsersAsync();
            var totalCourses = await _courseRepository.GetTotalCoursesAsync();
            var totalRevenue = await _paymentRepository.GetTotalRevenueAsync();
            var totalEnrollments = await _enrollmentRepository.GetTotalEnrollmentsAsync();

            return new DashboardStatsDto
            {
                TotalUsers = totalUsers,
                TotalCourses = totalCourses,
                TotalRevenue = totalRevenue,
                TotalEnrollments = totalEnrollments
            };
        }

        /// <summary>
        /// Get recent enrollments
        /// </summary>
        /// <param name="limit">Number of enrollments to return</param>
        /// <returns>List of recent enrollments</returns>
        public async Task<IEnumerable<EnrollmentDto>> GetRecentEnrollmentsAsync(int limit = 5)
        {
            var enrollments = await _enrollmentRepository.GetRecentEnrollmentsAsync(limit);
            return _mapper.Map<IEnumerable<EnrollmentDto>>(enrollments);
        }

        /// <summary>
        /// Get revenue chart data
        /// </summary>
        /// <param name="period">Period for the chart (daily, weekly, monthly, yearly)</param>
        /// <returns>Revenue chart data</returns>
        public async Task<ChartDataDto> GetRevenueChartDataAsync(string period = "monthly")
        {
            var revenueData = await _paymentRepository.GetRevenueByPeriodAsync(period);

            return new ChartDataDto
            {
                Labels = revenueData.Select(d => d.Label).ToList(),
                Data = revenueData.Select(d => d.Value).ToList()
            };
        }

        /// <summary>
        /// Get user growth chart data
        /// </summary>
        /// <param name="period">Period for the chart (daily, weekly, monthly, yearly)</param>
        /// <returns>User growth chart data</returns>
        public async Task<ChartDataDto> GetUserGrowthChartDataAsync(string period = "monthly")
        {
            var userData = await _userRepository.GetUserGrowthByPeriodAsync(period);

            return new ChartDataDto
            {
                Labels = userData.Select(d => d.Label).ToList(),
                Data = userData.Select(d => (decimal)d.Value).ToList()
            };
        }

        /// <summary>
        /// Get all users
        /// </summary>
        /// <returns>List of all users</returns>
        public async Task<IEnumerable<UserDto>> GetAllUsersAsync()
        {
            var users = await _userRepository.GetAllAsync();
            return _mapper.Map<IEnumerable<UserDto>>(users);
        }

        /// <summary>
        /// Get user by ID
        /// </summary>
        /// <param name="id">User ID</param>
        /// <returns>User details</returns>
        public async Task<UserDto> GetUserByIdAsync(int id)
        {
            var user = await _userRepository.GetByIdAsync(id);
            return _mapper.Map<UserDto>(user);
        }

        /// <summary>
        /// Update user
        /// </summary>
        /// <param name="userDto">Updated user data</param>
        /// <returns>Result with updated user</returns>
        public async Task<ApiResponseDto<UserDto>> UpdateUserAsync(UserUpdateDto userDto)
        {
            var user = await _userRepository.GetByIdAsync(userDto.Id);
            if (user == null)
                return ApiResponseDto<UserDto>.FailureResponse("User not found");

            // Update user properties
            user.FullName = userDto.FullName;
            user.Email = userDto.Email;
            user.Username = userDto.Username;
            user.Role = userDto.Role;

            await _userRepository.UpdateAsync(user);

            var updatedUserDto = _mapper.Map<UserDto>(user);
            return ApiResponseDto<UserDto>.SuccessResponse(
                updatedUserDto,
                "User updated successfully");
        }

        /// <summary>
        /// Delete user
        /// </summary>
        /// <param name="id">User ID</param>
        /// <returns>Result of the operation</returns>
        public async Task<ApiResponseDto> DeleteUserAsync(int id)
        {
            var user = await _userRepository.GetByIdAsync(id);
            if (user == null)
                return ApiResponseDto.FailureResponse("User not found");

            await _userRepository.DeleteAsync(user);

            return ApiResponseDto.SuccessResponse("User deleted successfully");
        }

        /// <summary>
        /// Get all courses with admin details
        /// </summary>
        /// <returns>List of all courses with admin details</returns>
        public async Task<AdminCoursesResponseDto> GetAllCoursesWithDetailsAsync()
        {
            var courses = await _courseRepository.GetAllCoursesWithDetailsAsync();

            var adminCourses = _mapper.Map<List<AdminCourseDto>>(courses);

            return new AdminCoursesResponseDto
            {
                Courses = adminCourses,
                TotalPages = 1, // Implement pagination if needed
                CurrentPage = 1,
                TotalCourses = adminCourses.Count
            };
        }

        /// <summary>
        /// Create a new course
        /// </summary>
        /// <param name="createDto">Course data</param>
        /// <returns>Result with created course</returns>
        public async Task<ApiResponseDto<CourseDto>> CreateCourseAsync(CourseCreateDto createDto)
        {
            var course = new Course
            {
                Title = createDto.Title,
                Description = createDto.Description,
                CategoryId = createDto.CategoryId,
                Price = createDto.Price,
                Duration = createDto.Duration,
                Status = createDto.Status,
                ImageUrl = createDto.ImageUrl
            };

            await _courseRepository.AddAsync(course);

            var createdCourse = await _courseRepository.GetCourseWithDetailsAsync(course.Id);

            var courseDto = _mapper.Map<CourseDto>(createdCourse);
            return ApiResponseDto<CourseDto>.SuccessResponse(
                courseDto,
                "Course created successfully");
        }

        /// <summary>
        /// Update a course
        /// </summary>
        /// <param name="updateDto">Updated course data</param>
        /// <returns>Result with updated course</returns>
        public async Task<ApiResponseDto<CourseDto>> UpdateCourseAsync(CourseUpdateDto updateDto)
        {
            var course = await _courseRepository.GetByIdAsync(updateDto.Id);
            if (course == null)
                return ApiResponseDto<CourseDto>.FailureResponse("Course not found");

            // Update course properties
            course.Title = updateDto.Title;
            course.Description = updateDto.Description;
            course.CategoryId = updateDto.CategoryId;
            course.Price = updateDto.Price;
            course.Duration = updateDto.Duration;
            course.Status = updateDto.Status;
            course.ImageUrl = updateDto.ImageUrl;

            await _courseRepository.UpdateAsync(course);

            var updatedCourse = await _courseRepository.GetCourseWithDetailsAsync(course.Id);

            var resultDto = _mapper.Map<CourseDto>(updatedCourse);
            return ApiResponseDto<CourseDto>.SuccessResponse(
                resultDto,
                "Course updated successfully");
        }

        /// <summary>
        /// Delete a course
        /// </summary>
        /// <param name="id">Course ID</param>
        /// <returns>Result of the operation</returns>
        public async Task<ApiResponseDto> DeleteCourseAsync(int id)
        {
            var course = await _courseRepository.GetByIdAsync(id);
            if (course == null)
                return ApiResponseDto.FailureResponse("Course not found");

            await _courseRepository.DeleteAsync(course);

            return ApiResponseDto.SuccessResponse("Course deleted successfully");
        }

        /// <summary>
        /// Get all payments
        /// </summary>
        /// <param name="page">Page number</param>
        /// <param name="limit">Items per page</param>
        /// <returns>Paginated list of payments</returns>
        public async Task<PaginatedResponseDto<PaymentDto>> GetAllPaymentsAsync(int page = 1, int limit = 10)
        {
            var (payments, totalCount) = await _paymentRepository.GetAllPaymentsPaginatedAsync(page, limit);

            var paymentDtos = _mapper.Map<List<PaymentDto>>(payments);

            return new PaginatedResponseDto<PaymentDto>
            {
                Items = paymentDtos,
                TotalItems = totalCount,
                CurrentPage = page,
                PageSize = limit,
                TotalPages = (totalCount + limit - 1) / limit
            };
        }

        /// <summary>
        /// Get payment by ID
        /// </summary>
        /// <param name="id">Payment ID</param>
        /// <returns>Payment details</returns>
        public async Task<PaymentDto> GetPaymentByIdAsync(int id)
        {
            var payment = await _paymentRepository.GetPaymentByIdWithDetailsAsync(id);
            if (payment == null)
                return null;

            return _mapper.Map<PaymentDto>(payment);
        }

        /// <summary>
        /// Update payment status
        /// </summary>
        /// <param name="id">Payment ID</param>
        /// <param name="status">New status</param>
        /// <returns>Result with updated payment</returns>
        public async Task<ApiResponseDto<PaymentDto>> UpdatePaymentStatusAsync(int id, string status)
        {
            var payment = await _paymentRepository.GetPaymentByIdWithDetailsAsync(id);
            if (payment == null)
                return ApiResponseDto<PaymentDto>.FailureResponse("Payment not found");

            payment.Status = status;
            await _paymentRepository.UpdateAsync(payment);

            var updatedPayment = await _paymentRepository.GetPaymentByIdWithDetailsAsync(id);

            var paymentDto = _mapper.Map<PaymentDto>(updatedPayment);
            return ApiResponseDto<PaymentDto>.SuccessResponse(
                paymentDto,
                "Payment status updated successfully");
        }
    }
}
