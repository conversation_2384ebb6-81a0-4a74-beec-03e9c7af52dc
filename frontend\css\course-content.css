/* Course Content CSS for EduVerse Learning Hub */

/* Video Container */
.video-box {
    width: 100%;
    margin: 20px 0;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.video-box iframe {
    width: 100%;
    height: 450px;
    border: none;
}

/* Locked Content */
#locked-content {
    text-align: center;
    padding: 40px 20px;
    background-color: #f8f9fa;
    border-radius: 8px;
    margin: 20px 0;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

#locked-content:hover {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    transform: translateY(-5px);
}

#locked-content img {
    width: 80px;
    margin-bottom: 20px;
    opacity: 0.8;
}

#locked-content h3 {
    color: #333;
    margin-bottom: 15px;
    font-size: 24px;
}

#locked-content p {
    color: #666;
    margin-bottom: 20px;
    font-size: 16px;
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
}

#unlock-btn {
    background-color: #DF2771;
    color: white;
    border: none;
    padding: 12px 25px;
    border-radius: 5px;
    font-size: 1.1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

#unlock-btn:hover {
    background-color: #c91c5f;
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
}

/* Responsive Styles */
@media (max-width: 768px) {
    .video-box iframe {
        height: 300px;
    }
    
    #locked-content {
        padding: 30px 15px;
    }
    
    #locked-content img {
        width: 60px;
    }
    
    #locked-content h3 {
        font-size: 20px;
    }
    
    #locked-content p {
        font-size: 14px;
    }
    
    #unlock-btn {
        padding: 10px 20px;
        font-size: 1rem;
    }
}

@media (max-width: 480px) {
    .video-box iframe {
        height: 200px;
    }
}
