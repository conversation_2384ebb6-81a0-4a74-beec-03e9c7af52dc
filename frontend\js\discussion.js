// Discussion JavaScript for EduVerse Learning Hub

// Tutor data
const tutorData = {
    john: {
        name: "<PERSON>",
        subject: "Computer Science",
        image: "images/tutors/tutor1.png",
        experience: "8 years",
        expertise: "Java, Python, Data Structures",
        rating: "4.8/5"
    },
    sarah: {
        name: "<PERSON>",
        subject: "Data Structures",
        image: "images/tutors/tutor2.png",
        experience: "6 years",
        expertise: "Data Structures, Algorithms, C++",
        rating: "4.9/5"
    },
    michael: {
        name: "<PERSON>",
        subject: "Algorithms",
        image: "images/tutors/tutor3.png",
        experience: "10 years",
        expertise: "Algorithms, Machine Learning, Python",
        rating: "4.7/5"
    },
    emily: {
        name: "<PERSON>",
        subject: "Web Development",
        image: "images/tutors/tutor4.png",
        experience: "5 years",
        expertise: "HTML, CSS, JavaScript, React",
        rating: "4.6/5"
    }
};

// Chat history
let chatHistory = {
    john: [
        { sender: 'tutor', message: 'Hello! How can I help you with Computer Science today?', time: '10:00 AM' }
    ],
    sarah: [
        { sender: 'tutor', message: 'Hi there! Do you have any questions about Data Structures?', time: '10:05 AM' }
    ],
    michael: [
        { sender: 'tutor', message: 'Hello! I\'m currently away but will respond to your algorithm questions when I return.', time: '09:30 AM' }
    ],
    emily: [
        { sender: 'tutor', message: 'Hi! Need help with Web Development? I\'m here to assist!', time: '10:15 AM' }
    ]
};

// Current selected tutor
let currentTutor = 'john';

// DOM elements
let chatMessages;
let messageInput;
let sendButton;
let tutorElements;
let endChatButton;

// Initialize the discussion page
document.addEventListener('DOMContentLoaded', function() {
    // Check authentication state
    if (typeof updateAuthUI === 'function') {
        updateAuthUI();
    }

    // Setup logout functionality
    if (typeof setupLogout === 'function') {
        setupLogout();
    }

    // Initialize chat elements
    initChat();
});

// Initialize chat elements and event listeners
function initChat() {
    // Get DOM elements
    chatMessages = document.getElementById('chat-messages');
    messageInput = document.getElementById('message-input');
    sendButton = document.getElementById('send-message-btn');
    tutorElements = document.querySelectorAll('.tutor');
    endChatButton = document.getElementById('end-chat-btn');

    // Add event listeners
    sendButton.addEventListener('click', sendMessage);
    messageInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            sendMessage();
        }
    });

    endChatButton.addEventListener('click', endChat);

    // Add click event to tutors
    tutorElements.forEach(tutor => {
        tutor.addEventListener('click', function() {
            const tutorId = this.getAttribute('data-tutor');
            selectTutor(tutorId);
        });
    });

    // Load initial chat
    loadChat(currentTutor);
    updateTutorProfile(currentTutor);
}

// Select a tutor
function selectTutor(tutorId) {
    // Update current tutor
    currentTutor = tutorId;
    
    // Update active class
    tutorElements.forEach(tutor => {
        tutor.classList.remove('active');
        if (tutor.getAttribute('data-tutor') === tutorId) {
            tutor.classList.add('active');
        }
    });
    
    // Load chat history
    loadChat(tutorId);
    
    // Update tutor profile
    updateTutorProfile(tutorId);
    
    // Update chat header
    updateChatHeader(tutorId);
}

// Load chat history
function loadChat(tutorId) {
    // Clear chat messages
    chatMessages.innerHTML = '';
    
    // Add welcome message if no messages
    if (chatHistory[tutorId].length === 0) {
        chatMessages.innerHTML = `
            <div class="message-welcome">
                <p>Start a conversation with ${tutorData[tutorId].name}!</p>
            </div>
        `;
        return;
    }
    
    // Add messages to chat
    chatHistory[tutorId].forEach(msg => {
        const messageClass = msg.sender === 'user' ? 'sent' : 'received';
        const messageHTML = `
            <div class="message ${messageClass}">
                <p>${msg.message}</p>
                <div class="time">${msg.time}</div>
            </div>
        `;
        chatMessages.innerHTML += messageHTML;
    });
    
    // Scroll to bottom
    scrollToBottom();
}

// Send a message
function sendMessage() {
    const message = messageInput.value.trim();
    if (message === '') return;
    
    // Get current time
    const now = new Date();
    const hours = now.getHours() % 12 || 12;
    const minutes = now.getMinutes().toString().padStart(2, '0');
    const ampm = now.getHours() >= 12 ? 'PM' : 'AM';
    const time = `${hours}:${minutes} ${ampm}`;
    
    // Add message to chat history
    chatHistory[currentTutor].push({
        sender: 'user',
        message: message,
        time: time
    });
    
    // Clear input
    messageInput.value = '';
    
    // Reload chat
    loadChat(currentTutor);
    
    // Simulate tutor response after a delay
    setTimeout(() => {
        simulateTutorResponse(currentTutor);
    }, 1000);
}

// Simulate tutor response
function simulateTutorResponse(tutorId) {
    // Get last user message
    const lastMessage = chatHistory[tutorId].filter(msg => msg.sender === 'user').pop();
    
    if (!lastMessage) return;
    
    // Generate response based on message content
    let response = '';
    const lowercaseMsg = lastMessage.message.toLowerCase();
    
    if (lowercaseMsg.includes('hello') || lowercaseMsg.includes('hi')) {
        response = `Hello! How can I help you with ${tutorData[tutorId].subject} today?`;
    } else if (lowercaseMsg.includes('thank')) {
        response = "You're welcome! Feel free to ask if you have any other questions.";
    } else if (lowercaseMsg.includes('bye') || lowercaseMsg.includes('goodbye')) {
        response = "Goodbye! Have a great day!";
    } else if (lowercaseMsg.includes('help')) {
        response = `I'd be happy to help you with ${tutorData[tutorId].subject}. What specific topic are you struggling with?`;
    } else if (lowercaseMsg.includes('course')) {
        response = "We offer various courses on our platform. Which specific course are you interested in?";
    } else {
        // Default responses based on tutor subject
        const responses = [
            `That's a great question about ${tutorData[tutorId].subject}! Let me explain...`,
            `In ${tutorData[tutorId].subject}, this concept is important because...`,
            `I understand your question. When working with ${tutorData[tutorId].subject}, you should consider...`,
            `Let me provide some resources on this ${tutorData[tutorId].subject} topic that might help you.`
        ];
        response = responses[Math.floor(Math.random() * responses.length)];
    }
    
    // Get current time
    const now = new Date();
    const hours = now.getHours() % 12 || 12;
    const minutes = now.getMinutes().toString().padStart(2, '0');
    const ampm = now.getHours() >= 12 ? 'PM' : 'AM';
    const time = `${hours}:${minutes} ${ampm}`;
    
    // Add response to chat history
    chatHistory[tutorId].push({
        sender: 'tutor',
        message: response,
        time: time
    });
    
    // Reload chat
    loadChat(tutorId);
}

// Update tutor profile
function updateTutorProfile(tutorId) {
    const tutor = tutorData[tutorId];
    
    document.getElementById('profile-img').src = tutor.image;
    document.getElementById('profile-name').textContent = tutor.name;
    document.getElementById('profile-subject').textContent = tutor.subject;
    document.getElementById('profile-experience').textContent = tutor.experience;
    document.getElementById('profile-expertise').textContent = tutor.expertise;
    document.getElementById('profile-rating').textContent = tutor.rating;
}

// Update chat header
function updateChatHeader(tutorId) {
    const tutor = tutorData[tutorId];
    
    document.getElementById('chat-header-img').src = tutor.image;
    document.getElementById('chat-header-name').textContent = tutor.name;
    document.getElementById('chat-header-subject').textContent = tutor.subject;
}

// End chat
function endChat() {
    // Clear chat history for current tutor
    chatHistory[currentTutor] = [];
    
    // Add welcome message
    chatHistory[currentTutor].push({
        sender: 'tutor',
        message: `Hello! How can I help you with ${tutorData[currentTutor].subject} today?`,
        time: getCurrentTime()
    });
    
    // Reload chat
    loadChat(currentTutor);
}

// Get current time formatted
function getCurrentTime() {
    const now = new Date();
    const hours = now.getHours() % 12 || 12;
    const minutes = now.getMinutes().toString().padStart(2, '0');
    const ampm = now.getHours() >= 12 ? 'PM' : 'AM';
    return `${hours}:${minutes} ${ampm}`;
}

// Scroll chat to bottom
function scrollToBottom() {
    chatMessages.scrollTop = chatMessages.scrollHeight;
}

// Update authentication UI
function updateAuthUI() {
    try {
        const isAuthenticated = localStorage.getItem('user') !== null;
        const loginBtn = document.querySelector('.login-btn');
        const userInfo = document.querySelector('.user-info');

        if (loginBtn && userInfo) {
            if (isAuthenticated) {
                // User is logged in
                loginBtn.style.display = 'none';
                
                // Show user info
                const user = JSON.parse(localStorage.getItem('user'));
                userInfo.style.display = 'block';
                userInfo.querySelector('.username').textContent = user.fullName || user.username;
            } else {
                // User is not logged in
                loginBtn.style.display = 'block';
                userInfo.style.display = 'none';
            }
        }
    } catch (error) {
        console.error('Error updating auth UI:', error);
    }
}

// Setup logout functionality
function setupLogout() {
    const logoutBtn = document.querySelector('.logout-btn');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', function(event) {
            event.preventDefault();
            localStorage.removeItem('user');
            window.location.href = 'index.html';
        });
    }
}
