using System;
using System.Collections.Generic;

namespace EduVerse.Core.Entities
{
    /// <summary>
    /// Course entity
    /// </summary>
    public class Course
    {
        /// <summary>
        /// Course ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// Course title
        /// </summary>
        public string Title { get; set; }

        /// <summary>
        /// Course description
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// Course image URL
        /// </summary>
        public string ImageUrl { get; set; }

        /// <summary>
        /// Course video URL
        /// </summary>
        public string VideoUrl { get; set; }

        /// <summary>
        /// Course price
        /// </summary>
        public decimal Price { get; set; }

        /// <summary>
        /// Course duration in hours
        /// </summary>
        public int Duration { get; set; }

        /// <summary>
        /// Course status (active, draft, archived)
        /// </summary>
        public string Status { get; set; } = "active";

        /// <summary>
        /// Creation date
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// Last update date
        /// </summary>
        public DateTime? UpdatedAt { get; set; }

        // Navigation properties
        /// <summary>
        /// Category ID
        /// </summary>
        public int CategoryId { get; set; }

        /// <summary>
        /// Category
        /// </summary>
        public CourseCategory Category { get; set; }

        /// <summary>
        /// Quizzes
        /// </summary>
        public ICollection<Quiz> Quizzes { get; set; }

        /// <summary>
        /// Enrollments
        /// </summary>
        public ICollection<Enrollment> Enrollments { get; set; }

        /// <summary>
        /// Payments
        /// </summary>
        public ICollection<Payment> Payments { get; set; }

        /// <summary>
        /// Discussions related to this course
        /// </summary>
        public ICollection<Discussion> Discussions { get; set; }
    }
}
