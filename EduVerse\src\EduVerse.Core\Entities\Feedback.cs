using System;

namespace EduVerse.Core.Entities
{
    /// <summary>
    /// Feedback entity
    /// </summary>
    public class Feedback
    {
        /// <summary>
        /// Feedback ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// Name of the person providing feedback
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// Email of the person providing feedback
        /// </summary>
        public string Email { get; set; }

        /// <summary>
        /// Feedback message
        /// </summary>
        public string Message { get; set; }

        /// <summary>
        /// Admin response to the feedback
        /// </summary>
        public string? Response { get; set; }

        /// <summary>
        /// Response date
        /// </summary>
        public DateTime? ResponseDate { get; set; }

        /// <summary>
        /// Creation date
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// User ID (if feedback is from a registered user)
        /// </summary>
        public int? UserId { get; set; }

        /// <summary>
        /// Navigation property for user
        /// </summary>
        public User User { get; set; }
    }
}
