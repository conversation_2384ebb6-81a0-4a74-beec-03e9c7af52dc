using EduVerse.Core.Entities;
using EduVerse.Core.Interfaces;
using EduVerse.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace EduVerse.Infrastructure.Repositories
{
    /// <summary>
    /// Repository for feedback
    /// </summary>
    public class FeedbackRepository : Repository<Feedback>, IFeedbackRepository
    {
        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="context">Database context</param>
        public FeedbackRepository(ApplicationDbContext context) : base(context)
        {
        }

        /// <summary>
        /// Get recent feedback
        /// </summary>
        /// <param name="limit">Number of feedback items to return</param>
        /// <returns>List of recent feedback</returns>
        public async Task<IEnumerable<Feedback>> GetRecentFeedbackAsync(int limit)
        {
            return await _context.Feedback
                .Include(f => f.User)
                .OrderByDescending(f => f.CreatedAt)
                .Take(limit)
                .ToListAsync();
        }

        /// <summary>
        /// Get all feedback with pagination
        /// </summary>
        /// <param name="page">Page number</param>
        /// <param name="pageSize">Items per page</param>
        /// <returns>Paginated list of feedback</returns>
        public async Task<(IEnumerable<Feedback> Items, int TotalCount)> GetAllFeedbackPaginatedAsync(int page, int pageSize)
        {
            var totalCount = await _context.Feedback.CountAsync();
            var items = await _context.Feedback
                .Include(f => f.User)
                .OrderByDescending(f => f.CreatedAt)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            return (items, totalCount);
        }
    }
}
