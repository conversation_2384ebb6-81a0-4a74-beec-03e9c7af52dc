// Admin Service for EduVerse Learning Hub

// Mock data for offline use
const mockData = {
    dashboardStats: {
        totalUsers: 120,
        totalCourses: 25,
        totalRevenue: 15750,
        totalEnrollments: 350
    },
    recentEnrollments: [
        { userName: '<PERSON>', courseName: 'Web Development', enrollmentDate: '2023-05-01', amount: 99, status: 'Completed' },
        { userName: '<PERSON>', courseName: 'Data Science', enrollmentDate: '2023-05-02', amount: 129, status: 'Pending' },
        { userName: '<PERSON>', courseName: 'Mobile App Development', enrollmentDate: '2023-05-03', amount: 149, status: 'Completed' }
    ],
    recentFeedback: [
        { name: '<PERSON>', message: 'Great platform for learning!', createdAt: '2023-05-01' },
        { name: '<PERSON>', message: 'The courses are very informative.', createdAt: '2023-05-02' }
    ],
    revenueChart: {
        labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
        data: [1200, 1900, 1500, 2500, 2100, 3000]
    },
    userGrowthChart: {
        labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
        data: [15, 25, 18, 30, 22, 35]
    }
};

const AdminDashboardService = {
    // Get dashboard statistics
    getDashboardStats: async () => {
        try {
            // Check if API_BASE_URL is defined
            if (typeof API_BASE_URL === 'undefined') {
                console.warn('API_BASE_URL is not defined, using mock data');
                return mockData.dashboardStats;
            }

            const token = localStorage.getItem('token');
            if (!token) {
                console.warn('No token found, using mock data');
                return mockData.dashboardStats;
            }

            try {
                const response = await fetch(`${API_BASE_URL}/api/admin/dashboard/stats`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                return await response.json();
            } catch (fetchError) {
                console.warn('API fetch failed, using mock data:', fetchError);
                return mockData.dashboardStats;
            }
        } catch (error) {
            console.error('Get dashboard stats error:', error);
            return mockData.dashboardStats;
        }
    },

    // Get recent enrollments
    getRecentEnrollments: async (limit = 5) => {
        try {
            // Check if API_BASE_URL is defined
            if (typeof API_BASE_URL === 'undefined') {
                console.warn('API_BASE_URL is not defined, using mock data');
                return mockData.recentEnrollments.slice(0, limit);
            }

            const token = localStorage.getItem('token');
            if (!token) {
                console.warn('No token found, using mock data');
                return mockData.recentEnrollments.slice(0, limit);
            }

            try {
                const response = await fetch(`${API_BASE_URL}/api/admin/enrollments/recent?limit=${limit}`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                return await response.json();
            } catch (fetchError) {
                console.warn('API fetch failed, using mock data:', fetchError);
                return mockData.recentEnrollments.slice(0, limit);
            }
        } catch (error) {
            console.error('Get recent enrollments error:', error);
            return mockData.recentEnrollments.slice(0, limit);
        }
    },

    // Get recent feedback
    getRecentFeedback: async (limit = 5) => {
        try {
            // Check if API_BASE_URL is defined
            if (typeof API_BASE_URL === 'undefined') {
                console.warn('API_BASE_URL is not defined, using mock data');
                return mockData.recentFeedback.slice(0, limit);
            }

            const token = localStorage.getItem('token');
            if (!token) {
                console.warn('No token found, using mock data');
                return mockData.recentFeedback.slice(0, limit);
            }

            try {
                const response = await fetch(`${API_BASE_URL}/api/admin/feedback/recent?limit=${limit}`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                return await response.json();
            } catch (fetchError) {
                console.warn('API fetch failed, using mock data:', fetchError);
                return mockData.recentFeedback.slice(0, limit);
            }
        } catch (error) {
            console.error('Get recent feedback error:', error);
            return mockData.recentFeedback.slice(0, limit);
        }
    },

    // Get revenue data for chart
    getRevenueChartData: async (period = 'monthly') => {
        try {
            // Check if API_BASE_URL is defined
            if (typeof API_BASE_URL === 'undefined') {
                console.warn('API_BASE_URL is not defined, using mock data');
                return mockData.revenueChart;
            }

            const token = localStorage.getItem('token');
            if (!token) {
                console.warn('No token found, using mock data');
                return mockData.revenueChart;
            }

            try {
                const response = await fetch(`${API_BASE_URL}/api/admin/revenue/chart?period=${period}`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                return await response.json();
            } catch (fetchError) {
                console.warn('API fetch failed, using mock data:', fetchError);
                return mockData.revenueChart;
            }
        } catch (error) {
            console.error('Get revenue chart data error:', error);
            return mockData.revenueChart;
        }
    },

    // Get user growth data for chart
    getUserGrowthChartData: async (period = 'monthly') => {
        try {
            // Check if API_BASE_URL is defined
            if (typeof API_BASE_URL === 'undefined') {
                console.warn('API_BASE_URL is not defined, using mock data');
                return mockData.userGrowthChart;
            }

            const token = localStorage.getItem('token');
            if (!token) {
                console.warn('No token found, using mock data');
                return mockData.userGrowthChart;
            }

            try {
                const response = await fetch(`${API_BASE_URL}/api/admin/users/growth?period=${period}`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                return await response.json();
            } catch (fetchError) {
                console.warn('API fetch failed, using mock data:', fetchError);
                return mockData.userGrowthChart;
            }
        } catch (error) {
            console.error('Get user growth chart data error:', error);
            return mockData.userGrowthChart;
        }
    }
};

const AdminCourseService = {
    // Get all courses with details
    getAllCourses: async () => {
        try {
            const token = localStorage.getItem('token');
            if (!token) throw new Error('Authentication required');

            const response = await fetch(`${API_BASE_URL}/api/admin/courses`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });
            return await response.json();
        } catch (error) {
            console.error('Get all courses error:', error);
            throw error;
        }
    },

    // Create a new course
    createCourse: async (courseData) => {
        try {
            const token = localStorage.getItem('token');
            if (!token) throw new Error('Authentication required');

            const response = await fetch(`${API_BASE_URL}/api/admin/courses`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`
                },
                body: JSON.stringify(courseData)
            });
            return await response.json();
        } catch (error) {
            console.error('Create course error:', error);
            throw error;
        }
    },

    // Update a course
    updateCourse: async (courseId, courseData) => {
        try {
            const token = localStorage.getItem('token');
            if (!token) throw new Error('Authentication required');

            const response = await fetch(`${API_BASE_URL}/api/admin/courses/${courseId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`
                },
                body: JSON.stringify(courseData)
            });
            return await response.json();
        } catch (error) {
            console.error(`Update course ${courseId} error:`, error);
            throw error;
        }
    },

    // Delete a course
    deleteCourse: async (courseId) => {
        try {
            const token = localStorage.getItem('token');
            if (!token) throw new Error('Authentication required');

            const response = await fetch(`${API_BASE_URL}/api/admin/courses/${courseId}`, {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });
            return await response.json();
        } catch (error) {
            console.error(`Delete course ${courseId} error:`, error);
            throw error;
        }
    }
};

const AdminUserService = {
    // Get all users
    getAllUsers: async () => {
        try {
            const token = localStorage.getItem('token');
            if (!token) throw new Error('Authentication required');

            const response = await fetch(`${API_BASE_URL}/api/admin/users`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });
            return await response.json();
        } catch (error) {
            console.error('Get all users error:', error);
            throw error;
        }
    },

    // Get user by ID
    getUserById: async (userId) => {
        try {
            const token = localStorage.getItem('token');
            if (!token) throw new Error('Authentication required');

            const response = await fetch(`${API_BASE_URL}/api/admin/users/${userId}`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });
            return await response.json();
        } catch (error) {
            console.error(`Get user ${userId} error:`, error);
            throw error;
        }
    },

    // Update user
    updateUser: async (userId, userData) => {
        try {
            const token = localStorage.getItem('token');
            if (!token) throw new Error('Authentication required');

            const response = await fetch(`${API_BASE_URL}/api/admin/users/${userId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`
                },
                body: JSON.stringify(userData)
            });
            return await response.json();
        } catch (error) {
            console.error(`Update user ${userId} error:`, error);
            throw error;
        }
    },

    // Delete user
    deleteUser: async (userId) => {
        try {
            const token = localStorage.getItem('token');
            if (!token) throw new Error('Authentication required');

            const response = await fetch(`${API_BASE_URL}/api/admin/users/${userId}`, {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });
            return await response.json();
        } catch (error) {
            console.error(`Delete user ${userId} error:`, error);
            throw error;
        }
    }
};

const AdminPaymentService = {
    // Get all payments
    getAllPayments: async (page = 1, limit = 10) => {
        try {
            const token = localStorage.getItem('token');
            if (!token) throw new Error('Authentication required');

            const response = await fetch(`${API_BASE_URL}/api/admin/payments?page=${page}&limit=${limit}`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });
            return await response.json();
        } catch (error) {
            console.error('Get all payments error:', error);
            throw error;
        }
    },

    // Get payment by ID
    getPaymentById: async (paymentId) => {
        try {
            const token = localStorage.getItem('token');
            if (!token) throw new Error('Authentication required');

            const response = await fetch(`${API_BASE_URL}/api/admin/payments/${paymentId}`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });
            return await response.json();
        } catch (error) {
            console.error(`Get payment ${paymentId} error:`, error);
            throw error;
        }
    },

    // Update payment status
    updatePaymentStatus: async (paymentId, status) => {
        try {
            const token = localStorage.getItem('token');
            if (!token) throw new Error('Authentication required');

            const response = await fetch(`${API_BASE_URL}/api/admin/payments/${paymentId}/status`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`
                },
                body: JSON.stringify({ status })
            });
            return await response.json();
        } catch (error) {
            console.error(`Update payment ${paymentId} status error:`, error);
            throw error;
        }
    }
};

const AdminFeedbackService = {
    // Get all feedback
    getAllFeedback: async (page = 1, limit = 10) => {
        try {
            const token = localStorage.getItem('token');
            if (!token) throw new Error('Authentication required');

            const response = await fetch(`${API_BASE_URL}/api/admin/feedback?page=${page}&limit=${limit}`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });
            return await response.json();
        } catch (error) {
            console.error('Get all feedback error:', error);
            throw error;
        }
    },

    // Respond to feedback
    respondToFeedback: async (feedbackId, responseText) => {
        try {
            const token = localStorage.getItem('token');
            if (!token) throw new Error('Authentication required');

            const response = await fetch(`${API_BASE_URL}/api/admin/feedback/${feedbackId}/respond`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`
                },
                body: JSON.stringify({ response: responseText })
            });
            return await response.json();
        } catch (error) {
            console.error(`Respond to feedback ${feedbackId} error:`, error);
            throw error;
        }
    }
};
