namespace EduVerse.Application.DTOs.Payment
{
    /// <summary>
    /// Response DTO for payment verification
    /// </summary>
    public class PaymentVerificationResponseDto
    {
        /// <summary>
        /// Whether the payment verification was successful
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// Message describing the result
        /// </summary>
        public string Message { get; set; }

        /// <summary>
        /// Payment ID in our system
        /// </summary>
        public int PaymentId { get; set; }

        /// <summary>
        /// Razorpay Order ID
        /// </summary>
        public string OrderId { get; set; }

        /// <summary>
        /// Razorpay Payment ID
        /// </summary>
        public string RazorpayPaymentId { get; set; }

        /// <summary>
        /// Course ID
        /// </summary>
        public int CourseId { get; set; }

        /// <summary>
        /// Course name
        /// </summary>
        public string CourseName { get; set; }

        /// <summary>
        /// Enrollment ID (if enrollment was created)
        /// </summary>
        public int? EnrollmentId { get; set; }
    }
}
