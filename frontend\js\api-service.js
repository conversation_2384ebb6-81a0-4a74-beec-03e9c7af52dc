// API Service for EduVerse Learning Hub
const API_BASE_URL = 'http://localhost:5217';

// Authentication Service
const AuthService = {
    // Register a new user
    register: async (userData) => {
        try {
            const response = await fetch(`${API_BASE_URL}/api/auth/register`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(userData)
            });
            return await response.json();
        } catch (error) {
            console.error('Registration error:', error);
            throw error;
        }
    },

    // Login with credentials
    login: async (credentials) => {
        try {
            const response = await fetch(`${API_BASE_URL}/api/auth/login`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(credentials)
            });
            return await response.json();
        } catch (error) {
            console.error('Login error:', error);
            throw error;
        }
    },

    // Get current user information
    getCurrentUser: async () => {
        try {
            const token = localStorage.getItem('token');
            if (!token) return null;

            const response = await fetch(`${API_BASE_URL}/api/auth/me`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });
            return await response.json();
        } catch (error) {
            console.error('Get current user error:', error);
            throw error;
        }
    },

    // Logout user
    logout: () => {
        localStorage.removeItem('token');
        localStorage.removeItem('user');
    },

    // Check if user is authenticated
    isAuthenticated: () => {
        return localStorage.getItem('token') !== null;
    }
};

// Course Service
const CourseService = {
    // Get all courses
    getAllCourses: async () => {
        try {
            console.log(`Fetching all courses from: ${API_BASE_URL}/api/courses`);
            const response = await fetch(`${API_BASE_URL}/api/courses`);

            if (!response.ok) {
                const errorText = await response.text();
                console.error(`Error response from courses API: ${response.status} ${errorText}`);
                throw new Error(`API Error (${response.status}): ${errorText || 'No error details available'}`);
            }

            const data = await response.json();
            console.log(`Received ${data.length || 0} courses`);
            return data;
        } catch (error) {
            console.error('Get all courses error:', error);

            // For testing purposes, return mock data if the API is not available
            if (error.message.includes('Failed to fetch')) {
                console.log('API server not available, using mock course data');
                return [
                    {
                        id: 1,
                        title: 'Java Programming',
                        description: 'Learn Java programming from scratch',
                        categoryId: 1,
                        price: 49.99,
                        status: 'Active'
                    },
                    {
                        id: 2,
                        title: 'Python Programming',
                        description: 'Learn Python programming from scratch',
                        categoryId: 1,
                        price: 39.99,
                        status: 'Active'
                    },
                    {
                        id: 3,
                        title: 'JavaScript Programming',
                        description: 'Learn JavaScript programming from scratch',
                        categoryId: 1,
                        price: 29.99,
                        status: 'Active'
                    }
                ];
            }

            throw error;
        }
    },

    // Get course by ID
    getCourseById: async (id) => {
        try {
            const response = await fetch(`${API_BASE_URL}/api/courses/${id}`);
            return await response.json();
        } catch (error) {
            console.error(`Get course ${id} error:`, error);
            throw error;
        }
    },

    // Get courses by category
    getCoursesByCategory: async (categoryId) => {
        try {
            const response = await fetch(`${API_BASE_URL}/api/courses/category/${categoryId}`);
            return await response.json();
        } catch (error) {
            console.error(`Get courses by category ${categoryId} error:`, error);
            throw error;
        }
    },

    // Get all categories
    getAllCategories: async () => {
        try {
            const response = await fetch(`${API_BASE_URL}/api/courses/categories`);
            return await response.json();
        } catch (error) {
            console.error('Get all categories error:', error);
            throw error;
        }
    },

    // Get category by ID
    getCategoryById: async (id) => {
        try {
            const response = await fetch(`${API_BASE_URL}/api/courses/categories/${id}`);
            return await response.json();
        } catch (error) {
            console.error(`Get category ${id} error:`, error);
            throw error;
        }
    }
};

// Feedback Service
const FeedbackService = {
    // Submit feedback
    submitFeedback: async (feedbackData) => {
        try {
            console.log('Submitting feedback to:', `${API_BASE_URL}/api/feedback/submit`);
            console.log('Feedback data:', feedbackData);

            // Ensure all required fields are present
            if (!feedbackData.name || !feedbackData.email || !feedbackData.message) {
                console.error('Missing required feedback fields');
                throw new Error('Please fill in all required fields');
            }

            // Format data for the new endpoint (simpler format)
            const formattedData = {
                Name: feedbackData.name,
                Email: feedbackData.email,
                Message: feedbackData.message
                // User ID removed as per requirements
            };

            console.log('Formatted feedback data:', formattedData);

            // Use the new endpoint specifically for frontend submissions
            const response = await fetch(`${API_BASE_URL}/api/feedback/submit`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(formattedData)
            });

            console.log('Response status:', response.status);

            if (!response.ok) {
                const errorText = await response.text();
                console.error('Error response:', errorText);
                throw new Error(`Failed to submit feedback: ${response.status} ${errorText}`);
            }

            return await response.json();
        } catch (error) {
            console.error('Submit feedback error:', error);

            // For testing purposes, return a mock success response
            // Remove this in production
            if (error.message.includes('Failed to fetch')) {
                console.log('API server not available, using mock response');
                return {
                    Id: 999,
                    Name: feedbackData.name,
                    Email: feedbackData.email,
                    Message: feedbackData.message,
                    Response: null,
                    ResponseDate: null,
                    CreatedAt: new Date().toISOString()
                    // User ID removed as per requirements
                };
            }

            throw error;
        }
    }
};

// Quiz Service
const QuizService = {
    // Get all quizzes
    getAllQuizzes: async () => {
        try {
            const response = await fetch(`${API_BASE_URL}/api/quizzes`);
            return await response.json();
        } catch (error) {
            console.error('Get all quizzes error:', error);
            throw error;
        }
    },

    // Get quiz by ID
    getQuizById: async (id) => {
        try {
            const response = await fetch(`${API_BASE_URL}/api/quizzes/${id}`);
            return await response.json();
        } catch (error) {
            console.error(`Get quiz ${id} error:`, error);
            throw error;
        }
    },

    // Get quizzes by course
    getQuizzesByCourse: async (courseId) => {
        try {
            console.log(`Fetching quizzes for course ${courseId} from: ${API_BASE_URL}/api/quizzes/course/${courseId}`);
            const response = await fetch(`${API_BASE_URL}/api/quizzes/course/${courseId}`);

            if (!response.ok) {
                const errorText = await response.text();
                console.error(`Error response from quizzes API: ${response.status} ${errorText}`);
                throw new Error(`API Error (${response.status}): ${errorText || 'No error details available'}`);
            }

            const data = await response.json();
            console.log(`Received ${data.length || 0} quizzes for course ${courseId}`);
            return data;
        } catch (error) {
            console.error(`Get quizzes by course ${courseId} error:`, error);

            // For testing purposes, return mock data if the API is not available
            if (error.message.includes('Failed to fetch')) {
                console.log('API server not available, using mock quiz data');
                return [{
                    id: 999,
                    title: 'Mock Quiz',
                    description: 'This is a mock quiz for testing purposes',
                    courseId: courseId,
                    questions: [
                        {
                            id: 1,
                            questionText: 'What is the capital of France?',
                            optionA: 'London',
                            optionB: 'Paris',
                            optionC: 'Berlin',
                            optionD: 'Madrid',
                            correctOption: 'B'
                        }
                    ]
                }];
            }

            throw error;
        }
    },

    // Submit quiz answers
    submitQuizAnswers: async (attemptId, answers) => {
        try {
            const token = localStorage.getItem('token');
            if (!token) throw new Error('Authentication required');

            const response = await fetch(`${API_BASE_URL}/api/quizzes/submit/${attemptId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`
                },
                body: JSON.stringify({ answers })
            });
            return await response.json();
        } catch (error) {
            console.error('Submit quiz answers error:', error);
            throw error;
        }
    },

    // Start quiz attempt
    startQuizAttempt: async (quizId) => {
        try {
            const token = localStorage.getItem('token');
            if (!token) throw new Error('Authentication required');

            console.log(`Starting quiz attempt for quiz ${quizId} at: ${API_BASE_URL}/api/quizzes/start/${quizId}`);
            console.log('Using token:', token.substring(0, 10) + '...');

            const response = await fetch(`${API_BASE_URL}/api/quizzes/start/${quizId}`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                const errorText = await response.text();
                console.error(`Error response from start quiz API: ${response.status} ${errorText}`);
                throw new Error(`API Error (${response.status}): ${errorText || 'No error details available'}`);
            }

            const data = await response.json();
            console.log('Quiz attempt started successfully:', data);
            return data;
        } catch (error) {
            console.error(`Start quiz ${quizId} attempt error:`, error);

            // For testing purposes, return mock data if the API is not available
            if (error.message.includes('Failed to fetch')) {
                console.log('API server not available, using mock quiz attempt data');
                return {
                    id: 999,
                    quizId: quizId,
                    userId: 1,
                    startTime: new Date().toISOString(),
                    status: 'InProgress'
                };
            }

            throw error;
        }
    }
};
