// Settings Management JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Initialize the page
    initSettingsPage();
    
    // Setup event listeners
    setupEventListeners();
    
    // Update admin info
    updateAdminInfo();
});

// Initialize the settings page
function initSettingsPage() {
    try {
        // Load settings
        loadSettings();
        
        // Setup tabs
        setupTabs();
    } catch (error) {
        console.error('Error initializing settings page:', error);
    }
}

// Load settings from localStorage or default values
function loadSettings() {
    // General settings
    const generalSettings = JSON.parse(localStorage.getItem('generalSettings')) || {
        siteName: 'EduVerse Learning Hub',
        siteDescription: 'EduVerse is an online learning platform offering courses in programming, data science, design, and more.',
        contactEmail: '<EMAIL>',
        contactPhone: '+****************',
        facebookUrl: 'https://facebook.com/eduverse',
        twitterUrl: 'https://twitter.com/eduverse',
        instagramUrl: 'https://instagram.com/eduverse',
        linkedinUrl: 'https://linkedin.com/company/eduverse'
    };
    
    // Appearance settings
    const appearanceSettings = JSON.parse(localStorage.getItem('appearanceSettings')) || {
        themeMode: 'light',
        primaryColor: '#DF2771',
        compactSidebar: false,
        fixedHeader: true
    };
    
    // Email settings
    const emailSettings = JSON.parse(localStorage.getItem('emailSettings')) || {
        smtpHost: 'smtp.example.com',
        smtpPort: 587,
        smtpUsername: '<EMAIL>',
        smtpPassword: '********',
        smtpEncryption: true,
        newUserNotification: true,
        newPurchaseNotification: true,
        newFeedbackNotification: true
    };
    
    // Payment settings
    const paymentSettings = JSON.parse(localStorage.getItem('paymentSettings')) || {
        paypalEnabled: true,
        paypalClientId: 'YOUR_PAYPAL_CLIENT_ID',
        paypalSecret: '********',
        stripeEnabled: true,
        stripePublicKey: 'pk_test_XXXXXXXXXXXXXXXXXXXXXXXX',
        stripeSecretKey: '********',
        defaultCurrency: 'USD',
        currencyPosition: 'before'
    };
    
    // Security settings
    const securitySettings = JSON.parse(localStorage.getItem('securitySettings')) || {
        twoFactorAuth: false
    };
    
    // Populate form fields
    populateGeneralSettings(generalSettings);
    populateAppearanceSettings(appearanceSettings);
    populateEmailSettings(emailSettings);
    populatePaymentSettings(paymentSettings);
    populateSecuritySettings(securitySettings);
}

// Populate general settings form
function populateGeneralSettings(settings) {
    document.getElementById('site-name').value = settings.siteName;
    document.getElementById('site-description').value = settings.siteDescription;
    document.getElementById('contact-email').value = settings.contactEmail;
    document.getElementById('contact-phone').value = settings.contactPhone;
    document.getElementById('facebook-url').value = settings.facebookUrl;
    document.getElementById('twitter-url').value = settings.twitterUrl;
    document.getElementById('instagram-url').value = settings.instagramUrl;
    document.getElementById('linkedin-url').value = settings.linkedinUrl;
}

// Populate appearance settings form
function populateAppearanceSettings(settings) {
    document.getElementById('theme-mode').value = settings.themeMode;
    
    // Set primary color
    const colorOptions = document.querySelectorAll('.color-option');
    colorOptions.forEach(option => {
        option.classList.remove('active');
        if (option.getAttribute('data-color') === settings.primaryColor) {
            option.classList.add('active');
        }
    });
    
    document.getElementById('compact-sidebar').checked = settings.compactSidebar;
    document.getElementById('fixed-header').checked = settings.fixedHeader;
    
    // Apply theme
    applyTheme(settings);
}

// Populate email settings form
function populateEmailSettings(settings) {
    document.getElementById('smtp-host').value = settings.smtpHost;
    document.getElementById('smtp-port').value = settings.smtpPort;
    document.getElementById('smtp-username').value = settings.smtpUsername;
    document.getElementById('smtp-password').value = settings.smtpPassword;
    document.getElementById('smtp-encryption').checked = settings.smtpEncryption;
    document.getElementById('new-user-notification').checked = settings.newUserNotification;
    document.getElementById('new-purchase-notification').checked = settings.newPurchaseNotification;
    document.getElementById('new-feedback-notification').checked = settings.newFeedbackNotification;
}

// Populate payment settings form
function populatePaymentSettings(settings) {
    document.getElementById('paypal-enabled').checked = settings.paypalEnabled;
    document.getElementById('paypal-client-id').value = settings.paypalClientId;
    document.getElementById('paypal-secret').value = settings.paypalSecret;
    document.getElementById('stripe-enabled').checked = settings.stripeEnabled;
    document.getElementById('stripe-public-key').value = settings.stripePublicKey;
    document.getElementById('stripe-secret-key').value = settings.stripeSecretKey;
    document.getElementById('default-currency').value = settings.defaultCurrency;
    document.getElementById('currency-position').value = settings.currencyPosition;
}

// Populate security settings form
function populateSecuritySettings(settings) {
    document.getElementById('two-factor-auth').checked = settings.twoFactorAuth;
    
    // Show/hide two-factor setup
    if (settings.twoFactorAuth) {
        document.getElementById('two-factor-setup').style.display = 'block';
    } else {
        document.getElementById('two-factor-setup').style.display = 'none';
    }
}

// Apply theme settings
function applyTheme(settings) {
    // Apply primary color
    document.documentElement.style.setProperty('--primary-color', settings.primaryColor);
    
    // Apply theme mode
    if (settings.themeMode === 'dark') {
        document.body.classList.add('dark-mode');
    } else {
        document.body.classList.remove('dark-mode');
    }
    
    // Apply layout settings
    if (settings.compactSidebar) {
        document.querySelector('.admin-container').classList.add('sidebar-collapsed');
    } else {
        document.querySelector('.admin-container').classList.remove('sidebar-collapsed');
    }
    
    if (settings.fixedHeader) {
        document.querySelector('.top-bar').classList.add('fixed-header');
    } else {
        document.querySelector('.top-bar').classList.remove('fixed-header');
    }
}

// Setup tabs
function setupTabs() {
    const tabs = document.querySelectorAll('.tab');
    const tabContents = document.querySelectorAll('.tab-content');
    
    tabs.forEach(tab => {
        tab.addEventListener('click', function() {
            // Remove active class from all tabs
            tabs.forEach(t => t.classList.remove('active'));
            
            // Add active class to clicked tab
            this.classList.add('active');
            
            // Hide all tab contents
            tabContents.forEach(content => content.classList.remove('active'));
            
            // Show the corresponding tab content
            const tabId = this.getAttribute('data-tab');
            document.getElementById(`${tabId}-tab`).classList.add('active');
        });
    });
}

// Save general settings
function saveGeneralSettings(event) {
    event.preventDefault();
    
    const settings = {
        siteName: document.getElementById('site-name').value,
        siteDescription: document.getElementById('site-description').value,
        contactEmail: document.getElementById('contact-email').value,
        contactPhone: document.getElementById('contact-phone').value,
        facebookUrl: document.getElementById('facebook-url').value,
        twitterUrl: document.getElementById('twitter-url').value,
        instagramUrl: document.getElementById('instagram-url').value,
        linkedinUrl: document.getElementById('linkedin-url').value
    };
    
    localStorage.setItem('generalSettings', JSON.stringify(settings));
    
    alert('General settings saved successfully!');
}

// Save appearance settings
function saveAppearanceSettings(event) {
    event.preventDefault();
    
    const activeColorOption = document.querySelector('.color-option.active');
    
    const settings = {
        themeMode: document.getElementById('theme-mode').value,
        primaryColor: activeColorOption ? activeColorOption.getAttribute('data-color') : '#DF2771',
        compactSidebar: document.getElementById('compact-sidebar').checked,
        fixedHeader: document.getElementById('fixed-header').checked
    };
    
    localStorage.setItem('appearanceSettings', JSON.stringify(settings));
    
    // Apply theme
    applyTheme(settings);
    
    alert('Appearance settings saved successfully!');
}

// Save email settings
function saveEmailSettings(event) {
    event.preventDefault();
    
    const settings = {
        smtpHost: document.getElementById('smtp-host').value,
        smtpPort: parseInt(document.getElementById('smtp-port').value),
        smtpUsername: document.getElementById('smtp-username').value,
        smtpPassword: document.getElementById('smtp-password').value,
        smtpEncryption: document.getElementById('smtp-encryption').checked,
        newUserNotification: document.getElementById('new-user-notification').checked,
        newPurchaseNotification: document.getElementById('new-purchase-notification').checked,
        newFeedbackNotification: document.getElementById('new-feedback-notification').checked
    };
    
    localStorage.setItem('emailSettings', JSON.stringify(settings));
    
    alert('Email settings saved successfully!');
}

// Save payment settings
function savePaymentSettings(event) {
    event.preventDefault();
    
    const settings = {
        paypalEnabled: document.getElementById('paypal-enabled').checked,
        paypalClientId: document.getElementById('paypal-client-id').value,
        paypalSecret: document.getElementById('paypal-secret').value,
        stripeEnabled: document.getElementById('stripe-enabled').checked,
        stripePublicKey: document.getElementById('stripe-public-key').value,
        stripeSecretKey: document.getElementById('stripe-secret-key').value,
        defaultCurrency: document.getElementById('default-currency').value,
        currencyPosition: document.getElementById('currency-position').value
    };
    
    localStorage.setItem('paymentSettings', JSON.stringify(settings));
    
    alert('Payment settings saved successfully!');
}

// Save security settings
function saveSecuritySettings(event) {
    event.preventDefault();
    
    const currentPassword = document.getElementById('current-password').value;
    const newPassword = document.getElementById('new-password').value;
    const confirmPassword = document.getElementById('confirm-password').value;
    
    // Validate password change
    if (currentPassword || newPassword || confirmPassword) {
        if (!currentPassword) {
            alert('Please enter your current password.');
            return;
        }
        
        if (newPassword !== confirmPassword) {
            alert('New password and confirm password do not match.');
            return;
        }
        
        // In a real application, you would verify the current password and update it
        alert('Password updated successfully!');
    }
    
    const settings = {
        twoFactorAuth: document.getElementById('two-factor-auth').checked
    };
    
    localStorage.setItem('securitySettings', JSON.stringify(settings));
    
    alert('Security settings saved successfully!');
}

// Test email
function testEmail() {
    alert('Test email sent to ' + document.getElementById('smtp-username').value);
}

// Verify two-factor authentication code
function verifyTwoFactorCode() {
    const code = document.getElementById('verification-code').value;
    
    if (!code) {
        alert('Please enter the verification code.');
        return;
    }
    
    // In a real application, you would verify the code
    if (code === '123456') {
        alert('Two-factor authentication enabled successfully!');
    } else {
        alert('Invalid verification code. Please try again.');
    }
}

// Reset general settings
function resetGeneralSettings() {
    if (confirm('Are you sure you want to reset general settings to default?')) {
        localStorage.removeItem('generalSettings');
        loadSettings();
        alert('General settings reset to default.');
    }
}

// Reset appearance settings
function resetAppearanceSettings() {
    if (confirm('Are you sure you want to reset appearance settings to default?')) {
        localStorage.removeItem('appearanceSettings');
        loadSettings();
        alert('Appearance settings reset to default.');
    }
}

// Reset payment settings
function resetPaymentSettings() {
    if (confirm('Are you sure you want to reset payment settings to default?')) {
        localStorage.removeItem('paymentSettings');
        loadSettings();
        alert('Payment settings reset to default.');
    }
}

// Setup event listeners
function setupEventListeners() {
    // Form submissions
    document.getElementById('general-settings-form').addEventListener('submit', saveGeneralSettings);
    document.getElementById('appearance-settings-form').addEventListener('submit', saveAppearanceSettings);
    document.getElementById('email-settings-form').addEventListener('submit', saveEmailSettings);
    document.getElementById('payment-settings-form').addEventListener('submit', savePaymentSettings);
    document.getElementById('security-settings-form').addEventListener('submit', saveSecuritySettings);
    
    // Reset buttons
    document.getElementById('reset-general-btn').addEventListener('click', resetGeneralSettings);
    document.getElementById('reset-appearance-btn').addEventListener('click', resetAppearanceSettings);
    document.getElementById('reset-payment-btn').addEventListener('click', resetPaymentSettings);
    
    // Test email button
    document.getElementById('test-email-btn').addEventListener('click', testEmail);
    
    // Two-factor authentication toggle
    document.getElementById('two-factor-auth').addEventListener('change', function() {
        const twoFactorSetup = document.getElementById('two-factor-setup');
        if (this.checked) {
            twoFactorSetup.style.display = 'block';
        } else {
            twoFactorSetup.style.display = 'none';
        }
    });
    
    // Verify code button
    document.getElementById('verify-code-btn').addEventListener('click', verifyTwoFactorCode);
    
    // Color options
    document.querySelectorAll('.color-option').forEach(option => {
        option.addEventListener('click', function() {
            // Remove active class from all options
            document.querySelectorAll('.color-option').forEach(opt => opt.classList.remove('active'));
            
            // Add active class to clicked option
            this.classList.add('active');
            
            // Apply color
            const color = this.getAttribute('data-color');
            document.documentElement.style.setProperty('--primary-color', color);
        });
    });
    
    // Toggle sidebar
    document.querySelector('.toggle-menu').addEventListener('click', function() {
        document.querySelector('.admin-container').classList.toggle('sidebar-collapsed');
        
        // Update compact sidebar setting
        const compactSidebar = document.querySelector('.admin-container').classList.contains('sidebar-collapsed');
        document.getElementById('compact-sidebar').checked = compactSidebar;
    });
    
    // Admin logout
    document.getElementById('admin-logout').addEventListener('click', function(event) {
        event.preventDefault();
        
        // Clear localStorage
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        
        // Redirect to login page
        window.location.href = '../admin-login.html';
    });
}

// Update admin info
function updateAdminInfo() {
    const user = JSON.parse(localStorage.getItem('user') || '{}');
    const adminNameElement = document.querySelector('.admin-name');
    
    if (adminNameElement && user.fullName) {
        adminNameElement.textContent = user.fullName;
    }
}
