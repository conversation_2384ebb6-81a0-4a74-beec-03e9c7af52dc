using System;

namespace EduVerse.Application.DTOs.Admin
{
    /// <summary>
    /// DTO for payment information
    /// </summary>
    public class PaymentDto
    {
        /// <summary>
        /// Payment ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// User ID
        /// </summary>
        public int UserId { get; set; }

        /// <summary>
        /// User name
        /// </summary>
        public string UserName { get; set; }

        /// <summary>
        /// Course ID
        /// </summary>
        public int CourseId { get; set; }

        /// <summary>
        /// Course name
        /// </summary>
        public string CourseName { get; set; }

        /// <summary>
        /// Payment amount
        /// </summary>
        public decimal Amount { get; set; }

        /// <summary>
        /// Payment date
        /// </summary>
        public DateTime Date { get; set; }

        /// <summary>
        /// Payment method
        /// </summary>
        public string PaymentMethod { get; set; }

        /// <summary>
        /// Payment status
        /// </summary>
        public string Status { get; set; }

        /// <summary>
        /// Transaction ID
        /// </summary>
        public string TransactionId { get; set; }

        /// <summary>
        /// Billing address
        /// </summary>
        public string BillingAddress { get; set; }
    }
}
