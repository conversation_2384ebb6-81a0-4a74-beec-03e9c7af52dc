(function(sttc){'use strict';var ba,ca=Object.defineProperty,ea=globalThis,fa=typeof Symbol==="function"&&typeof Symbol("x")==="symbol",ha={},ia={};function ja(a,b,c){if(!c||a!=null){c=ia[b];if(c==null)return a[b];c=a[c];return c!==void 0?c:a[b]}} 
function ka(a,b,c){if(b)a:{var d=a.split(".");a=d.length===1;var e=d[0],f;!a&&e in ha?f=ha:f=ea;for(e=0;e<d.length-1;e++){var g=d[e];if(!(g in f))break a;f=f[g]}d=d[d.length-1];c=fa&&c==="es6"?f[d]:null;b=b(c);b!=null&&(a?ca(ha,d,{configurable:!0,writable:!0,value:b}):b!==c&&(ia[d]===void 0&&(a=Math.random()*1E9>>>0,ia[d]=fa?ea.Symbol(d):"$jscp$"+a+"$"+d),ca(f,ia[d],{configurable:!0,writable:!0,value:b})))}}var na=Object.create,oa=Object.setPrototypeOf; 
function pa(a,b){a.prototype=na(b.prototype);a.prototype.constructor=a;oa(a,b);a.ll=b.prototype}function q(a){return a}ka("Symbol.dispose",function(a){return a?a:Symbol("Symbol.dispose")},"es_next"); 
ka("String.prototype.replaceAll",function(a){return a?a:function(b,c){if(b instanceof RegExp&&!b.global)throw new TypeError("String.prototype.replaceAll called with a non-global RegExp argument.");return b instanceof RegExp?this.replace(b,c):this.replace(new RegExp(String(b).replace(/([-()\[\]{}+?*.$\^|,:#<!\\])/g,"\\$1").replace(/\x08/g,"\\x08"),"g"),c)}},"es_2021"); 
ka("AggregateError",function(a){function b(c,d){d=Error(d);"stack"in d&&(this.stack=d.stack);this.errors=c;this.message=d.message}if(a)return a;pa(b,Error);b.prototype.name="AggregateError";return b},"es_2021"); 
ka("Promise.any",function(a){return a?a:function(b){b=b instanceof Array?b:Array.from(b);return Promise.all(b.map(function(c){return Promise.resolve(c).then(function(d){throw d;},function(d){return d})})).then(function(c){throw new ha.AggregateError(c,"All promises were rejected");},function(c){return c})}},"es_2021");/* 
 
 Copyright The Closure Library Authors. 
 SPDX-License-Identifier: Apache-2.0 
*/ 
var r=this||self;function ra(a){var b=typeof a;return b!="object"?b:a?Array.isArray(a)?"array":b:"null"}function sa(a){var b=ra(a);return b=="array"||b=="object"&&typeof a.length=="number"}function ta(a){var b=typeof a;return b=="object"&&a!=null||b=="function"}function ua(a){return Object.prototype.hasOwnProperty.call(a,va)&&a[va]||(a[va]=++wa)}var va="closure_uid_"+(Math.random()*1E9>>>0),wa=0;function za(a,b,c){return a.call.apply(a.bind,arguments)} 
function Ba(a,b,c){if(!a)throw Error();if(arguments.length>2){var d=Array.prototype.slice.call(arguments,2);return function(){var e=Array.prototype.slice.call(arguments);Array.prototype.unshift.apply(e,d);return a.apply(b,e)}}return function(){return a.apply(b,arguments)}}function Da(a,b,c){Da=Function.prototype.bind&&Function.prototype.bind.toString().indexOf("native code")!=-1?za:Ba;return Da.apply(null,arguments)} 
function Ea(a,b){var c=Array.prototype.slice.call(arguments,1);return function(){var d=c.slice();d.push.apply(d,arguments);return a.apply(this,d)}}function Ha(a,b,c){a=a.split(".");c=c||r;for(var d;a.length&&(d=a.shift());)a.length||b===void 0?c[d]&&c[d]!==Object.prototype[d]?c=c[d]:c=c[d]={}:c[d]=b}function Ia(a){return a} 
function Ja(a,b){function c(){}c.prototype=b.prototype;a.ll=b.prototype;a.prototype=new c;a.prototype.constructor=a;a.ip=function(d,e,f){for(var g=Array(arguments.length-2),h=2;h<arguments.length;h++)g[h-2]=arguments[h];return b.prototype[e].apply(d,g)}};var Ka={mo:0,lo:1,ko:2};var La;function Ma(a,b){if(typeof a==="string")return typeof b!=="string"||b.length!=1?-1:a.indexOf(b,0);for(let c=0;c<a.length;c++)if(c in a&&a[c]===b)return c;return-1}function Na(a,b){const c=a.length,d=typeof a==="string"?a.split(""):a;for(let e=0;e<c;e++)e in d&&b.call(void 0,d[e],e,a)}function Oa(a,b){var c=a.length;const d=typeof a==="string"?a.split(""):a;for(--c;c>=0;--c)c in d&&b.call(void 0,d[c],c,a)} 
function Qa(a,b){const c=a.length,d=[];let e=0;const f=typeof a==="string"?a.split(""):a;for(let g=0;g<c;g++)if(g in f){const h=f[g];b.call(void 0,h,g,a)&&(d[e++]=h)}return d}function Va(a,b){const c=a.length,d=Array(c),e=typeof a==="string"?a.split(""):a;for(let f=0;f<c;f++)f in e&&(d[f]=b.call(void 0,e[f],f,a));return d}function Wa(a,b){let c=1;Na(a,function(d,e){c=b.call(void 0,c,d,e,a)});return c} 
function Xa(a,b){const c=a.length,d=typeof a==="string"?a.split(""):a;for(let e=0;e<c;e++)if(e in d&&b.call(void 0,d[e],e,a))return!0;return!1}function Za(a,b){return Ma(a,b)>=0}function $a(a,b){b=Ma(a,b);let c;(c=b>=0)&&Array.prototype.splice.call(a,b,1);return c}function ab(a,b){let c=0;Oa(a,function(d,e){b.call(void 0,d,e,a)&&Array.prototype.splice.call(a,e,1).length==1&&c++})}function bb(a){return Array.prototype.concat.apply([],arguments)} 
function eb(a){const b=a.length;if(b>0){const c=Array(b);for(let d=0;d<b;d++)c[d]=a[d];return c}return[]}function fb(a,b){for(let c=1;c<arguments.length;c++){const d=arguments[c];if(sa(d)){const e=a.length||0,f=d.length||0;a.length=e+f;for(let g=0;g<f;g++)a[e+g]=d[g]}else a.push(d)}}function gb(a,b,c){return arguments.length<=2?Array.prototype.slice.call(a,b):Array.prototype.slice.call(a,b,c)} 
function hb(a,b,c){c=c||jb;let d=0,e=a.length,f;for(;d<e;){const g=d+(e-d>>>1);let h;h=c(b,a[g]);h>0?d=g+1:(e=g,f=!h)}return f?d:-d-1}function jb(a,b){return a>b?1:a<b?-1:0}function kb(a){const b=[];for(let c=0;c<arguments.length;c++){const d=arguments[c];if(Array.isArray(d))for(let e=0;e<d.length;e+=8192){const f=kb.apply(null,gb(d,e,e+8192));for(let g=0;g<f.length;g++)b.push(f[g])}else b.push(d)}return b} 
function lb(a,b){b=b||Math.random;for(let c=a.length-1;c>0;c--){const d=Math.floor(b()*(c+1)),e=a[c];a[c]=a[d];a[d]=e}};var mb={Il:"google_adtest",Ml:"google_ad_client",Wl:"google_ad_intent_query",Vl:"google_ad_intent_qetid",Ul:"google_ad_intent_eids",Tl:"google_ad_intents_format",Nl:"google_ad_format",Pl:"google_ad_height",im:"google_ad_width",Xl:"google_ad_layout",Yl:"google_ad_layout_key",am:"google_ad_output",bm:"google_ad_region",em:"google_ad_slot",gm:"google_ad_type",hm:"google_ad_url",Pm:"google_gl",Xm:"google_enable_ose",jn:"google_full_width_responsive",po:"google_rl_filtering",oo:"google_rl_mode",qo:"google_rt", 
no:"google_rl_dest_url",Sn:"google_max_radlink_len",Xn:"google_num_radlinks",Yn:"google_num_radlinks_per_unit",Ll:"google_ad_channel",Rn:"google_max_num_ads",Tn:"google_max_responsive_height",Cm:"google_color_border",Wm:"google_enable_content_recommendations",Mm:"google_content_recommendation_ui_type",Lm:"google_source_type",Km:"google_content_recommendation_rows_num",Jm:"google_content_recommendation_columns_num",Im:"google_content_recommendation_ad_positions",Nm:"google_content_recommendation_use_square_imgs", 
Em:"google_color_link",Dm:"google_color_line",Gm:"google_color_url",Jl:"google_ad_block",dm:"google_ad_section",Kl:"google_ad_callback",zm:"google_captcha_token",Fm:"google_color_text",um:"google_alternate_ad_url",Sl:"google_ad_host_tier_id",Am:"google_city",Ql:"google_ad_host",Rl:"google_ad_host_channel",vm:"google_alternate_color",Bm:"google_color_bg",Ym:"google_encoding",gn:"google_font_face",ln:"google_hints",Dn:"google_image_size",Un:"google_mtl",No:"google_cpm",Hm:"google_contents",Vn:"google_native_settings_key", 
Om:"google_country",Lo:"google_targeting",hn:"google_font_size",Um:"google_disable_video_autoplay",bp:"google_video_product_type",ap:"google_video_doc_id",Zo:"google_cust_gender",Fo:"google_cust_lh",Eo:"google_cust_l",Mo:"google_tfs",Kn:"google_kw",Io:"google_tag_for_child_directed_treatment",Jo:"google_tag_for_under_age_of_consent",so:"google_region",Rm:"google_cust_criteria",cm:"google_safe",Qm:"google_ctr_threshold",vo:"google_resizing_allowed",xo:"google_resizing_width",wo:"google_resizing_height", 
Yo:"google_cust_age",Nn:"google_language",Ln:"google_kw_type",fo:"google_pucrd",co:"google_page_url",Ko:"google_tag_partner",Bo:"google_restrict_data_processing",El:"google_adbreak_test",Ol:"google_ad_frequency_hint",Gl:"google_admob_interstitial_slot",Hl:"google_admob_rewarded_slot",Fl:"google_admob_ads_only",fm:"google_ad_start_delay_hint",Qn:"google_max_ad_content_rating",jo:"google_ad_public_floor",ho:"google_ad_private_floor",Wo:"google_traffic_source",ao:"google_overlays",eo:"google_privacy_treatments", 
Go:"google_special_category_data",cp:"google_wrap_fullscreen_ad",Zl:"google_ad_loaded_callback"};function nb(a){var b=ob;if(!pb(a))throw b=(typeof b==="function"?b():b)?.concat("\n")??"",Error(b+String(a));}function qb(a){return a}function rb(a){a.qp=!0;return a}let ob=void 0;var sb=rb(a=>typeof a==="number"),pb=rb(a=>typeof a==="string");function tb(){var a=ub;return rb(b=>{for(const c in a)if(b===a[c]&&!/^[0-9]+$/.test(c))return!0;return!1})}var vb=rb(a=>!!a&&(typeof a==="object"||typeof a==="function"));function wb(){var a=pb;return xb(rb((b,c)=>b===void 0?!0:a(b,c)))}function xb(a){a.lk=!0;return a}var yb=rb(a=>Array.isArray(a));function Bb(){return rb(a=>yb(a)?a.every(b=>sb(b)):!1)};let Cb,Db=64;function Eb(){try{return Cb??(Cb=new Uint32Array(64)),Db>=64&&(crypto.getRandomValues(Cb),Db=0),Cb[Db++]}catch(a){return Math.floor(Math.random()*2**32)}};function Fb(a,b){if(!sb(a.goog_pvsid))try{const c=Eb()+(Eb()&2**21-1)*2**32;Object.defineProperty(a,"goog_pvsid",{value:c,configurable:!1})}catch(c){b.fe({methodName:784,Kd:c})}a=Number(a.goog_pvsid);(!a||a<=0)&&b.fe({methodName:784,Kd:Error(`Invalid correlator, ${a}`)});return a||-1};var Gb,Hb;a:{for(var Mb=["CLOSURE_FLAGS"],Nb=r,Ob=0;Ob<Mb.length;Ob++)if(Nb=Nb[Mb[Ob]],Nb==null){Hb=null;break a}Hb=Nb}var Pb=Hb&&Hb[610401301];Gb=Pb!=null?Pb:!1;function Qb(a){return/^[\s\xa0]*([\s\S]*?)[\s\xa0]*$/.exec(a)[1]}function Rb(a,b){return a.toLowerCase().indexOf(b.toLowerCase())!=-1};function Tb(){var a=r.navigator;return a&&(a=a.userAgent)?a:""}var Ub;const Vb=r.navigator;Ub=Vb?Vb.userAgentData||null:null;function Wb(a){if(!Gb||!Ub)return!1;for(let b=0;b<Ub.brands.length;b++){const {brand:c}=Ub.brands[b];if(c&&c.indexOf(a)!=-1)return!0}return!1}function Xb(a){return Tb().indexOf(a)!=-1};function Yb(){return Gb?!!Ub&&Ub.brands.length>0:!1}function Zb(){return Yb()?!1:Xb("Opera")}function $b(){return Xb("Firefox")||Xb("FxiOS")}function ac(){return Xb("Safari")&&!(bc()||(Yb()?0:Xb("Coast"))||Zb()||(Yb()?0:Xb("Edge"))||(Yb()?Wb("Microsoft Edge"):Xb("Edg/"))||(Yb()?Wb("Opera"):Xb("OPR"))||$b()||Xb("Silk")||Xb("Android"))}function bc(){return Yb()?Wb("Chromium"):(Xb("Chrome")||Xb("CriOS"))&&!(Yb()?0:Xb("Edge"))||Xb("Silk")};function cc(a){cc[" "](a);return a}cc[" "]=function(){};function dc(a,b){try{return cc(a[b]),!0}catch(c){}return!1};var ec=Yb()?!1:Xb("Trident")||Xb("MSIE"),fc=Xb("Edge")||ec,gc=Xb("Gecko")&&!(Rb(Tb(),"WebKit")&&!Xb("Edge"))&&!(Xb("Trident")||Xb("MSIE"))&&!Xb("Edge"),hc=Rb(Tb(),"WebKit")&&!Xb("Edge");function oc(){return!1}function pc(){return!0}function qc(a){const b=arguments,c=b.length;return function(){for(let d=0;d<c;d++)if(!b[d].apply(this,arguments))return!1;return!0}}function rc(a){return function(){return!a.apply(this,arguments)}}function sc(a){let b=!1,c;return function(){b||(c=a(),b=!0);return c}}function tc(a){let b=a;return function(){if(b){const c=b;b=null;c()}}} 
function uc(a,b){let c=0;return function(d){r.clearTimeout(c);const e=arguments;c=r.setTimeout(function(){a.apply(b,e)},63)}}function vc(a,b){function c(){e=r.setTimeout(d,63);let h=g;g=[];a.apply(b,h)}function d(){e=0;f&&(f=!1,c())}let e=0,f=!1,g=[];return function(h){g=arguments;e?f=!0:c()}};function wc(){return Gb&&Ub?Ub.mobile:!xc()&&(Xb("iPod")||Xb("iPhone")||Xb("Android")||Xb("IEMobile"))}function xc(){return Gb&&Ub?!Ub.mobile&&(Xb("iPad")||Xb("Android")||Xb("Silk")):Xb("iPad")||Xb("Android")&&!Xb("Mobile")||Xb("Silk")};function yc(a,b){const c={};for(const d in a)b.call(void 0,a[d],d,a)&&(c[d]=a[d]);return c}function zc(a,b){for(const c in a)if(b.call(void 0,a[c],c,a))return!0;return!1}function Ac(a){var b=Bc;a:{for(const c in b)if(b[c]==a){a=!0;break a}a=!1}return a}function Cc(a){const b=[];let c=0;for(const d in a)b[c++]=a[d];return b}function Dc(a){const b={};for(const c in a)b[c]=a[c];return b}const Ec="constructor hasOwnProperty isPrototypeOf propertyIsEnumerable toLocaleString toString valueOf".split(" "); 
function Fc(a,b){let c,d;for(let e=1;e<arguments.length;e++){d=arguments[e];for(c in d)a[c]=d[c];for(let f=0;f<Ec.length;f++)c=Ec[f],Object.prototype.hasOwnProperty.call(d,c)&&(a[c]=d[c])}};/* 
 
 Copyright Google LLC 
 SPDX-License-Identifier: Apache-2.0 
*/ 
let Gc=globalThis.trustedTypes,Jc;function Kc(){let a=null;if(!Gc)return a;try{const b=c=>c;a=Gc.createPolicy("goog#html",{createHTML:b,createScript:b,createScriptURL:b})}catch(b){}return a}function Lc(){Jc===void 0&&(Jc=Kc());return Jc};var Mc=class{constructor(a){this.g=a}toString(){return this.g+""}};function Nc(a){const b=Lc();a=b?b.createScriptURL(a):a;return new Mc(a)}function Oc(a){if(a instanceof Mc)return a.g;throw Error("");};var Pc=class{constructor(a){this.g=a}toString(){return this.g}},Qc=new Pc("about:invalid#zClosurez");function Rc(a){return a instanceof Pc}function Sc(a){if(Rc(a))return a.g;throw Error("");};class Uc{constructor(a){this.pk=a}}function Vc(a){return new Uc(b=>b.substr(0,a.length+1).toLowerCase()===a+":")}const Wc=[Vc("data"),Vc("http"),Vc("https"),Vc("mailto"),Vc("ftp"),new Uc(a=>/^[^:]*([/?#]|$)/.test(a))];function Xc(a,b=Wc){if(Rc(a))return a;for(let c=0;c<b.length;++c){const d=b[c];if(d instanceof Uc&&d.pk(a))return new Pc(a)}}var Yc=/^\s*(?!javascript:)(?:[\w+.-]+:|[^:/?#]*(?:[/?#]|$))/i;function Zc(a){if(Yc.test(a))return a};function $c(a){var b=Zc("#");b!==void 0&&(a.href=b)};function ad(a,b=`unexpected value ${a}!`){throw Error(b);};var bd=class{constructor(a){this.g=a}toString(){return this.g+""}};function cd(a){const b=Lc();a=b?b.createHTML(a):a;return new bd(a)}function dd(a){if(a instanceof bd)return a.g;throw Error("");};function ed(a=document){a=a.querySelector?.("script[nonce]");return a==null?"":a.nonce||a.getAttribute("nonce")||""};function fd(a,b){a.src=Oc(b);(b=ed(a.ownerDocument))&&a.setAttribute("nonce",b)};var gd=class{constructor(a){this.g=a}toString(){return this.g}};function hd(a,b){if(a.nodeType===1&&/^(script|style)$/i.test(a.tagName))throw Error("");a.innerHTML=dd(b)}function id(a,b,c){var d=[jd`width`,jd`height`];if(d.length===0)throw Error("");d=d.map(f=>{if(f instanceof gd)f=f.g;else throw Error("");return f});const e=b.toLowerCase();if(d.every(f=>e.indexOf(f)!==0))throw Error(`Attribute "${b}" does not match any of the allowed prefixes.`);a.setAttribute(b,c)};var kd=class{constructor(a){this.g=a}toString(){return this.g}};function ld(a){if(a instanceof kd)return a.g;throw Error("");};function md(a,b){a.__closure__error__context__984382||(a.__closure__error__context__984382={});a.__closure__error__context__984382.severity=b};function nd(a,b){const c={"&amp;":"&","&lt;":"<","&gt;":">","&quot;":'"'};let d;d=b?b.createElement("div"):r.document.createElement("div");return a.replace(td,function(e,f){let g=c[e];if(g)return g;f.charAt(0)=="#"&&(f=Number("0"+f.slice(1)),isNaN(f)||(g=String.fromCharCode(f)));g||(hd(d,cd(e+" ")),g=d.firstChild.nodeValue.slice(0,-1));return c[e]=g})}var td=/&([^;\s<&]+);?/g;function ud(a){let b=0;for(let c=0;c<a.length;++c)b=31*b+a.charCodeAt(c)>>>0;return b} 
function vd(a){return String(a).replace(/\-([a-z])/g,function(b,c){return c.toUpperCase()})}function wd(a){return a.replace(RegExp("(^|[\\s]+)([a-z])","g"),function(b,c,d){return c+d.toUpperCase()})};var xd=RegExp("^(?:([^:/?#.]+):)?(?://(?:([^\\\\/?#]*)@)?([^\\\\/?#]*?)(?::([0-9]+))?(?=[\\\\/?#]|$))?([^?#]+)?(?:\\?([^#]*))?(?:#([\\s\\S]*))?$"),yd=/#|$/;function zd(a,b){const c=a.search(yd);a:{var d=0;for(var e=b.length;(d=a.indexOf(b,d))>=0&&d<c;){var f=a.charCodeAt(d-1);if(f==38||f==63)if(f=a.charCodeAt(d+e),!f||f==61||f==38||f==35)break a;d+=e+1}d=-1}if(d<0)return null;e=a.indexOf("&",d);if(e<0||e>c)e=c;d+=b.length+1;return decodeURIComponent(a.slice(d,e!==-1?e:0).replace(/\+/g," "))};function jd(a){return new gd(a[0].toLowerCase())};function Ad(a){return new kd(a[0])};function Bd(a){return a instanceof bd?a:cd(Cd(String(a)))}function Cd(a){return a.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;")}function Dd(a){return Ed(a)}function Ed(a){const b=Bd("");return cd(a.map(c=>dd(Bd(c))).join(dd(b).toString()))}const Fd=/^[a-z][a-z\d-]*$/i,Gd="APPLET BASE EMBED IFRAME LINK MATH META OBJECT SCRIPT STYLE SVG TEMPLATE".split(" ");var Hd="AREA BR COL COMMAND HR IMG INPUT KEYGEN PARAM SOURCE TRACK WBR".split(" "); 
const Id=["action","formaction","href"];function Jd(a){if(!Fd.test(a))throw Error("");if(Gd.indexOf(a.toUpperCase())!==-1)throw Error("");}function Kd(a,b,c){Jd(a);let d=`<${a}`;b&&(d+=Qd(b));Array.isArray(c)||(c=c===void 0?[]:[c]);Hd.indexOf(a.toUpperCase())!==-1?d+=">":(b=Dd(c.map(e=>e instanceof bd?e:Bd(String(e)))),d+=">"+b.toString()+"</"+a+">");return cd(d)} 
function Qd(a){var b="";const c=Object.keys(a);for(let f=0;f<c.length;f++){var d=c[f],e=a[d];if(!Fd.test(d))throw Error("");if(e!==void 0&&e!==null){if(/^on./i.test(d))throw Error("");Id.indexOf(d.toLowerCase())!==-1&&(e=Rc(e)?e.toString():Zc(String(e))||"about:invalid#zClosurez");e=`${d}="${Bd(String(e))}"`;b+=" "+e}}return b};function Rd(a,...b){if(b.length===0)return Nc(a[0]);let c=a[0];for(let d=0;d<b.length;d++)c+=encodeURIComponent(b[d])+a[d+1];return Nc(c)}function Sd(a,b){a=Oc(a).toString();const c=a.split(/[?#]/),d=/[?]/.test(a)?"?"+c[1]:"";return Td(c[0],d,/[#]/.test(a)?"#"+(d?c[2]:c[1]):"",b)} 
function Td(a,b,c,d){function e(g,h){g!=null&&(Array.isArray(g)?g.forEach(k=>e(k,h)):(b+=f+encodeURIComponent(h)+"="+encodeURIComponent(g),f="&"))}let f=b.length?"&":"?";d.constructor===Object&&(d=Object.entries(d));Array.isArray(d)?d.forEach(g=>e(g[1],g[0])):d.forEach(e);return Nc(a+b+c)};function Ud(a){try{return!!a&&a.location.href!=null&&dc(a,"foo")}catch{return!1}}function Vd(a,b=r){b=Wd(b);let c=0;for(;b&&c++<40&&!a(b);)b=Wd(b)}function Wd(a){try{const b=a.parent;if(b&&b!=a)return b}catch{}return null}function Xd(a){return Ud(a.top)?a.top:null}function Yd(a,b){const c=Zd("SCRIPT",a);fd(c,b);(a=a.getElementsByTagName("script")[0])&&a.parentNode&&a.parentNode.insertBefore(c,a)}function $d(a,b){return b.getComputedStyle?b.getComputedStyle(a,null):a.currentStyle} 
function ae(){if(!globalThis.crypto)return Math.random();try{const a=new Uint32Array(1);globalThis.crypto.getRandomValues(a);return a[0]/65536/65536}catch{return Math.random()}}function be(a,b){if(a)for(const c in a)Object.prototype.hasOwnProperty.call(a,c)&&b(a[c],c,a)}function ce(a){const b=[];be(a,function(c){b.push(c)});return b}function de(a){const b=a.length;if(b==0)return 0;let c=305419896;for(let d=0;d<b;d++)c^=(c<<5)+(c>>2)+a.charCodeAt(d)&4294967295;return c>0?c:4294967296+c} 
var fe=sc(()=>Xa(["Google Web Preview","Mediapartners-Google","Google-Read-Aloud","Google-Adwords"],ee)||Math.random()<1E-4);const ee=a=>Tb().indexOf(a)!=-1;var ge=/^([0-9.]+)px$/,he=/^(-?[0-9.]{1,30})$/;function ie(a){if(!he.test(a))return null;a=Number(a);return isNaN(a)?null:a}function oe(a){return(a=ge.exec(a))?+a[1]:null} 
var pe={jm:"allow-forms",km:"allow-modals",lm:"allow-orientation-lock",mm:"allow-pointer-lock",nm:"allow-popups",om:"allow-popups-to-escape-sandbox",pm:"allow-presentation",qm:"allow-same-origin",rm:"allow-scripts",sm:"allow-top-navigation",tm:"allow-top-navigation-by-user-activation"};const qe=sc(()=>ce(pe));function re(){var a=["allow-top-navigation","allow-modals","allow-orientation-lock","allow-presentation","allow-pointer-lock"];const b=qe();return a.length?Qa(b,c=>!Za(a,c)):b} 
function se(){const a=Zd("IFRAME"),b={};Na(qe(),c=>{a.sandbox&&a.sandbox.supports&&a.sandbox.supports(c)&&(b[c]=!0)});return b} 
var te=(a,b)=>{try{return!(!a.frames||!a.frames[b])}catch{return!1}},ue=(a,b)=>{for(let c=0;c<50;++c){if(te(a,b))return a;if(!(a=Wd(a)))break}return null},ve=sc(()=>wc()?2:xc()?1:0),v=(a,b)=>{be(b,(c,d)=>{a.style.setProperty(d,c,"important")})},xe=(a,b)=>{if("length"in a.style){a=a.style;const c=a.length;for(let d=0;d<c;d++){const e=a[d];b(a[e],e,a)}}else a=we(a.style.cssText),be(a,b)},we=a=>{const b={};if(a){const c=/\s*:\s*/;Na((a||"").split(/\s*;\s*/),d=>{if(d){var e=d.split(c);d=e[0];e=e[1];d&& 
e&&(b[d.toLowerCase()]=e)}})}return b},ye=a=>{const b=/!\s*important/i;xe(a,(c,d)=>{b.test(c)?b.test(c):a.style.setProperty(d,c,"important")})};const ze={["http://googleads.g.doubleclick.net"]:!0,["http://pagead2.googlesyndication.com"]:!0,["https://googleads.g.doubleclick.net"]:!0,["https://pagead2.googlesyndication.com"]:!0},Ae=/\.proxy\.(googleprod|googlers)\.com(:\d+)?$/,Be=/.*domain\.test$/,Ce=/\.prod\.google\.com(:\d+)?$/;var De=a=>ze[a]||Ae.test(a)||Be.test(a)||Ce.test(a);let Ee=[]; 
const Fe=()=>{const a=Ee;Ee=[];for(const b of a)try{b()}catch{}};var Ge=(a,b)=>Fb(a,{fe:({methodName:c,Kd:d})=>void b?.ma(c,d)}),He=(a,b)=>{a.document.readyState==="complete"?(Ee.push(b),Ee.length==1&&(window.Promise?Promise.resolve().then(Fe):window.setImmediate?setImmediate(Fe):setTimeout(Fe,0))):a.addEventListener("load",b)},Ie=(a,b)=>new Promise(c=>{setTimeout(()=>void c(b),a)});function Zd(a,b=document){return b.createElement(String(a).toLowerCase())} 
var Je=a=>{let b=a;for(;a&&a!=a.parent;)a=a.parent,Ud(a)&&(b=a);return b},Ke=a=>{var b=Xd(a);if(!b)return 1;a=ve()===0;const c=!!b.document.querySelector('meta[name=viewport][content*="width=device-width"]'),d=b.innerWidth;b=b.outerWidth;if(d===0)return 1;const e=Math.round((b/d+Number.EPSILON)*100)/100;return e===1?1:a||c?e:Math.round((b/d/.4+Number.EPSILON)*100)/100};let Le;function Me(a){return(Le||(Le=new TextEncoder)).encode(a)};function Ne(a){r.setTimeout(()=>{throw a;},0)};var Ue={},Ve=null;function We(a){var b=3;b===void 0&&(b=0);Xe();b=Ue[b];const c=Array(Math.floor(a.length/3)),d=b[64]||"";let e=0,f=0;for(;e<a.length-2;e+=3){var g=a[e],h=a[e+1],k=a[e+2],l=b[g>>2];g=b[(g&3)<<4|h>>4];h=b[(h&15)<<2|k>>6];k=b[k&63];c[f++]=l+g+h+k}l=0;k=d;switch(a.length-e){case 2:l=a[e+1],k=b[(l&15)<<2]||d;case 1:a=a[e],c[f]=b[a>>2]+b[(a&3)<<4|l>>4]+k+d}return c.join("")} 
function Ye(a){const b=[];let c=0;for(let d=0;d<a.length;d++){let e=a.charCodeAt(d);e>255&&(b[c++]=e&255,e>>=8);b[c++]=e}return We(b)}function Ze(a){const b=[];$e(a,function(c){b.push(c)});return b} 
function $e(a,b){function c(e){for(;d<a.length;){const f=a.charAt(d++),g=Ve[f];if(g!=null)return g;if(!/^[\s\xa0]*$/.test(f))throw Error("Unknown base64 encoding at char: "+f);}return e}Xe();let d=0;for(;;){const e=c(-1),f=c(0),g=c(64),h=c(64);if(h===64&&e===-1)break;b(e<<2|f>>4);g!=64&&(b(f<<4&240|g>>2),h!=64&&b(g<<6&192|h))}} 
function Xe(){if(!Ve){Ve={};var a="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".split(""),b=["+/=","+/","-_=","-_.","-_"];for(let c=0;c<5;c++){const d=a.concat(b[c].split(""));Ue[c]=d;for(let e=0;e<d.length;e++){const f=d[e];Ve[f]===void 0&&(Ve[f]=e)}}}};const af=/[-_.]/g,bf={"-":"+",_:"/",".":"="};function cf(a){return bf[a]||""}var df={},ef=typeof structuredClone!="undefined";function ff(){return gf||(gf=new hf(null,df))}var hf=class{isEmpty(){return this.g==null}constructor(a,b){jf(b);this.g=a;if(a!=null&&a.length===0)throw Error("ByteString should be constructed with non-empty values");}};let gf;function jf(a){if(a!==df)throw Error("illegal external caller");};let kf=void 0,lf;function mf(a){if(lf)throw Error("");lf=b=>{r.setTimeout(()=>{a(b)},0)}}function nf(a){if(lf)try{lf(a)}catch(b){throw b.cause=a,b;}}function of(){const a=Error();md(a,"incident");lf?nf(a):Ne(a)}function pf(a){a=Error(a);md(a,"warning");nf(a);return a};function qf(a,b=!1){return b&&Symbol.for&&a?Symbol.for(a):a!=null?Symbol(a):Symbol()}var rf=qf(),sf=qf(),tf=qf(),uf=qf(),vf=qf("m_m",!0);const x=qf("jas",!0);var wf;const xf=[];xf[x]=7;wf=Object.freeze(xf);function yf(a,b){a[x]|=b}function Nf(a){if(4&a)return 512&a?512:1024&a?1024:0}function Of(a){yf(a,34);return a}function Pf(a){yf(a,32);return a};var Qf={};function Rf(a,b){return b===void 0?a.j!==Sf&&!!(2&(a.P[x]|0)):!!(2&b)&&a.j!==Sf}const Sf={};function Tf(a,b){if(a!=null)if(typeof a==="string")a=a?new hf(a,df):ff();else if(a.constructor!==hf)if(a!=null&&a instanceof Uint8Array)a=a.length?new hf(new Uint8Array(a),df):ff();else{if(!b)throw Error();a=void 0}return a}class Uf{constructor(a,b,c){this.g=a;this.i=b;this.j=c}next(){const a=this.g.next();a.done||(a.value=this.i.call(this.j,a.value));return a}[Symbol.iterator](){return this}} 
var Vf=Object.freeze({});function Wf(a,b,c){b=b&128?0:-1;const d=a.length;var e;if(e=!!d)e=a[d-1],e=e!=null&&typeof e==="object"&&e.constructor===Object;const f=d+(e?-1:0);for(let g=0;g<f;g++)c(g-b,a[g]);if(e){a=a[d-1];for(const g in a)Object.prototype.hasOwnProperty.call(a,g)&&!isNaN(g)&&c(+g,a[g])}}var Xf={};function Yf(a){if(pb(a)){if(!/^\s*(?:-?[1-9]\d*|0)?\s*$/.test(a))throw Error(String(a));}else if(sb(a)&&!Number.isSafeInteger(a))throw Error(String(a));return BigInt(a)}var ag=rb(a=>a>=Zf&&a<=$f);const Zf=BigInt(Number.MIN_SAFE_INTEGER),$f=BigInt(Number.MAX_SAFE_INTEGER);let bg=0,cg=0,dg;function eg(a){const b=a>>>0;bg=b;cg=(a-b)/4294967296>>>0}function fg(a){if(a<0){eg(-a);a=bg;var b=cg;b=~b;a?a=~a+1:b+=1;const [c,d]=[a,b];bg=c>>>0;cg=d>>>0}else eg(a)}function gg(a,b){const c=b*4294967296+(a>>>0);return Number.isSafeInteger(c)?c:hg(a,b)}function hg(a,b){b>>>=0;a>>>=0;var c;b<=2097151?c=""+(4294967296*b+a):c=""+(BigInt(b)<<BigInt(32)|BigInt(a));return c}function ig(){var a=bg,b=cg,c;b&2147483648?c=""+(BigInt(b|0)<<BigInt(32)|BigInt(a>>>0)):c=hg(a,b);return c} 
function jg(a){a.length<16?fg(Number(a)):(a=BigInt(a),bg=Number(a&BigInt(4294967295))>>>0,cg=Number(a>>BigInt(32)&BigInt(4294967295)))};const kg=typeof BigInt==="function"?BigInt.asIntN:void 0,lg=typeof BigInt==="function"?BigInt.asUintN:void 0,mg=Number.isSafeInteger,ng=Number.isFinite,og=Math.trunc;function pg(a){if(a!=null&&typeof a!=="number")throw Error(`Value of float/double field must be a number, found ${typeof a}: ${a}`);return a}function qg(a){if(a==null||typeof a==="number")return a;if(a==="NaN"||a==="Infinity"||a==="-Infinity")return Number(a)} 
function rg(a){if(typeof a!=="boolean")throw Error(`Expected boolean but got ${ra(a)}: ${a}`);return a}function sg(a){if(a==null||typeof a==="boolean")return a;if(typeof a==="number")return!!a}const tg=/^-?([1-9][0-9]*|0)(\.[0-9]+)?$/;function ug(a){switch(typeof a){case "bigint":return!0;case "number":return ng(a);case "string":return tg.test(a);default:return!1}}function vg(a){if(!ng(a))throw pf("enum");return a|0}function wg(a){return a==null?a:ng(a)?a|0:void 0} 
function xg(a){if(typeof a!=="number")throw pf("int32");if(!ng(a))throw pf("int32");return a|0}function yg(a){if(a==null)return a;if(typeof a==="string"&&a)a=+a;else if(typeof a!=="number")return;return ng(a)?a|0:void 0}function zg(a){if(typeof a!=="number")throw pf("uint32");if(!ng(a))throw pf("uint32");return a>>>0}function Ag(a){if(a==null)return a;if(typeof a==="string"&&a)a=+a;else if(typeof a!=="number")return;return ng(a)?a>>>0:void 0} 
function Bg(a,b=0){if(!ug(a))throw pf("int64");const c=typeof a;switch(b){case 512:switch(c){case "string":return Cg(a);case "bigint":return String(kg(64,a));default:return Dg(a)}case 1024:switch(c){case "string":return Eg(a);case "bigint":return Yf(kg(64,a));default:return Fg(a)}case 0:switch(c){case "string":return Cg(a);case "bigint":return Yf(kg(64,a));default:return Gg(a)}default:return ad(b,"Unknown format requested type for int64")}}function Hg(a){return a==null?a:Bg(a,0)} 
function Ig(a){if(a[0]==="-")return!1;const b=a.length;return b<20?!0:b===20&&Number(a.substring(0,6))<184467}function Jg(a){const b=a.length;return a[0]==="-"?b<20?!0:b===20&&Number(a.substring(0,7))>-922337:b<19?!0:b===19&&Number(a.substring(0,6))<922337}function Kg(a){if(a<0){fg(a);var b=hg(bg,cg);a=Number(b);return mg(a)?a:b}b=String(a);if(Ig(b))return b;fg(a);return gg(bg,cg)} 
function Gg(a){a=og(a);if(!mg(a)){fg(a);var b=bg,c=cg;if(a=c&2147483648)b=~b+1>>>0,c=~c>>>0,b==0&&(c=c+1>>>0);b=gg(b,c);a=typeof b==="number"?a?-b:b:a?"-"+b:b}return a}function Lg(a){a=og(a);return a>=0&&mg(a)?a:Kg(a)}function Dg(a){a=og(a);if(mg(a))a=String(a);else{{const b=String(a);Jg(b)?a=b:(fg(a),a=ig())}}return a}function Mg(a){a=og(a);if(a>=0&&mg(a))a=String(a);else{{const b=String(a);Ig(b)?a=b:(fg(a),a=hg(bg,cg))}}return a} 
function Cg(a){var b=og(Number(a));if(mg(b))return String(b);b=a.indexOf(".");b!==-1&&(a=a.substring(0,b));Jg(a)||(jg(a),a=ig());return a}function Eg(a){var b=og(Number(a));if(mg(b))return Yf(b);b=a.indexOf(".");b!==-1&&(a=a.substring(0,b));return Yf(kg(64,BigInt(a)))}function Fg(a){return mg(a)?Yf(Gg(a)):Yf(Dg(a))}function Sg(a){var b=og(Number(a));if(mg(b)&&b>=0)return String(b);b=a.indexOf(".");b!==-1&&(a=a.substring(0,b));Ig(a)||(jg(a),a=hg(bg,cg));return a} 
function Tg(a){if(a==null)return a;if(typeof a==="bigint")return ag(a)?a=Number(a):(a=kg(64,a),a=ag(a)?Number(a):String(a)),a;if(ug(a))return typeof a==="number"?Gg(a):Cg(a)}function Ug(a){const b=typeof a;if(a==null)return a;if(b==="bigint")return Yf(kg(64,a));if(ug(a))return b==="string"?Eg(a):Fg(a)} 
function Vg(a,b=0){if(!ug(a))throw pf("uint64");const c=typeof a;switch(b){case 512:switch(c){case "string":return Sg(a);case "bigint":return String(lg(64,a));default:return Mg(a)}case 1024:switch(c){case "string":return b=og(Number(a)),mg(b)&&b>=0?a=Yf(b):(b=a.indexOf("."),b!==-1&&(a=a.substring(0,b)),a=Yf(lg(64,BigInt(a)))),a;case "bigint":return Yf(lg(64,a));default:return mg(a)?Yf(Lg(a)):Yf(Mg(a))}case 0:switch(c){case "string":return Sg(a);case "bigint":return Yf(lg(64,a));default:return Lg(a)}default:return ad(b, 
"Unknown format requested type for int64")}}function Wg(a){return a==null?a:Vg(a,0)}function Xg(a){const b=typeof a;if(a==null)return a;if(b==="bigint")return String(lg(64,a));if(ug(a))return b==="string"?Sg(a):Mg(a)}function Yg(a){if(a==null)return a;const b=typeof a;if(b==="bigint")return String(kg(64,a));if(ug(a)){if(b==="string")return Cg(a);if(b==="number")return Gg(a)}} 
function Zg(a){if(a==null)return a;const b=typeof a;if(b==="bigint")return String(lg(64,a));if(ug(a)){if(b==="string")return Sg(a);if(b==="number")return Lg(a)}}function $g(a){if(typeof a!=="string")throw Error();return a}function ah(a){if(a!=null&&typeof a!=="string")throw Error();return a}function bh(a){return a==null||typeof a==="string"?a:void 0} 
function ch(a,b,c,d){if(a!=null&&typeof a==="object"&&a[vf]===Qf)return a;if(!Array.isArray(a))return c?d&2?((a=b[rf])||(a=new b,Of(a.P),a=b[rf]=a),b=a):b=new b:b=void 0,b;c=a[x]|0;d=c|d&32|d&2;d!==c&&(a[x]=d);return new b(a)}function dh(a,b,c){return b?$g(a):bh(a)??(c?"":void 0)};function eh(a){return a};const fh={},gh=(()=>class extends Map{constructor(){super()}})();function hh(a){return a}function ih(a){if(a.ac&2)throw Error("Cannot mutate an immutable Map");} 
var mh=class extends gh{constructor(a,b,c=hh,d=hh){super();this.ac=a[x]|0;this.Vb=b;this.de=c;this.vi=this.Vb?jh:d;for(let e=0;e<a.length;e++){const f=a[e],g=c(f[0],!1,!0);let h=f[1];b?h===void 0&&(h=null):h=d(f[1],!1,!0,void 0,void 0,this.ac);super.set(g,h)}}wl(){var a=kh;if(this.size!==0)return Array.from(super.entries(),a)}ri(){return Array.from(super.entries())}clear(){ih(this);super.clear()}delete(a){ih(this);return super.delete(this.de(a,!0,!1))}entries(){if(this.Vb){var a=super.keys();a=new Uf(a, 
lh,this)}else a=super.entries();return a}values(){if(this.Vb){var a=super.keys();a=new Uf(a,mh.prototype.get,this)}else a=super.values();return a}forEach(a,b){this.Vb?super.forEach((c,d,e)=>{a.call(b,e.get(d),d,e)}):super.forEach(a,b)}set(a,b){ih(this);a=this.de(a,!0,!1);return a==null?this:b==null?(super.delete(a),this):super.set(a,this.vi(b,!0,!0,this.Vb,!1,this.ac))}has(a){return super.has(this.de(a,!1,!1))}get(a){a=this.de(a,!1,!1);const b=super.get(a);if(b!==void 0){var c=this.Vb;return c?(c= 
this.vi(b,!1,!0,c,this.gj,this.ac),c!==b&&super.set(a,c),c):b}}[Symbol.iterator](){return this.entries()}};mh.prototype.toJSON=void 0;function jh(a,b,c,d,e,f){a=ch(a,d,c,f);e&&(a=nh(a));return a}function lh(a){return[a,this.get(a)]}let oh;function ph(){return oh||(oh=new mh(Of([]),void 0,void 0,void 0,fh))};function qh(a,b,c,d){var e=d!==void 0;d=!!d;const f=[];var g=a.length;let h,k=4294967295,l=!1;const m=!!(b&64),n=m?b&128?0:-1:void 0;b&1||(h=g&&a[g-1],h!=null&&typeof h==="object"&&h.constructor===Object?(g--,k=g):h=void 0,!m||b&128||e||(l=!0,k=(rh??eh)(k-n,n,a,h)+n));b=void 0;for(e=0;e<g;e++){let p=a[e];if(p!=null&&(p=c(p,d))!=null)if(m&&e>=k){const w=e-n;(b??(b={}))[w]=p}else f[e]=p}if(h)for(let p in h){if(!Object.prototype.hasOwnProperty.call(h,p))continue;a=h[p];if(a==null||(a=c(a,d))==null)continue; 
g=+p;let w;m&&!Number.isNaN(g)&&(w=g+n)<k?f[w]=a:(b??(b={}))[p]=a}b&&(l?f.push(b):f[k]=b);return f}function kh(a){a[0]=sh(a[0]);a[1]=sh(a[1]);return a} 
function sh(a){switch(typeof a){case "number":return Number.isFinite(a)?a:""+a;case "bigint":return ag(a)?Number(a):""+a;case "boolean":return a?1:0;case "object":if(Array.isArray(a)){var b=a[x]|0;return a.length===0&&b&1?void 0:qh(a,b,sh)}if(a[vf]===Qf)return th(a);if(a instanceof hf){b=a.g;if(b==null)a="";else if(typeof b==="string")a=b;else{let c="",d=0;const e=b.length-10240;for(;d<e;)c+=String.fromCharCode.apply(null,b.subarray(d,d+=10240));c+=String.fromCharCode.apply(null,d?b.subarray(d):b); 
a=a.g=btoa(c)}return a}if(a instanceof mh)return a.wl();return}return a}var uh=ef?structuredClone:a=>qh(a,0,sh);let rh;function th(a){a=a.P;return qh(a,a[x]|0,sh)};let vh,wh;function xh(a){switch(typeof a){case "boolean":return vh||(vh=[0,void 0,!0]);case "number":return a>0?void 0:a===0?wh||(wh=[0,void 0]):[-a,void 0];case "string":return[0,a];case "object":return a}}function yh(a,b,c){a=zh(a,b[0],b[1],c?1:2);b!==vh&&c&&yf(a,2048);return a} 
function zh(a,b,c,d=0){if(a==null){var e=32;c?(a=[c],e|=128):a=[];b&&(e=e&-16760833|(b&1023)<<14)}else{if(!Array.isArray(a))throw Error("narr");e=a[x]|0;4096&e&&!(2&e)&&Ah();if(e&256)throw Error("farr");if(e&64)return d!==0||e&4096||(a[x]=e|4096),a;if(c&&(e|=128,c!==a[0]))throw Error("mid");a:{c=a;e|=64;var f=c.length;if(f){var g=f-1;const k=c[g];if(k!=null&&typeof k==="object"&&k.constructor===Object){b=e&128?0:-1;g-=b;if(g>=1024)throw Error("pvtlmt");for(var h in k)if(Object.prototype.hasOwnProperty.call(k, 
h))if(f=+h,f<g)c[f+b]=k[h],delete k[h];else break;e=e&-16760833|(g&1023)<<14;break a}}if(b){h=Math.max(b,f-(e&128?0:-1));if(h>1024)throw Error("spvt");e=e&-16760833|(h&1023)<<14}}}e|=64;d===0&&(e|=4096);a[x]=e;return a}function Ah(){if(uf!=null){var a=kf??(kf={});var b=a[uf]||0;b>=5||(a[uf]=b+1,of())}};function Bh(a,b){if(typeof a!=="object")return a;if(Array.isArray(a)){var c=a[x]|0;return a.length===0&&c&1?void 0:Ch(a,c,b)}if(a[vf]===Qf)return Dh(a);if(a instanceof mh){b=a.ac;if(b&2)return a;if(!a.size)return;c=Of(a.ri());if(a.Vb)for(a=0;a<c.length;a++){const d=c[a];let e=d[1];e==null||typeof e!=="object"?e=void 0:e[vf]===Qf?e=Dh(e):Array.isArray(e)?e=Ch(e,e[x]|0,!!(b&32)):e=void 0;d[1]=e}return c}if(a instanceof hf)return a} 
function Ch(a,b,c){if(b&2)return a;!c||8192&b||16&b?a=Eh(a,b,!1,c&&!(b&16)):(yf(a,34),b&4&&Object.freeze(a));return a}function Dh(a){const b=a.P,c=b[x]|0;return Rf(a,c)?a:Eh(b,c)}function Fh(a){const b=a.P;return new a.constructor(Eh(b,b[x]|0,!0))}function Eh(a,b,c,d){d??(d=!!(34&b));a=qh(a,b,Bh,d);d=32;c||(d|=2);b=b&16761025|d;a[x]=b;return a}function nh(a){const b=a.P,c=b[x]|0;return Rf(a,c)?new a.constructor(Eh(b,c,!0)):a} 
function Gh(a){const b=a.P,c=b[x]|0;return Rf(a,c)?a:new a.constructor(Eh(b,c))}function Hh(a){if(a.j!==Sf)return!1;let b=a.P;b=Eh(b,b[x]|0,!0);a.P=b;a.j=void 0;a.D=void 0;return!0}function Ih(a){if(!Hh(a)&&Rf(a,a.P[x]|0))throw Error();};const Jh=Yf(0),Kh={};function y(a,b,c,d,e){b=Lh(a.P,b,c,e);if(b!==null||d&&a.D!==Sf)return b}function Lh(a,b,c,d){if(b===-1)return null;const e=b+(c?0:-1),f=a.length-1;let g,h;if(!(f<1+(c?0:-1))){if(e>=f)if(g=a[f],g!=null&&typeof g==="object"&&g.constructor===Object)c=g[b],h=!0;else if(e===f)c=g;else return;else c=a[e];if(d&&c!=null){d=d(c);if(d==null)return d;if(!Object.is(d,c))return h?g[b]=d:a[e]=d,d}return c}}function Mh(a,b,c){Ih(a);const d=a.P;Nh(d,d[x]|0,b,c);return a} 
function Nh(a,b,c,d,e){const f=c+(e?0:-1);var g=a.length-1;if(g>=1+(e?0:-1)&&f>=g){const h=a[g];if(h!=null&&typeof h==="object"&&h.constructor===Object)return h[c]=d,b}if(f<=g)return a[f]=d,b;d!==void 0&&(g=(b??(b=a[x]|0))>>14&1023||536870912,c>=g?d!=null&&(a[g+(e?0:-1)]={[c]:d}):a[f]=d);return b}function Oh(a,b,c){a=a.P;return Ph(a,a[x]|0,b,c)!==void 0}function Qh(a,b,c,d){const e=a.P;return Ph(e,e[x]|0,b,Rh(a,d,c))!==void 0}function Sh(a,b,c){return y(a,b,void 0,c,qg)} 
function Th(a){return a===Vf?2:4} 
function Uh(a,b,c,d,e,f,g){let h=a.P,k=h[x]|0;d=Rf(a,k)?1:d;e=!!e||d===3;d===2&&Hh(a)&&(h=a.P,k=h[x]|0);let l=Vh(h,b,g),m=l===wf?7:l[x]|0,n=Wh(m,k);var p=n;4&p?f==null?a=!1:(!e&&f===0&&(512&p||1024&p)&&(a.constructor[tf]=(a.constructor[tf]|0)+1)<5&&of(),a=f===0?!1:!(f&p)):a=!0;if(a){4&n&&(l=[...l],m=0,n=Xh(n,k),k=Nh(h,k,b,l,g));let w=p=0;for(;p<l.length;p++){const u=c(l[p]);u!=null&&(l[w++]=u)}w<p&&(l.length=w);c=(n|4)&-513;n=c&=-1025;f&&(n|=f);n&=-8193}n!==m&&(l[x]=n,2&n&&Object.freeze(l));return l= 
Yh(l,n,h,k,b,g,d,a,e)}function Yh(a,b,c,d,e,f,g,h,k){let l=b;g===1||(g!==4?0:2&b||!(16&b)&&32&d)?Zh(b)||(b|=!a.length||h&&!(8192&b)||32&d&&!(8192&b||16&b)?2:256,b!==l&&(a[x]=b),Object.freeze(a)):(g===2&&Zh(b)&&(a=[...a],l=0,b=Xh(b,d),Nh(c,d,e,a,f)),Zh(b)||(k||(b|=16),b!==l&&(a[x]=b)));return a}function Vh(a,b,c){a=Lh(a,b,c);return Array.isArray(a)?a:wf}function Wh(a,b){2&b&&(a|=2);return a|1}function Zh(a){return!!(2&a)&&!!(4&a)||!!(256&a)}function $h(a){return Tf(a,!0)} 
function ai(a){var b=bi;let c;var d=a.P;var e=d[x]|0;c=Rf(a,e);a:{!c&&Hh(a)&&(d=a.P,e=d[x]|0);var f=Lh(d,14);a=!1;if(f==null){if(c){d=ph();break a}f=[]}else if(f.constructor===mh)if(f.ac&2&&!c)f=f.ri();else{d=f;break a}else Array.isArray(f)?a=!!((f[x]|0)&2):f=[];if(c){if(!f.length){d=ph();break a}a||(a=!0,Of(f))}else if(a){a=!1;f=[...f];for(let g=0;g<f.length;g++){const h=f[g]=[...f[g]];Array.isArray(h[1])&&(h[1]=Of(h[1]))}}!a&&e&32&&Pf(f);a=new mh(f,b,dh,void 0);Nh(d,e,14,a);d=a}!c&&b&&(d.gj=!0); 
return d}function ci(a,b,c,d){Ih(a);const e=a.P;let f=e[x]|0;if(c==null)return Nh(e,f,b),a;let g=c===wf?7:c[x]|0,h=g;var k=Zh(g);let l=k||Object.isFrozen(c);k||(g=0);l||(c=[...c],h=0,g=Xh(g,f),l=!1);g|=5;k=Nf(g)??0;for(let m=0;m<c.length;m++){const n=c[m],p=d(n,k);Object.is(n,p)||(l&&(c=[...c],h=0,g=Xh(g,f),l=!1),c[m]=p)}g!==h&&(l&&(c=[...c],g=Xh(g,f)),c[x]=g);Nh(e,f,b,c);return a}function di(a,b,c,d){Ih(a);const e=a.P;Nh(e,e[x]|0,b,(d==="0"?Number(c)===0:c===d)?void 0:c);return a} 
function ei(a,b,c,d){Ih(a);const e=a.P;var f=e[x]|0;if(d==null){var g=fi(e);if(gi(g,e,f,c)===b)g.set(c,0);else return a}else{g=fi(e);const h=gi(g,e,f,c);h!==b&&(h&&(f=Nh(e,f,h)),g.set(c,b))}Nh(e,f,b,d);return a}function Rh(a,b,c){return hi(a,b)===c?c:-1}function hi(a,b){a=a.P;return gi(fi(a),a,void 0,b)}function fi(a){return a[sf]??(a[sf]=new Map)} 
function gi(a,b,c,d){let e=a.get(d);if(e!=null)return e;e=0;for(let f=0;f<d.length;f++){const g=d[f];Lh(b,g)!=null&&(e!==0&&(c=Nh(b,c,e)),e=g)}a.set(d,e);return e}function ii(a){var b=ji;Ih(a);a=a.P;let c=a[x]|0;const d=Lh(a,26);b=nh(ch(d,b,!0,c));d!==b&&Nh(a,c,26,b);return b}function Ph(a,b,c,d){a=Lh(a,d,void 0,e=>ch(e,c,!1,b));if(a!=null)return a}function ki(a,b,c){a=a.P;(c=Ph(a,a[x]|0,b,c))||(c=b[rf])||(c=new b,Of(c.P),c=b[rf]=c);return c} 
function z(a,b,c){let d=a.P,e=d[x]|0;b=Ph(d,e,b,c);if(b==null)return b;e=d[x]|0;if(!Rf(a,e)){const f=nh(b);f!==b&&(Hh(a)&&(d=a.P,e=d[x]|0),b=f,Nh(d,e,c,b))}return b} 
function li(a,b,c,d,e,f,g,h,k){var l=Rf(a,c);f=l?1:f;h=!!h||f===3;l=k&&!l;(f===2||l)&&Hh(a)&&(b=a.P,c=b[x]|0);a=Vh(b,e,g);var m=a===wf?7:a[x]|0,n=Wh(m,c);if(k=!(4&n)){var p=a,w=c;const u=!!(2&n);u&&(w|=2);let t=!u,B=!0,I=0,U=0;for(;I<p.length;I++){const M=ch(p[I],d,!1,w);if(M instanceof d){if(!u){const P=Rf(M);t&&(t=!P);B&&(B=P)}p[U++]=M}}U<I&&(p.length=U);n|=4;n=B?n&-8193:n|8192;n=t?n|8:n&-9}n!==m&&(a[x]=n,2&n&&Object.freeze(a));if(l&&!(8&n||!a.length&&(f===1||(f!==4?0:2&n||!(16&n)&&32&c)))){Zh(n)&& 
(a=[...a],n=Xh(n,c),c=Nh(b,c,e,a,g));d=a;l=n;for(m=0;m<d.length;m++)p=d[m],n=nh(p),p!==n&&(d[m]=n);l|=8;n=l=d.length?l|8192:l&-8193;a[x]=n}return a=Yh(a,n,b,c,e,g,f,k,h)}function mi(a,b,c,d){const e=a.P;return li(a,e,e[x]|0,b,c,d,void 0,!1,!0)}function ni(a){a==null&&(a=void 0);return a}function A(a,b,c){c=ni(c);Mh(a,b,c);return a}function oi(a,b,c,d){d=ni(d);ei(a,b,c,d);return a} 
function pi(a,b,c){Ih(a);const d=a.P;let e=d[x]|0;if(c==null)return Nh(d,e,b),a;let f=c===wf?7:c[x]|0,g=f;const h=Zh(f),k=h||Object.isFrozen(c);let l=!0,m=!0;for(let p=0;p<c.length;p++){var n=c[p];h||(n=Rf(n),l&&(l=!n),m&&(m=n))}h||(f=l?13:5,f=m?f&-8193:f|8192);k&&f===g||(c=[...c],g=0,f=Xh(f,e));f!==g&&(c[x]=f);Nh(d,e,b,c);return a}function Xh(a,b){return a=(2&b?a|2:a&-3)&-273} 
function qi(a,b,c,d,e,f,g,h){Ih(a);b=Uh(a,b,e,2,!0,void 0,f);e=Nf(b===wf?7:b[x]|0)??0;if(h)if(Array.isArray(d))for(g=d.length,h=0;h<g;h++)b.push(c(d[h],e));else for(const k of d)b.push(c(k,e));else{if(g)throw Error();b.push(c(d,e))}return a}function ri(a,b,c,d){Ih(a);var e=a.P;b=li(a,e,e[x]|0,c,b,2,void 0,!0);d=d!=null?d:new c;b.push(d);e=c=b===wf?7:b[x]|0;Rf(d)?(c&=-9,b.length===1&&(c&=-8193)):c|=8192;c!==e&&(b[x]=c);return a}function si(a,b,c,d){return sg(y(a,b,c,d))} 
function ti(a,b,c,d){return bh(y(a,b,c,d))}function C(a,b){return si(a,b)??!1}function ui(a,b){return yg(y(a,b))??0}function vi(a,b){return Ug(y(a,b))??Jh}function wi(a,b,c=0){return Sh(a,b)??c}function E(a,b){return ti(a,b)??""}function F(a,b){return wg(y(a,b))??0}function xi(a,b){return Uh(a,b,yg,Th())}function yi(a,b){return Uh(a,b,bh,Th())}function zi(a,b){return Uh(a,b,wg,Th())}function Ai(a,b,c,d){return z(a,b,Rh(a,d,c))}function Bi(a,b){return si(a,b,void 0,Kh)} 
function Ci(a,b){return yg(y(a,b,void 0,Kh))}function Ji(a,b){return ti(a,b,void 0,Kh)}function Ki(a,b){return wg(y(a,b,void 0,Kh))}function Li(a,b,c){return Mh(a,b,c==null?c:rg(c))}function Mi(a,b,c){return di(a,b,c==null?c:rg(c),!1)}function Ni(a,b,c){return Mh(a,b,c==null?c:xg(c))}function Oi(a,b,c){return di(a,b,c==null?c:xg(c),0)}function Pi(a,b,c){return di(a,b,Hg(c),"0")}function Qi(a,b,c){return Mh(a,b,ah(c))}function Ri(a,b,c){return di(a,b,ah(c),"")} 
function Si(a,b,c){return Mh(a,b,c==null?c:vg(c))}function G(a,b,c){return di(a,b,c==null?c:vg(c),0)};function Ti(a){return nh(a)}function Ui(a){return JSON.stringify(th(a))}function Vi(a){return Gh(a)}var H=class{constructor(a){this.P=zh(a)}toJSON(){return th(this)}};H.prototype[vf]=Qf;function Wi(a,b){if(b==null)return new a;if(!Array.isArray(b))throw Error();if(Object.isFrozen(b)||Object.isSealed(b)||!Object.isExtensible(b))throw Error();return new a(Pf(b))};function Xi(a){a=BigInt.asUintN(64,a);return new Yi(Number(a&BigInt(4294967295)),Number(a>>BigInt(32)))}function Zi(a){if(!a)return $i||($i=new Yi(0,0));if(!/^\d+$/.test(a))return null;jg(a);return new Yi(bg,cg)}var Yi=class{constructor(a,b){this.i=a>>>0;this.g=b>>>0}};let $i;function aj(a){a=BigInt.asUintN(64,a);return new bj(Number(a&BigInt(4294967295)),Number(a>>BigInt(32)))}function cj(a){if(!a)return dj||(dj=new bj(0,0));if(!/^-?\d+$/.test(a))return null;jg(a);return new bj(bg,cg)} 
var bj=class{constructor(a,b){this.i=a>>>0;this.g=b>>>0}};let dj;function ej(a,b,c){for(;c>0||b>127;)a.g.push(b&127|128),b=(b>>>7|c<<25)>>>0,c>>>=7;a.g.push(b)}function fj(a,b){for(;b>127;)a.g.push(b&127|128),b>>>=7;a.g.push(b)}function gj(a,b){if(b>=0)fj(a,b);else{for(let c=0;c<9;c++)a.g.push(b&127|128),b>>=7;a.g.push(1)}}function hj(a,b){a.g.push(b>>>0&255);a.g.push(b>>>8&255);a.g.push(b>>>16&255);a.g.push(b>>>24&255)}var ij=class{constructor(){this.g=[]}length(){return this.g.length}end(){const a=this.g;this.g=[];return a}};function jj(a,b){b.length!==0&&(a.j.push(b),a.i+=b.length)}function kj(a,b){jj(a,a.g.end());jj(a,b)}function lj(a,b,c){fj(a.g,b*8+c)}function mj(a,b){lj(a,b,2);b=a.g.end();jj(a,b);b.push(a.i);return b}function nj(a,b){var c=b.pop();for(c=a.i+a.g.length()-c;c>127;)b.push(c&127|128),c>>>=7,a.i++;b.push(c);a.i++}function oj(a){jj(a,a.g.end());const b=new Uint8Array(a.i),c=a.j,d=c.length;let e=0;for(let f=0;f<d;f++){const g=c[f];b.set(g,e);e+=g.length}a.j=[b];return b} 
function pj(a,b,c,d){c!=null&&(b=mj(a,b),d(c,a),nj(a,b))}function qj(a,b,c){var d=rj;if(c!=null)for(let e=0;e<c.length;e++){const f=mj(a,b);d(c[e],a);nj(a,f)}}var sj=class{constructor(){this.j=[];this.i=0;this.g=new ij}};function tj(){const a=class{constructor(){throw Error();}};Object.setPrototypeOf(a,a.prototype);return a}var uj=tj(),vj=tj(),wj=tj(),xj=tj(),yj=tj(),zj=tj(),Aj=tj(),Bj=tj();var Cj=class{constructor(a,b){this.g=a;a=Ia(uj);this.i=!!a&&b===a||!1}};function Dj(a,b,c,d,e){pj(a,c,Ej(b,d),e)}const Fj=new Cj(Dj,uj),Gj=new Cj(Dj,uj);var Hj=Symbol(),Ij=Symbol();let Jj,Kj; 
function Lj(a){var b=Mj,c=Nj,d=a[Hj];if(d)return d;d={};d.jp=a;d.Mh=xh(a[0]);var e=a[1];let f=1;e&&e.constructor===Object&&(d.Jj=e,e=a[++f],typeof e==="function"&&(d.kk=!0,Jj??(Jj=e),Kj??(Kj=a[f+1]),e=a[f+=2]));const g={};for(;e&&Array.isArray(e)&&e.length&&typeof e[0]==="number"&&e[0]>0;){for(var h=0;h<e.length;h++)g[e[h]]=e;e=a[++f]}for(h=1;e!==void 0;){typeof e==="number"&&(h+=e,e=a[++f]);let m;var k=void 0;e instanceof Cj?m=e:(m=Fj,f--);if(m?.i){e=a[++f];k=a;var l=f;typeof e==="function"&&(e= 
e(),k[l]=e);k=e}e=a[++f];l=h+1;typeof e==="number"&&e<0&&(l-=e,e=a[++f]);for(;h<l;h++){const n=g[h];k?c(d,h,m,k,n):b(d,h,m,n)}}return a[Hj]=d}function Ej(a,b){if(a instanceof H)return a.P;if(Array.isArray(a))return yh(a,b,!1)};function Mj(a,b,c){a[b]=c.g}function Nj(a,b,c,d){let e,f;const g=c.g;a[b]=(h,k,l)=>g(h,k,l,f||(f=Lj(d).Mh),e||(e=Oj(d)))}function Oj(a){let b=a[Ij];if(!b){const c=Lj(a);b=(d,e)=>Pj(d,e,c);a[Ij]=b}return b}function Pj(a,b,c){Wf(a,a[x]|0,(d,e)=>{if(e!=null){var f=Qj(c,d);f&&f(b,e,d)}})} 
function Qj(a,b){var c=a[b];if(c)return c;if(c=a.Jj)if(c=c[b]){c=Array.isArray(c)?c[0]instanceof Cj?c:[Gj,c]:[c,void 0];var d=c[0].g;if(c=c[1]){const e=Oj(c),f=Lj(c).Mh;c=a.kk?Kj(f,e):(g,h,k)=>d(g,h,k,f,e)}else c=d;return a[b]=c}};function Rj(a,b,c){if(Array.isArray(b)){var d=b[x]|0;if(d&4)return b;for(var e=0,f=0;e<b.length;e++){const g=a(b[e]);g!=null&&(b[f++]=g)}f<e&&(b.length=f);c&&(b[x]=(d|5)&-1537,d&2&&Object.freeze(b));return b}}function Sj(a,b){return new Cj(a,b)}function Tj(a,b){return new Cj(a,b)}var Uj=new Cj(function(a,b,c,d,e){if(b instanceof mh)b.forEach((f,g)=>{pj(a,c,yh([g,f],d,!1),e)});else if(Array.isArray(b))for(let f=0;f<b.length;f++){const g=b[f];Array.isArray(g)&&pj(a,c,yh(g,d,!1),e)}},uj); 
function Vj(a,b,c){b=qg(b);b!=null&&(lj(a,c,1),a=a.g,c=dg||(dg=new DataView(new ArrayBuffer(8))),c.setFloat64(0,+b,!0),bg=c.getUint32(0,!0),cg=c.getUint32(4,!0),hj(a,bg),hj(a,cg))}function Wj(a,b,c){b=Yg(b);if(b!=null){switch(typeof b){case "string":cj(b)}if(b!=null)switch(lj(a,c,0),typeof b){case "number":a=a.g;fg(b);ej(a,bg,cg);break;case "bigint":c=aj(b);ej(a.g,c.i,c.g);break;default:c=cj(b),ej(a.g,c.i,c.g)}}} 
function Xj(a,b,c){b=Rj(Yg,b,!1);if(b!=null&&b.length){c=mj(a,c);for(let e=0;e<b.length;e++){const f=b[e];switch(typeof f){case "number":var d=a.g;fg(f);ej(d,bg,cg);break;case "bigint":d=aj(f);ej(a.g,d.i,d.g);break;default:d=cj(f),ej(a.g,d.i,d.g)}}nj(a,c)}}function Yj(a,b,c){b=Zg(b);if(b!=null){switch(typeof b){case "string":Zi(b)}if(b!=null)switch(lj(a,c,0),typeof b){case "number":a=a.g;fg(b);ej(a,bg,cg);break;case "bigint":c=Xi(b);ej(a.g,c.i,c.g);break;default:c=Zi(b),ej(a.g,c.i,c.g)}}} 
function Zj(a,b,c){b=yg(b);b!=null&&b!=null&&(lj(a,c,0),gj(a.g,b))}function ak(a,b,c){b=sg(b);b!=null&&(lj(a,c,0),a.g.g.push(b?1:0))}function bk(a,b,c){b=bh(b);b!=null&&(b=Me(b),lj(a,c,2),fj(a.g,b.length),kj(a,b))}function ck(a,b,c,d,e){pj(a,c,Ej(b,d),e)}function dk(a,b,c){b=yg(b);b!=null&&(b=parseInt(b,10),lj(a,c,0),gj(a.g,b))} 
var ek=Sj(Vj,Aj),fk=Sj(Vj,Aj),gk=Sj(function(a,b,c){b=qg(b);b!=null&&(lj(a,c,5),a=a.g,c=dg||(dg=new DataView(new ArrayBuffer(8))),c.setFloat32(0,+b,!0),cg=0,bg=c.getUint32(0,!0),hj(a,bg))},tj()),hk=Tj(Xj,yj),ik=Sj(Wj,yj),jk=Tj(Xj,yj),kk=Sj(Wj,yj),lk=Sj(Wj,yj),mk=Sj(Yj,zj),nk=Tj(function(a,b,c){b=Rj(Zg,b,!1);if(b!=null&&b.length){c=mj(a,c);for(let f=0;f<b.length;f++){var d=b[f];switch(typeof d){case "number":var e=a.g;fg(d);ej(e,bg,cg);break;case "bigint":e=Number(d);Number.isSafeInteger(e)?(d=a.g, 
fg(e),ej(d,bg,cg)):(d=Xi(d),ej(a.g,d.i,d.g));break;default:d=Zi(d),ej(a.g,d.i,d.g)}}nj(a,c)}},zj),ok=Sj(Yj,zj),pk=Sj(Zj,xj),qk=Tj(function(a,b,c){b=Rj(yg,b,!0);if(b!=null&&b.length){c=mj(a,c);for(let d=0;d<b.length;d++)gj(a.g,b[d]);nj(a,c)}},xj),rk=Sj(Zj,xj),sk=Sj(function(a,b,c){b=Ag(b);b!=null&&(lj(a,c,5),hj(a.g,b))},tj()),tk=Sj(ak,vj),uk=Sj(ak,vj),vk=Sj(ak,vj),wk=Sj(bk,wj),xk=Tj(function(a,b,c){b=Rj(bh,b,!0);if(b!=null)for(let g=0;g<b.length;g++){var d=a,e=c,f=b[g];f!=null&&(f=Me(f),lj(d,e,2), 
fj(d.g,f.length),kj(d,f))}},wj),yk=Sj(bk,wj),zk=Sj(bk,wj),Ak=function(a,b,c=uj){return new Cj(b,c)}(function(a,b,c,d,e){if(a.g()!==2)return!1;var f=a.i;d=yh(void 0,d,!0);var g=b[x]|0;if(g&2)throw Error();const h=g&128?Xf:void 0;let k=Vh(b,c,h),l=k===wf?7:k[x]|0,m=Wh(l,g);if(2&m||Zh(m)||16&m)k=[...k],l=0,m=Xh(m,g),Nh(b,g,c,k,h);m&=-13;m!==l&&(k[x]=m);k.push(d);f.call(a,d,e);return!0},function(a,b,c,d,e){if(Array.isArray(b))for(let f=0;f<b.length;f++)ck(a,b[f],c,d,e)}),J=new Cj(ck,uj),Bk=Sj(function(a, 
b,c){b=Ag(b);b!=null&&b!=null&&(lj(a,c,0),fj(a.g,b))},tj()),Ck=Sj(dk,Bj),Dk=Tj(function(a,b,c){b=Rj(yg,b,!0);if(b!=null&&b.length){c=mj(a,c);for(let d=0;d<b.length;d++)gj(a.g,b[d]);nj(a,c)}},Bj),Ek=Sj(dk,Bj);function Fk(a){return()=>{var b;(b=a[rf])||(b=new a,Of(b.P),b=a[rf]=b);return b}}function Gk(a){return b=>{const c=new sj;Pj(b.P,c,Lj(a));return oj(c)}}function Hk(a){return b=>{if(b==null||b=="")b=new a;else{b=JSON.parse(b);if(!Array.isArray(b))throw Error("dnarr");b=new a(Pf(b))}return b}};Rd`https://www.google.com/recaptcha/api2/aframe`;var Ik={passive:!0},Jk=sc(()=>{let a=!1;try{const b=Object.defineProperty({},"passive",{get(){a=!0}});r.addEventListener("test",null,b)}catch(b){}return a});function Kk(a){return a?a.passive&&Jk()?a:a.capture||!1:!1}function Lk(a,b,c,d){return typeof a.addEventListener==="function"?(a.addEventListener(b,c,Kk(d)),!0):!1}function Mk(a,b,c,d){return typeof a.removeEventListener==="function"?(a.removeEventListener(b,c,Kk(d)),!0):!1};function Nk(a){var b=window;new Promise((c,d)=>{function e(){f.onload=null;f.onerror=null;f.parentElement?.removeChild(f)}const f=b.document.createElement("script");f.onload=()=>{e();c()};f.onerror=()=>{e();d(void 0)};f.type="text/javascript";fd(f,a);b.document.readyState!=="complete"?Lk(b,"load",()=>{b.document.body.appendChild(f)}):b.document.body.appendChild(f)})};function Ok(){Pk||(Pk=new Qk);return Pk}async function Rk(){try{q(await q(window.android.webview.getExperimentalMediaIntegrityTokenProvider({cloudProjectNumber:187810013193})))}catch(a){a.mediaIntegrityErrorName||a.code&&a.code()}}var Qk=class{constructor(){this.g=!1}i(){return window.android&&window.android.webview&&window.android.webview.getExperimentalMediaIntegrityTokenProvider}},Pk;async function Sk(a){var b=`${a.lb?"https://ep1.adtrafficquality.google/getconfig/sodar":"https://pagead2.googlesyndication.com/getconfig/sodar"}?sv=${200}&tid=${a.g}`+`&tv=${a.i}&st=`+`${a.Fc}`;let c=void 0;try{c=q(await q(Tk(b)))}catch(g){}if(c){b=a.dd||c.sodar_query_id;var d=c.rc_enable!==void 0&&a.j?c.rc_enable:"n",e=c.bg_snapshot_delay_ms===void 0?"0":c.bg_snapshot_delay_ms,f=c.is_gen_204===void 0?"1":c.is_gen_204;if(b&&c.bg_hash_basename&&c.bg_binary)return{context:a.l,bj:c.bg_hash_basename, 
aj:c.bg_binary,qk:a.g+"_"+a.i,dd:b,Fc:a.Fc,ce:d,Le:e,ae:f,lb:a.lb}}}let Tk=a=>new Promise((b,c)=>{const d=new XMLHttpRequest;d.onreadystatechange=()=>{d.readyState===d.DONE&&(d.status>=200&&d.status<300?b(JSON.parse(d.responseText)):c())};d.open("GET",a,!0);d.send()}); 
async function Uk(a){if(a.A){Ok().g=!0;var b=Ok();b.i()&&b.g&&Rk()}if(a=q(await q(Sk(a)))){b=window;var c=b.GoogleGcLKhOms;c&&typeof c.push==="function"||(c=b.GoogleGcLKhOms=[]);c.push({_ctx_:a.context,_bgv_:a.bj,_bgp_:a.aj,_li_:a.qk,_jk_:a.dd,_st_:a.Fc,_rc_:a.ce,_dl_:a.Le,_g2_:a.ae,_atqg_:a.lb===void 0?"0":a.lb?"1":"0"});if(c=b.GoogleDX5YKUSk)b.GoogleDX5YKUSk=void 0,c[1]();a=a.lb?Rd`https://ep2.adtrafficquality.google/sodar/${"sodar2"}.js`:Rd`https://tpc.googlesyndication.com/sodar/${"sodar2"}.js`; 
Nk(a)}};function Vk(a,b){return Ri(a,1,b)}var Wk=class extends H{g(){return E(this,1)}};function Xk(a,b){return A(a,5,b)}function Yk(a){return G(a,2,1)}function Zk(a,b){return Ri(a,3,b)}function $k(a){return G(a,4,1)}function al(a,b){return Mi(a,6,b)}var bl=class extends H{};function cl(a){switch(a){case 1:return"gda";case 2:return"gpt";case 3:return"ima";case 4:return"pal";case 5:return"xfad";case 6:return"dv3n";case 7:return"spa";default:return"unk"}} 
var dl=class{constructor(a){this.g=a.j;this.i=a.A;this.l=a.l;this.dd=a.dd;this.B=a.da();this.Fc=a.Fc;this.ce=a.ce;this.Le=a.Le;this.ae=a.ae;this.j=a.g;this.lb=a.lb;this.A=a.i}},el=class{constructor(a,b,c){this.j=a;this.A=b;this.l=c;this.B=window;this.Fc="env";this.ce="n";this.Le="0";this.ae="1";this.g=!0;this.i=this.lb=!1}da(){return this.B}build(){return new dl(this)}};var fl=class extends H{};function gl(a){var b=new hl;return Qi(b,1,a)}var hl=class extends H{getValue(){return E(this,1)}getVersion(){return F(this,5)}};var il=class extends H{};var wl=class extends H{};function xl(a,b,c=null,d=!1,e=!1){yl(a,b,c,d,e)}function yl(a,b,c,d,e=!1){a.google_image_requests||(a.google_image_requests=[]);const f=Zd("IMG",a.document);if(c||d){const g=h=>{c&&c(h);d&&$a(a.google_image_requests,f);Mk(f,"load",g);Mk(f,"error",g)};Lk(f,"load",g);Lk(f,"error",g)}e&&(f.attributionSrc="");f.src=b;a.google_image_requests.push(f)} 
function zl(a,b){let c=`https://${"pagead2.googlesyndication.com"}/pagead/gen_204?id=${b}`;be(a,(d,e)=>{if(d||d===0)c+=`&${e}=${encodeURIComponent(String(d))}`});Al(c)}function Al(a){var b=window;b.fetch?b.fetch(a,{keepalive:!0,credentials:"include",redirect:"follow",method:"get",mode:"no-cors"}):xl(b,a,void 0,!1,!1)};let Bl=null;var Cl=window;var Dl=class extends H{};var El=class extends H{getCorrelator(){return vi(this,1)}setCorrelator(a){return Pi(this,1,a)}};var Fl=class extends H{};let Gl=null,Hl=null;function Il(){if(Gl!=null)return Gl;Gl=!1;try{const a=Xd(r);a&&a.location.hash.indexOf("google_logging")!==-1&&(Gl=!0)}catch(a){}return Gl}function Jl(){if(Hl!=null)return Hl;Hl=!1;try{const a=Xd(r);a&&a.location.hash.indexOf("auto_ads_logging")!==-1&&(Hl=!0)}catch(a){}return Hl}var Kl=(a,b=[])=>{let c=!1;r.google_logging_queue||(c=!0,r.google_logging_queue=[]);r.google_logging_queue.push([a,b]);c&&Il()&&Yd(r.document,Rd`https://pagead2.googlesyndication.com/pagead/js/logging_library.js`)};function Ll(a,b,c){return Math.min(Math.max(a,b),c)}function Ml(a){return Array.prototype.reduce.call(arguments,function(b,c){return b+c},0)}function Nl(a){return Ml.apply(null,arguments)/arguments.length};function Ol(a,b){this.x=a!==void 0?a:0;this.y=b!==void 0?b:0}ba=Ol.prototype;ba.equals=function(a){return a instanceof Ol&&(this==a?!0:this&&a?this.x==a.x&&this.y==a.y:!1)};ba.ceil=function(){this.x=Math.ceil(this.x);this.y=Math.ceil(this.y);return this};ba.floor=function(){this.x=Math.floor(this.x);this.y=Math.floor(this.y);return this};ba.round=function(){this.x=Math.round(this.x);this.y=Math.round(this.y);return this};ba.scale=function(a,b){this.x*=a;this.y*=typeof b==="number"?b:a;return this};function Pl(a,b,c,d){this.top=a;this.right=b;this.bottom=c;this.left=d}ba=Pl.prototype;ba.getWidth=function(){return this.right-this.left};ba.getHeight=function(){return this.bottom-this.top};function Ql(a){return new Pl(a.top,a.right,a.bottom,a.left)}ba.contains=function(a){return this&&a?a instanceof Pl?a.left>=this.left&&a.right<=this.right&&a.top>=this.top&&a.bottom<=this.bottom:a.x>=this.left&&a.x<=this.right&&a.y>=this.top&&a.y<=this.bottom:!1}; 
function Rl(a,b){return a.left<=b.right&&b.left<=a.right&&a.top<=b.bottom&&b.top<=a.bottom}ba.ceil=function(){this.top=Math.ceil(this.top);this.right=Math.ceil(this.right);this.bottom=Math.ceil(this.bottom);this.left=Math.ceil(this.left);return this};ba.floor=function(){this.top=Math.floor(this.top);this.right=Math.floor(this.right);this.bottom=Math.floor(this.bottom);this.left=Math.floor(this.left);return this}; 
ba.round=function(){this.top=Math.round(this.top);this.right=Math.round(this.right);this.bottom=Math.round(this.bottom);this.left=Math.round(this.left);return this};ba.scale=function(a,b){b=typeof b==="number"?b:a;this.left*=a;this.right*=a;this.top*=b;this.bottom*=b;return this};function Sl(a,b){this.width=a;this.height=b}function Tl(a,b){return a==b?!0:a&&b?a.width==b.width&&a.height==b.height:!1}ba=Sl.prototype;ba.aspectRatio=function(){return this.width/this.height};ba.isEmpty=function(){return!(this.width*this.height)};ba.ceil=function(){this.width=Math.ceil(this.width);this.height=Math.ceil(this.height);return this};ba.floor=function(){this.width=Math.floor(this.width);this.height=Math.floor(this.height);return this}; 
ba.round=function(){this.width=Math.round(this.width);this.height=Math.round(this.height);return this};ba.scale=function(a,b){this.width*=a;this.height*=typeof b==="number"?b:a;return this};function Ul(a,b,c,d){this.left=a;this.top=b;this.width=c;this.height=d}function Vl(a,b){const c=Math.max(a.left,b.left),d=Math.min(a.left+a.width,b.left+b.width);if(c<=d){const e=Math.max(a.top,b.top);a=Math.min(a.top+a.height,b.top+b.height);if(e<=a)return new Ul(c,e,d-c,a-e)}return null} 
function Wl(a,b){var c=Vl(a,b);if(!c||!c.height||!c.width)return[new Ul(a.left,a.top,a.width,a.height)];c=[];let d=a.top,e=a.height;const f=a.left+a.width,g=a.top+a.height,h=b.left+b.width,k=b.top+b.height;b.top>a.top&&(c.push(new Ul(a.left,a.top,a.width,b.top-a.top)),d=b.top,e-=b.top-a.top);k<g&&(c.push(new Ul(a.left,k,a.width,g-k)),e=k-d);b.left>a.left&&c.push(new Ul(a.left,d,b.left-a.left,e));h<f&&c.push(new Ul(h,d,f-h,e));return c}ba=Ul.prototype; 
ba.contains=function(a){return a instanceof Ol?a.x>=this.left&&a.x<=this.left+this.width&&a.y>=this.top&&a.y<=this.top+this.height:this.left<=a.left&&this.left+this.width>=a.left+a.width&&this.top<=a.top&&this.top+this.height>=a.top+a.height};ba.ceil=function(){this.left=Math.ceil(this.left);this.top=Math.ceil(this.top);this.width=Math.ceil(this.width);this.height=Math.ceil(this.height);return this}; 
ba.floor=function(){this.left=Math.floor(this.left);this.top=Math.floor(this.top);this.width=Math.floor(this.width);this.height=Math.floor(this.height);return this};ba.round=function(){this.left=Math.round(this.left);this.top=Math.round(this.top);this.width=Math.round(this.width);this.height=Math.round(this.height);return this};ba.scale=function(a,b){b=typeof b==="number"?b:a;this.left*=a;this.width*=a;this.top*=b;this.height*=b;return this};const Xl={"AMP-CAROUSEL":"ac","AMP-FX-FLYING-CARPET":"fc","AMP-LIGHTBOX":"lb","AMP-STICKY-AD":"sa"};function Yl(a=r){let b=a.context||a.AMP_CONTEXT_DATA;if(!b)try{b=a.parent.context||a.parent.AMP_CONTEXT_DATA}catch{}return b?.pageViewId&&b?.canonicalUrl?b:null}function Zl(a=Yl()){return a&&a.mode?+a.mode.version||null:null}function $l(a=Yl()){if(a&&a.container){a=a.container.split(",");const b=[];for(let c=0;c<a.length;c++)b.push(Xl[a[c]]||"x");return b.join()}return null} 
function am(){var a=Yl();return a&&a.initialIntersection}function bm(){const a=am();return a&&a.rootBounds&&ta(a.rootBounds)?new Sl(a.rootBounds.width,a.rootBounds.height):null}function cm(a=Yl()){return a?Ud(a.master)?a.master:null:null} 
function dm(a,b){const c=a.ampInaboxIframes=a.ampInaboxIframes||[];let d=()=>{},e=()=>{};b&&(c.push(b),e=()=>{a.AMP&&a.AMP.inaboxUnregisterIframe&&a.AMP.inaboxUnregisterIframe(b);$a(c,b);d()});if(a.ampInaboxInitialized)return e;a.ampInaboxPendingMessages=a.ampInaboxPendingMessages||[];const f=g=>{if(a.ampInaboxInitialized)g=!0;else{var h,k=g.data==="amp-ini-load";a.ampInaboxPendingMessages&&!k&&(h=/^amp-(\d{15,20})?/.exec(g.data))&&(a.ampInaboxPendingMessages.push(g),g=h[1],a.ampInaboxInitialized|| 
g&&!/^\d{15,20}$/.test(g)||a.document.querySelector('script[src$="amp4ads-host-v0.js"]')||Yd(a.document,g?Rd`https://cdn.ampproject.org/rtv/${g}/amp4ads-host-v0.js`:Rd`https://cdn.ampproject.org/amp4ads-host-v0.js`));g=!1}g&&d()};c.google_amp_listener_added||(c.google_amp_listener_added=!0,Lk(a,"message",f),d=()=>{Mk(a,"message",f)});return e};function em(a){return a?new fm(gm(a)):La||(La=new fm)}function hm(a){a=a.document;a=a.compatMode=="CSS1Compat"?a.documentElement:a.body;return new Sl(a.clientWidth,a.clientHeight)}function im(a){const b=a.scrollingElement?a.scrollingElement:hc||a.compatMode!="CSS1Compat"?a.body||a.documentElement:a.documentElement;a=a.defaultView;return new Ol(a.pageXOffset||b.scrollLeft,a.pageYOffset||b.scrollTop)} 
function jm(a,b){b=String(b);a.contentType==="application/xhtml+xml"&&(b=b.toLowerCase());return a.createElement(b)}function km(a){a&&a.parentNode&&a.parentNode.removeChild(a)}function gm(a){return a.nodeType==9?a:a.ownerDocument||a.document}var lm={SCRIPT:1,STYLE:1,HEAD:1,IFRAME:1,OBJECT:1},mm={IMG:" ",BR:"\n"}; 
function nm(a){const b=[];om(a,b,!0);a=b.join("");a=a.replace(/ \xAD /g," ").replace(/\xAD/g,"");a=a.replace(/\u200B/g,"");a=a.replace(/ +/g," ");a!=" "&&(a=a.replace(/^\s*/,""));return a}function om(a,b,c){if(!(a.nodeName in lm))if(a.nodeType==3)c?b.push(String(a.nodeValue).replace(/(\r\n|\r|\n)/g,"")):b.push(a.nodeValue);else if(a.nodeName in mm)b.push(mm[a.nodeName]);else for(a=a.firstChild;a;)om(a,b,c),a=a.nextSibling} 
function pm(a,b,c){if(!b&&!c)return null;const d=b?String(b).toUpperCase():null;return qm(a,function(e){return(!d||e.nodeName==d)&&(!c||typeof e.className==="string"&&Za(e.className.split(/\s+/),c))})}function qm(a,b){let c=0;for(;a;){if(b(a))return a;a=a.parentNode;c++}return null}function fm(a){this.g=a||r.document||document}ba=fm.prototype;ba.Ci=function(a){var b=this.g;return typeof a==="string"?b.getElementById(a):a};ba.Dl=fm.prototype.Ci;function rm(a,b){return jm(a.g,b)} 
function sm(a,b){var c=a.g;a=jm(c,"DIV");hd(a,b);if(a.childNodes.length==1)b=a.removeChild(a.firstChild);else for(b=c.createDocumentFragment();a.firstChild;)b.appendChild(a.firstChild);return b}ba.da=function(){return this.g.defaultView};ba.contains=function(a,b){return a&&b?a==b||a.contains(b):!1}; 
ba.Lj=function(a){let b;const c=arguments.length;if(!c)return null;if(c==1)return arguments[0];const d=[];let e=Infinity;for(b=0;b<c;b++){for(var f=[],g=arguments[b];g;)f.unshift(g),g=g.parentNode;d.push(f);e=Math.min(e,f.length)}f=null;for(b=0;b<e;b++){g=d[0][b];for(let h=1;h<c;h++)if(g!=d[h][b])return f;f=g}return f};function tm(a,b,c){if(typeof b==="string")(b=um(a,b))&&(a.style[b]=c);else for(const e in b){c=a;var d=b[e];const f=um(c,e);f&&(c.style[f]=d)}}var vm={};function um(a,b){let c=vm[b];if(!c){var d=vd(b);c=d;a.style[d]===void 0&&(d=(hc?"Webkit":gc?"Moz":null)+wd(d),a.style[d]!==void 0&&(c=d));vm[b]=c}return c}function wm(a,b){const c=a.style[vd(b)];return typeof c!=="undefined"?c:a.style[um(a,b)]||""} 
function xm(a,b){const c=gm(a);return c.defaultView&&c.defaultView.getComputedStyle&&(a=c.defaultView.getComputedStyle(a,null))?a[b]||a.getPropertyValue(b)||"":""}function ym(a,b){return xm(a,b)||(a.currentStyle?a.currentStyle[b]:null)||a.style&&a.style[b]}function zm(a){try{return a.getBoundingClientRect()}catch(b){return{left:0,top:0,right:0,bottom:0}}} 
function Am(a){var b=gm(a);const c=new Ol(0,0);if(a==(b?gm(b):document).documentElement)return c;a=zm(a);b=em(b);b=im(b.g);c.x=a.left+b.x;c.y=a.top+b.y;return c}function Bm(a){var b=Cm;if(ym(a,"display")!="none")return b(a);const c=a.style,d=c.display,e=c.visibility,f=c.position;c.visibility="hidden";c.position="absolute";c.display="inline";a=b(a);c.display=d;c.position=f;c.visibility=e;return a} 
function Cm(a){const b=a.offsetWidth,c=a.offsetHeight,d=hc&&!b&&!c;return(b===void 0||d)&&a.getBoundingClientRect?(a=zm(a),new Sl(a.right-a.left,a.bottom-a.top)):new Sl(b,c)};var Em=(a,b)=>{a=Dm(a);if(!a)return b;const c=b.slice(-1);return b+(c==="?"||c==="#"?"":"&")+a},Dm=a=>Object.entries(Fm(a)).map(([b,c])=>`${b}=${encodeURIComponent(String(c))}`).join("&"),Fm=a=>{const b={};be(a,(c,d)=>{if(c||c===0||c===!1)typeof c==="boolean"&&(c=c?1:0),b[d]=c});return b},Gm=a=>{a=a.google_unique_id;return typeof a==="number"?a:0},Hm=a=>{let b;b=a.nodeType!==9&&a.id;a:{if(a&&a.nodeName&&a.parentElement){var c=a.nodeName.toString().toLowerCase();const d=a.parentElement.childNodes; 
let e=0;for(let f=0;f<d.length;++f){const g=d[f];if(g.nodeName&&g.nodeName.toString().toLowerCase()===c){if(a===g){c="."+e;break a}++e}}}c=""}return(a.nodeName&&a.nodeName.toString().toLowerCase())+(b?"/"+b:"")+c},Im=a=>(a=a.google_ad_format)?a.indexOf("_0ads")>0:!1,Jm=a=>{let b=Number(a.google_ad_width),c=Number(a.google_ad_height);if(!(b>0&&c>0)){a:{try{const e=String(a.google_ad_format);if(e&&e.match){const f=e.match(/(\d+)x(\d+)/i);if(f){const g=parseInt(f[1],10),h=parseInt(f[2],10);if(g>0&&h> 
0){var d={width:g,height:h};break a}}}}catch(e){}d=null}a=d;if(!a)return null;b=b>0?b:a.width;c=c>0?c:a.height}return{width:b,height:c}},Km=a=>{if(!a)return"";a=a.toLowerCase();a.substring(0,3)!="ca-"&&(a="ca-"+a);return a};var Lm=class{constructor(a,b){this.error=a;this.meta={};this.context=b.context;this.msg=b.message||"";this.id=b.id||"jserror"}};function Mm(a){return new Lm(a,{message:Nm(a)})}function Nm(a){let b=a.toString();a.name&&b.indexOf(a.name)==-1&&(b+=": "+a.name);a.message&&b.indexOf(a.message)==-1&&(b+=": "+a.message);a.stack&&(b=Om(a.stack,b));return b}function Om(a,b){try{a.indexOf(b)==-1&&(a=b+"\n"+a);let c;for(;a!=c;)c=a,a=a.replace(RegExp("((https?:/..*/)[^/:]*:\\d+(?:.|\n)*)\\2"),"$1");return a.replace(RegExp("\n *","g"),"\n")}catch(c){return b}};const Pm=RegExp("^https?://(\\w|-)+\\.cdn\\.ampproject\\.(net|org)(\\?|/|$)");var Qm=class{constructor(a,b){this.g=a;this.i=b}},Rm=class{constructor(a,b,c){this.url=a;this.B=b;this.g=!!c;this.depth=null}};let Sm=null;function Tm(){var a=window;if(Sm===null){Sm="";try{let b="";try{b=a.top.location.hash}catch(c){b=a.location.hash}if(b){const c=b.match(/\bdeid=([\d,]+)/);Sm=c?c[1]:""}}catch(b){}}return Sm};function Um(){const a=r.performance;return a&&a.now&&a.timing?Math.floor(a.now()+a.timing.navigationStart):Date.now()}function Vm(){const a=r.performance;return a&&a.now?a.now():null};var Wm=class{constructor(a,b){var c=Vm()||Um();this.label=a;this.type=b;this.value=c;this.duration=0;this.taskId=this.slotId=void 0;this.uniqueId=Math.random()}};const Xm=r.performance,Ym=!!(Xm&&Xm.mark&&Xm.measure&&Xm.clearMarks),Zm=sc(()=>{var a;if(a=Ym)a=Tm(),a=!!a.indexOf&&a.indexOf("1337")>=0;return a});function $m(a){a&&Xm&&Zm()&&(Xm.clearMarks(`goog_${a.label}_${a.uniqueId}_start`),Xm.clearMarks(`goog_${a.label}_${a.uniqueId}_end`))}function an(a){a.g=!1;a.i!==a.j.google_js_reporting_queue&&(Zm()&&Na(a.i,$m),a.i.length=0)}function bn(a,b){if(!a.g)return b();const c=a.start("491",3);let d;try{d=b()}catch(e){throw $m(c),e;}a.end(c);return d} 
var cn=class{constructor(a){this.i=[];this.j=a||r;let b=null;a&&(a.google_js_reporting_queue=a.google_js_reporting_queue||[],this.i=a.google_js_reporting_queue,b=a.google_measure_js_timing);this.g=Zm()||(b!=null?b:Math.random()<1)}start(a,b){if(!this.g)return null;a=new Wm(a,b);b=`goog_${a.label}_${a.uniqueId}_start`;Xm&&Zm()&&Xm.mark(b);return a}end(a){if(this.g&&typeof a.value==="number"){a.duration=(Vm()||Um())-a.value;var b=`goog_${a.label}_${a.uniqueId}_end`;Xm&&Zm()&&Xm.mark(b);!this.g||this.i.length> 
2048||this.i.push(a)}}};function dn(a,b){const c={};c[a]=b;return[c]}function en(a,b,c,d,e){const f=[];be(a,(g,h)=>{(g=fn(g,b,c,d,e))&&f.push(`${h}=${g}`)});return f.join(b)} 
function fn(a,b,c,d,e){if(a==null)return"";b=b||"&";c=c||",$";typeof c==="string"&&(c=c.split(""));if(a instanceof Array){if(d||(d=0),d<c.length){const f=[];for(let g=0;g<a.length;g++)f.push(fn(a[g],b,c,d+1,e));return f.join(c[d])}}else if(typeof a==="object")return e||(e=0),e<2?encodeURIComponent(en(a,b,c,d,e+1)):"...";return encodeURIComponent(String(a))}function gn(a){let b=1;for(const c in a.i)c.length>b&&(b=c.length);return 3997-b-a.j.length-1} 
function hn(a,b,c,d){b=b+"//"+c+d;let e=gn(a)-d.length;if(e<0)return"";a.g.sort((f,g)=>f-g);d=null;c="";for(let f=0;f<a.g.length;f++){const g=a.g[f],h=a.i[g];for(let k=0;k<h.length;k++){if(!e){d=d==null?g:d;break}let l=en(h[k],a.j,",$");if(l){l=c+l;if(e>=l.length){e-=l.length;b+=l;c=a.j;break}d=d==null?g:d}}}a="";d!=null&&(a=`${c}${"trn"}=${d}`);return b+a}var jn=class{constructor(){this.j="&";this.i={};this.A=0;this.g=[]}};var mn=class{constructor(a=null){this.I=kn;this.i=a;this.g=null;this.A=!1;this.eb=this.ma}j(a){this.g=a}l(a){this.A=a}sb(a,b,c){let d,e;try{this.i&&this.i.g?(e=this.i.start(a.toString(),3),d=b(),this.i.end(e)):d=b()}catch(f){b=!0;try{$m(e),b=this.eb(a,Mm(f),void 0,c)}catch(g){this.ma(217,g)}if(b)window.console?.error?.(f);else throw f;}return d}tb(a,b,c,d){return(...e)=>this.sb(a,()=>b.apply(c,e),d)}ma(a,b,c,d,e){e=e||"jserror";let f=void 0;try{const P=new jn;var g=P;g.g.push(1);g.i[1]=dn("context", 
a);b.error&&b.meta&&b.id||(b=Mm(b));g=b;if(g.msg){b=P;var h=g.msg.substring(0,512);b.g.push(2);b.i[2]=dn("msg",h)}var k=g.meta||{};h=k;if(this.g)try{this.g(h)}catch(la){}if(d)try{d(h)}catch(la){}d=P;k=[k];d.g.push(3);d.i[3]=k;var l;if(!(l=p)){d=r;k=[];h=null;do{var m=d;if(Ud(m)){var n=m.location.href;h=m.document&&m.document.referrer||null}else n=h,h=null;k.push(new Rm(n||"",m));try{d=m.parent}catch(la){d=null}}while(d&&m!==d);for(let la=0,Ca=k.length-1;la<=Ca;++la)k[la].depth=Ca-la;m=r;if(m.location&& 
m.location.ancestorOrigins&&m.location.ancestorOrigins.length===k.length-1)for(n=1;n<k.length;++n){const la=k[n];la.url||(la.url=m.location.ancestorOrigins[n-1]||"",la.g=!0)}l=k}var p=l;let V=new Rm(r.location.href,r,!1);l=null;const Pa=p.length-1;for(m=Pa;m>=0;--m){var w=p[m];!l&&Pm.test(w.url)&&(l=w);if(w.url&&!w.g){V=w;break}}w=null;const Ra=p.length&&p[Pa].url;V.depth!==0&&Ra&&(w=p[Pa]);f=new Qm(V,w);if(f.i){p=P;var u=f.i.url||"";p.g.push(4);p.i[4]=dn("top",u)}var t={url:f.g.url||""};if(f.g.url){const la= 
f.g.url.match(xd);var B=la[1],I=la[3],U=la[4];u="";B&&(u+=B+":");I&&(u+="//",u+=I,U&&(u+=":"+U));var M=u}else M="";B=P;t=[t,{url:M}];B.g.push(5);B.i[5]=t;ln(this.I,e,P,this.A,c)}catch(P){try{ln(this.I,e,{context:"ecmserr",rctx:a,msg:Nm(P),url:f?.g.url??""},this.A,c)}catch(V){}}return!0}Sa(a,b,c){b.catch(d=>{d=d?d:"unknown rejection";this.ma(a,d instanceof Error?d:Error(d),void 0,c||this.g||void 0)})}};var nn=class extends H{};var on=Gk([0,Ek,yk]);function pn(a,b){try{const c=d=>[{[d.kb]:d.re}];return JSON.stringify([a.filter(d=>d.Ra).map(c),th(b),a.filter(d=>!d.Ra).map(c)])}catch(c){return qn(c,b),""}}function rn(a,b){const c=new sj;try{const d=a.filter(f=>f.Ra).map(sn);qj(c,1,d);pj(c,2,on(b),rj);const e=a.filter(f=>!f.Ra).map(sn);qj(c,3,e)}catch(d){qn(d,b)}return oj(c)}function qn(a,b){try{zl({m:Nm(a instanceof Error?a:Error(String(a))),b:F(b,1)||null,v:E(b,2)||null},"rcs_internal")}catch(c){}} 
function sn(a){const b=new sj;pj(b,a.kb,a.Ke,rj);return oj(b)}function rj(a,b){kj(b,a.subarray(0,a.length))}var tn=class{constructor(a,b){var c=new nn;a=G(c,1,a);b=Ri(a,2,b);this.i=Gh(b)}};function un(a){return Math.round(a)};function vn(a,b){return ei(a,1,wn,ah(b))}function xn(a,b){return ei(a,2,wn,Hg(b))}function yn(a,b){return ei(a,3,wn,b==null?b:rg(b))}var K=class extends H{},wn=[1,2,3];function zn(a,b){return ei(a,2,An,Hg(b))}function Bn(a,b){return ei(a,4,An,pg(b))}var Cn=class extends H{},An=[2,4];function Dn(a){var b=new En;return Ri(b,1,a)}function Fn(a,b){return A(a,3,b)}function L(a,b){return ri(a,4,K,b)}var En=class extends H{};var Gn=Gk([0,yk,1,[0,An,1,lk,1,fk],Ak,[0,wn,zk,lk,vk]]);var Hn=class extends H{i(){return E(this,2)}getWidth(){return E(this,3)}getHeight(){return E(this,4)}};var In=class extends H{};var Jn=class extends H{};var Kn=class extends H{};var Ln=class extends H{},Mn=[5,6];var Nn=[0,ik,-1];var On=[0,Ak,[0,Dk,[0,pk,-3]],Nn,-1];var Pn=class extends H{getValue(){return F(this,1)}};function Qn(a){var b=new Rn;return Si(b,1,a)}var Rn=class extends H{getValue(){return F(this,1)}};var Sn=class extends H{getValue(){return F(this,1)}};var Tn=class extends H{getHeight(){return ui(this,2)}};function Un(a,b){return Ni(a,1,b)}function Vn(a,b){return pi(a,2,b)}var Wn=class extends H{};var Xn=class extends H{};var Yn=class extends H{};var $n=class extends H{setError(a){return oi(this,3,Zn,a)}},Zn=[2,3];function ao(a,b){return Pi(a,1,b)}function bo(a,b){return Pi(a,2,b)}function co(a,b){return Pi(a,3,b)}function eo(a,b){return Pi(a,4,b)}function fo(a,b){return Pi(a,5,b)}function go(a,b){return di(a,8,pg(b),0)}function ho(a,b){return di(a,9,pg(b),0)}var io=class extends H{};function jo(a,b){return Pi(a,1,b)}function ko(a,b){return Pi(a,2,b)}var lo=class extends H{};function mo(a,b){ri(a,1,lo,b)}var bi=class extends H{};var no=class extends H{};function oo(a,b){return ci(a,1,b,$g)}function po(a,b){return ci(a,12,b,Vg)}function qo(){var a=new ro;return qi(a,2,$g,"irr",bh)}function so(a,b){return Mi(a,3,b)}function to(a,b){return Mi(a,4,b)}function uo(a,b){return Mi(a,5,b)}function vo(a,b){return Mi(a,7,b)}function wo(a,b){return Mi(a,8,b)}function xo(a,b){return Pi(a,9,b)}function yo(a,b){return pi(a,10,b)}function zo(a,b){return ci(a,11,b,Bg)}var ro=class extends H{};function Ao(a){var b=Bo();A(a,1,b)}function Co(a,b){return Pi(a,2,b)}function Do(a,b){return pi(a,3,b)}function Eo(a,b){return pi(a,4,b)}function Fo(a,b){return ri(a,4,Rn,b)}function Go(a,b){return pi(a,5,b)}function Ho(a,b){return ci(a,6,b,$g)}function Io(a,b){return Pi(a,7,b)}function Jo(a,b){return Pi(a,8,b)}function Ko(a,b){A(a,9,b)}function Lo(a,b){return Mi(a,10,b)}function Mo(a,b){return Mi(a,11,b)}function No(a,b){return Mi(a,12,b)}var Oo=class extends H{};var Po=class extends H{};var Qo=class extends H{};function Ro(a){var b=new So;return G(b,1,a)}var So=class extends H{};var To=class extends H{};var Uo=class extends H{};var Vo=class extends H{};var Wo=class extends H{},Xo=[1,2];var Yo=class extends H{};var Zo=class extends H{},$o=[1];function ap(a){var b=new bp;return G(b,1,a)}var bp=class extends H{};var cp=class extends H{};var dp=class extends H{};var ep=class extends H{};var fp=class extends H{};var gp=class extends H{};var hp=class extends H{getContentUrl(){return E(this,1)}};var ip=class extends H{};function jp(a){var b=new kp;return ci(b,1,a,vg)}var kp=class extends H{};var lp=class extends H{};function mp(){var a=new np,b=new lp;return oi(a,1,op,b)}function pp(){var a=new np,b=new lp;return oi(a,9,op,b)}function qp(){var a=new np,b=new lp;return oi(a,13,op,b)}function rp(a,b){return oi(a,14,op,b)}var np=class extends H{},op=[1,2,3,5,6,7,8,9,10,11,12,13,14];var sp=class extends H{};var tp=class extends H{};var up=class extends H{};var vp=class extends H{};function wp(a,b){return di(a,10,Wg(b),"0")}function xp(a,b){return G(a,1,b)}var yp=class extends H{};var zp=class extends H{};var Ap=class extends H{};var Cp=class extends H{i(){return Ai(this,zp,4,Bp)}g(){return Qh(this,zp,4,Bp)}},Bp=[4,5];function Dp(a){var b=new Ep;return Ri(b,4,a)}function Fp(a,b){return Mh(a,6,Wg(b))}var Ep=class extends H{};var Gp=class extends H{};var Hp=class extends H{i(){return z(this,zp,1)}g(){return Oh(this,zp,1)}};var Ip=class extends H{};var Jp=class extends H{};var Kp=class extends H{};var Lp=class extends H{};var Mp=class extends H{},Np=[2,3];var Op=class extends H{},Pp=[3,4,5,6,7,8,9,11,12,13,14,16,17];function Qp(a,b){return Pi(a,3,b)}var Rp=class extends H{},Sp=[4,5,6,8,9,10,11,12,13,14,15,16,17];var Tp=[0];var Up=[0,uk,Ak,[0,Ek,yk,-2,rk,-2,[0,yk,2,rk,-1,yk,[0,rk,-2,gk],-1],Ak,[0,yk,xk],ok,wk,pk],kk,[0,uk,kk,uk,-2]];var Vp=[0,ik,-1];var Wp=Gk([0,Sp,kk,yk,kk,J,[0,Nn,-1,[0,Ck],yk,uk],J,[0,[0,xk,-1,uk,-5,kk,Ak,[0,yk,kk,uk,-1],jk,nk,ok],kk,Ak,[0,Ck],Ak,[0,Ck],Ak,[0,Ck],xk,kk,-1,[0,kk,-4,2,ek,-1],uk,-2,1,Uj,[!0,wk,[0,Ak,[0,kk,-1]]],Ak,[0,yk,-2],[0,Zn,1,J,[0,pk,-2,tk,Ak,[0,pk,Ak,[0,pk,-1]]],J,[0,Ck,-1]]],J,[0,Pp,kk,-1,J,[0,Bp,[0,yk,-1,uk,ik],[0,xk,yk,-1],kk,J,Up,J,[0,Ak,[0,op,J,Tp,-2,1,J,Tp,-1,J,[0,kk],J,Tp,-5,J,[0,Dk]]],[0,pk,-1,Bk,-2,pk]],J,[0,kk,ok],J,[0,kk],J,[0,ik,mk,wk,yk,uk,pk],J,[0,kk],J,[0,ik,-2,yk,tk,mk,ik],J,[0,Np,kk,J, 
[0],J,[0]],[0,kk,rk,jk],J,[0,Ek],J,[0,kk,yk,ok],J,[0],J,[0,Up,kk],tk,J,[0,ok],J,[0,kk],Ek],kk,J,[0,yk,[0,rk,-1,[0,ek,-5,uk]],kk,On],J,[0,Ek,qk],J,[0,Ek,-1,yk,-1],J,[0,$o,J,[0,tk,-1]],J,[0,Ek,uk,-9],J,[0,Xo,J,[0,[0,Ek,yk,-1]],J,[0,rk,-1,yk,[0,rk,-1],-1,uk,Dk,rk,-1]],J,[0,[1,2,3,4],J,[0,[0,ik,-1],Vp,tk,wk],J,[0],J,[0,Vp],J,[0]],J,[0,[3,4,5,6,7,8],ik,[0,hk],J,[0],J,[0],J,[0],J,[0],J,[0],J,[0,[1,2,3,4,5],J,[0],-4]],J,[0,Mn,ik,-2,[0,wk,-2,tk,[0,wk,-3]],J,[0],J,[0]],J,On]);function Xp(a,b){return Pi(a,1,b)}function Yp(a,b){return Pi(a,2,b)}function Zp(a){return G(a,3,6)}var $p=class extends H{getTagSessionCorrelator(){return vi(this,1)}};var aq=Gk([0,kk,-1,Ek]);var bq=class extends H{};function tq(){var a=Ti(uq());return Ri(a,1,vq())}var wq=class extends H{};var xq=[0,[0,ik,sk,-1],yk];var yq=class extends H{};var zq=class extends H{getTagSessionCorrelator(){return vi(this,1)}};var Aq=class extends H{},Bq=[1,7],Cq=[4,6,8];var Dq=Gk([0,Bq,Cq,J,[0,Ek,yk,-1,xk,-1,xq],[0,kk,qk,yk],1,J,[0,yk,rk,xk],kk,J,[0,yk,-1,wk,[0,qk],1,Ek,yk,-1],J,[0,yk,-1,xk,-1,xq],J,[0,[1],J,[0,[0,yk,-2,Ek,yk]],[0,kk,-1]]]);class Eq{constructor(a){this.I=a;this.Ne=new Fq(this.I)}}class Fq{constructor(a){this.I=a;this.Cd=new Gq(this.I)}}class Gq{constructor(a){this.I=a;this.g=new Hq(this.I);this.ki=new Iq(this.I)}}class Hq{constructor(a){this.I=a;this.i=new Jq(this.I);this.g=new Kq(this.I)}}class Jq{constructor(a){this.I=a}pd(a){this.I.g(Fn(L(Dn("xR0Czf"),vn(new K,a.status)),Bn(new Cn,a.ud)))}}class Kq{constructor(a){this.I=a}pd(a){this.I.g(Fn(L(Dn("jM4CPd"),xn(new K,un(a.vl))),Bn(new Cn,a.ud)))}} 
class Iq{constructor(a){this.I=a;this.Li=new Lq(this.I);this.Mi=new Mq(this.I);this.df=new Nq(this.I);this.Ni=new Oq(this.I);this.Oi=new Pq(this.I);this.Pi=new Qq(this.I);this.Qi=new Rq(this.I);this.ff=new Sq(this.I);this.ij=new Tq(this.I);this.vj=new Uq(this.I);this.wj=new Vq(this.I);this.Oj=new Wq(this.I);this.Qk=new Xq(this.I)}}class Lq{constructor(a){this.I=a}Ha(a){this.I.g(Fn(L(L(Dn("VEDP7d"),vn(new K,a.language)),xn(new K,a.ya)),zn(new Cn,un(a.ea))))}} 
class Mq{constructor(a){this.I=a}Ha(a){this.I.g(Fn(L(L(Dn("igjuhc"),vn(new K,a.language)),xn(new K,a.ya)),zn(new Cn,un(a.ea))))}}class Nq{constructor(a){this.I=a}pd(a){this.I.g(Fn(L(L(L(L(L(Dn("i3zJEd"),vn(new K,a.language)),xn(new K,a.ya)),xn(new K,a.outcome)),yn(new K,a.cd)),yn(new K,a.Kf)),Bn(new Cn,a.ud)))}} 
class Oq{constructor(a){this.I=a}Ha(a){this.I.g(Fn(L(L(L(L(L(Dn("JN0hVd"),vn(new K,a.language)),xn(new K,a.ya)),xn(new K,a.outcome)),yn(new K,a.cd)),yn(new K,a.Kf)),zn(new Cn,un(a.ea))))}}class Pq{constructor(a){this.I=a}Ha(a){this.I.g(Fn(L(L(L(Dn("rmHfOd"),vn(new K,a.language)),xn(new K,a.ya)),xn(new K,a.reason)),zn(new Cn,un(a.ea))))}}class Qq{constructor(a){this.I=a}Ha(a){this.I.g(Fn(L(L(L(Dn("VEyQic"),vn(new K,a.language)),xn(new K,a.ya)),xn(new K,a.format)),zn(new Cn,un(a.ea))))}} 
class Rq{constructor(a){this.I=a}Ha(a){this.I.g(Fn(L(L(L(Dn("QFcNxc"),vn(new K,a.language)),xn(new K,a.ya)),xn(new K,a.format)),zn(new Cn,un(a.ea))))}}class Sq{constructor(a){this.I=a}Ha(a){this.I.g(Fn(L(L(L(L(Dn("SIhp4"),vn(new K,a.language)),xn(new K,a.ya)),xn(new K,a.format)),yn(new K,a.cd)),zn(new Cn,un(a.ea))))}}class Tq{constructor(a){this.I=a}Ha(a){this.I.g(Fn(L(L(L(Dn("Eeiun"),vn(new K,a.language)),xn(new K,a.ya)),xn(new K,a.format)),zn(new Cn,un(a.ea))))}} 
class Uq{constructor(a){this.I=a}Ha(a){this.I.g(Fn(L(L(Dn("zGH6sc"),vn(new K,a.language)),xn(new K,a.ya)),zn(new Cn,un(a.ea))))}}class Vq{constructor(a){this.I=a}Ha(a){this.I.g(Fn(L(L(L(Dn("SmbJl"),vn(new K,a.language)),xn(new K,a.ya)),xn(new K,a.type)),zn(new Cn,un(a.ea))))}}class Wq{constructor(a){this.I=a}Ha(a){this.I.g(Fn(L(L(L(Dn("qleBg"),vn(new K,a.language)),xn(new K,a.ya)),xn(new K,a.format)),zn(new Cn,un(a.ea))))}} 
class Xq{constructor(a){this.I=a}Ha(a){this.I.g(Fn(L(L(L(Dn("pYLGPe"),vn(new K,a.language)),xn(new K,a.ya)),xn(new K,a.type)),zn(new Cn,un(a.ea))))}}class Yq extends tn{constructor(){super(...arguments);this.se=new Eq(this)}} 
var Zq=class extends Yq{ii(...a){this.l(...a.map(b=>({Ra:!0,kb:3,re:th(b)})))}Rb(...a){this.l(...a.map(b=>({Ra:!0,kb:7,re:th(b)})))}D(...a){this.l(...a.map(b=>({Ra:!0,kb:16,re:th(b)})))}g(...a){this.l(...a.map(b=>({Ra:!1,kb:1,re:th(b)})))}},ar=class extends Yq{ii(...a){$q(this,...a.map(b=>({Ra:!0,kb:3,Ke:Dq(b)})))}Rb(...a){$q(this,...a.map(b=>({Ra:!0,kb:7,Ke:Wp(b)})))}D(...a){$q(this,...a.map(b=>({Ra:!0,kb:16,Ke:aq(b)})))}g(...a){$q(this,...a.map(b=>({Ra:!1,kb:1,Ke:Gn(b)})))}};function br(a,b){globalThis.fetch(a,{method:"POST",body:b,keepalive:b.length<65536,credentials:"omit",mode:"no-cors",redirect:"follow"}).catch(()=>{})}var cr=class extends Zq{constructor(a){super(2,a);this.j=br}l(...a){try{const b=pn(a,this.i);this.j("https://pagead2.googlesyndication.com/pagead/ping?e=1",b)}catch(b){qn(b,this.i)}}},dr=class extends cr{};function er(a){a.A!==null&&(clearTimeout(a.A),a.A=null);if(a.j.length){var b=pn(a.j,a.i);a.G("https://pagead2.googlesyndication.com/pagead/ping?e=1",b);a.j=[]}} 
var gr=class extends Zq{constructor(a,b,c,d,e){super(2,a);this.G=br;this.V=b;this.J=c;this.M=d;this.C=e;this.j=[];this.A=null;this.H=!1}l(...a){try{this.M&&pn(this.j.concat(a),this.i).length>=65536&&er(this),this.C&&!this.H&&(this.H=!0,fr(this.C,()=>{er(this)})),this.j.push(...a),this.j.length>=this.J&&er(this),this.j.length&&this.A===null&&(this.A=setTimeout(()=>{er(this)},this.V))}catch(b){qn(b,this.i)}}},hr=class extends gr{constructor(a,b=1E3,c=100,d=!1,e){super(a,b,c,d&&!0,e)}};var N=a=>{var b="Vf";if(a.Vf&&a.hasOwnProperty(b))return a.Vf;b=new a;return a.Vf=b};function ir(a,b,c){return b[a]||c};function jr(a,b){a.i=(c,d)=>ir(2,b,()=>[])(c,1,d);a.g=()=>ir(3,b,()=>[])(1)}class kr{i(){return[]}g(){return[]}}function lr(a,b){return N(kr).i(a,b)};function ln(a,b,c,d=!1,e){if((d?a.g:Math.random())<(e||.01))try{let f;c instanceof jn?f=c:(f=new jn,be(c,(h,k)=>{var l=f;const m=l.A++;h=dn(k,h);l.g.push(m);l.i[m]=h}));const g=hn(f,a.protocol,a.domain,a.path+b+"&");g&&xl(r,g)}catch(f){}}function mr(a,b){b>=0&&b<=1&&(a.g=b)}var nr=class{constructor(){this.domain="pagead2.googlesyndication.com";this.path="/pagead/gen_204?id=";this.protocol="https:";this.g=Math.random()}};let kn,or;const pr=new cn(window);(function(a){kn=a??new nr;typeof window.google_srt!=="number"&&(window.google_srt=Math.random());mr(kn,window.google_srt);or=new mn(pr);or.j(()=>{});or.l(!0);window.document.readyState==="complete"?window.google_measure_js_timing||an(pr):pr.g&&Lk(window,"load",()=>{window.google_measure_js_timing||an(pr)})})();function qr(a){or.Sa(1085,a)};let rr=(new Date).getTime();var sr={yn:0,xn:1,un:2,nn:3,vn:4,on:5,wn:6,rn:7,sn:8,mn:9,qn:10,zn:11};var tr={Bn:0,Cn:1,An:2};function ur(a,b){return a.left<b.right&&b.left<a.right&&a.top<b.bottom&&b.top<a.bottom}function vr(a){a=a.map(b=>new Pl(b.top,b.right,b.bottom,b.left));a=wr(a);return{top:a.top,right:a.right,bottom:a.bottom,left:a.left}}function wr(a){if(!a.length)throw Error("pso:box:m:nb");return a.slice(1).reduce((b,c)=>{b.left=Math.min(b.left,c.left);b.top=Math.min(b.top,c.top);b.right=Math.max(b.right,c.right);b.bottom=Math.max(b.bottom,c.bottom);return b},Ql(a[0]))};var Bc={ro:0,Zm:1,dn:2,bn:3,cn:4,kn:8,Co:9,On:10,Pn:11,Ao:16,Tm:17,Sm:24,Mn:25,xm:26,wm:27,Ei:30,Fn:32,Jn:40,Ho:41,Do:42};var xr={overlays:1,interstitials:2,vignettes:2,inserts:3,immersives:4,list_view:5,full_page:6,side_rails:7},yr={[1]:1,[2]:1,[3]:7,[4]:7,[8]:2,[27]:3,[9]:4,[30]:5};var zr=728*1.38;function Ar(a,b=-1){if(a!==a.top){if(b<0)a=!1;else{var c=Br(a,!0,!0),d=Cr(a,!0);a=c>0&&d>0&&Math.abs(1-a.screen.width/c)<=b&&Math.abs(1-a.screen.height/d)<=b}a=a?0:512}else a=0;return a}function Dr(a,b=420,c=!1,d=!1){return(a=Br(a,c,d))?a>b?32768:a<320?65536:0:16384}function Er(a){return Math.max(0,Fr(a,!0)-Cr(a))}function Gr(a){a=a.document;let b={};a&&(b=a.compatMode=="CSS1Compat"?a.documentElement:a.body);return b||{}} 
function Cr(a,b=!1){const c=Gr(a).clientHeight;return b?c*(bc()&&wc()?Ke(a):1):c}function Br(a,b=!1,c=!1){c=Gr(a).clientWidth??(c?a.innerWidth:void 0);return b?c*(bc()&&wc()?Ke(a):1):c}function Fr(a,b){const c=Gr(a);return b?(a=Cr(a),c.scrollHeight===a?c.offsetHeight:c.scrollHeight):c.offsetHeight}function Hr(a,b){var c=O(Ir);return Jr(b)||b===10||!a.adCount?!1:c||b!==1&&b!==2?(a=a.adCount[b])?a>=1:!1:!(!a.adCount[1]&&!a.adCount[2])} 
function Kr(a,b){return a&&a.source?a.source===b||a.source.parent===b:!1}function Lr(a){return a.pageYOffset===void 0?(a.document.documentElement||a.document.body.parentNode||a.document.body).scrollTop:a.pageYOffset}function Mr(a){return a.pageXOffset===void 0?(a.document.documentElement||a.document.body.parentNode||a.document.body).scrollLeft:a.pageXOffset} 
function Nr(a){const b={};let c;Array.isArray(a)?c=a:a&&a.key_value&&(c=a.key_value);if(c)for(a=0;a<c.length;a++){const d=c[a];if("key"in d&&"value"in d){const e=d.value;b[d.key]=e==null?null:String(e)}}return b}function Or(a,b,c,d){ln(c,b,{c:d.data.substring(0,500),u:a.location.href.substring(0,500)},!0,.1);return!0} 
function Pr(a){const b={bottom:"auto",clear:"none",display:"inline","float":"none",height:"auto",left:"auto",margin:0,"margin-bottom":0,"margin-left":0,"margin-right":"0","margin-top":0,"max-height":"none","max-width":"none",opacity:1,overflow:"visible",padding:0,"padding-bottom":0,"padding-left":0,"padding-right":0,"padding-top":0,position:"static",right:"auto",top:"auto","vertical-align":"baseline",visibility:"visible",width:"auto","z-index":"auto"};Na(Object.keys(b),c=>{wm(a,c)||tm(a,c,b[c])}); 
ye(a)}function Jr(a){return a===26||a===27||a===40||a===41};function Qr(a,b){Rr(a).forEach(b,void 0)}function Rr(a){const b=[],c=a.length;for(let d=0;d<c;d++)b.push(a[d]);return b};function Sr(a,b){return a.g[Tr(b)]!==void 0}function Ur(a){const b=[];for(const c in a.g)a.g[c]!==void 0&&a.g.hasOwnProperty(c)&&b.push(a.i[c]);return b}function Vr(a){const b=[];for(const c in a.g)a.g[c]!==void 0&&a.g.hasOwnProperty(c)&&b.push(a.g[c]);return b}var Wr=class{constructor(){this.g={};this.i={}}set(a,b){const c=Tr(a);this.g[c]=b;this.i[c]=a}get(a,b){a=Tr(a);return this.g[a]!==void 0?this.g[a]:b}Zc(){return Ur(this).length}clear(){this.g={};this.i={}}}; 
function Tr(a){return a instanceof Object?String(ua(a)):a+""};var Xr=class{constructor(a){this.g=new Wr;if(a)for(let b=0;b<a.length;++b)this.add(a[b])}add(a){this.g.set(a,!0)}contains(a){return Sr(this.g,a)}};const Yr=new Xr("IMG AMP-IMG IFRAME AMP-IFRAME HR EMBED OBJECT VIDEO AMP-VIDEO INPUT BUTTON SVG".split(" "));function Zr(a,{yb:b,qb:c,Zb:d}){return d&&c(b)?b:(b=b.parentElement)?$r(a,{yb:b,qb:c,Zb:!0}):null}function $r(a,{yb:b,qb:c,Zb:d=!1}){const e=as({yb:b,qb:c,Zb:d}),f=a.g.get(e);if(f)return f.element;b=Zr(a,{yb:b,qb:c,Zb:d});a.g.set(e,{element:b});return b}var bs=class{constructor(){this.g=new Map}};function as({yb:a,qb:b,Zb:c}){a=ua(a);b=ua(b);return`${a}:${b}:${c}`};function cs(a){cc(a.document.body.offsetHeight)};function ds(a){a&&typeof a.dispose=="function"&&a.dispose()};function Q(){this.A=this.A;this.H=this.H}Q.prototype.A=!1;Q.prototype.dispose=function(){this.A||(this.A=!0,this.g())};Q.prototype[ja(Symbol,"dispose")]=function(){this.dispose()};function es(a,b){fs(a,Ea(ds,b))}function fs(a,b){a.A?b():(a.H||(a.H=[]),a.H.push(b))}Q.prototype.g=function(){if(this.H)for(;this.H.length;)this.H.shift()()};function gs(a){a.i.forEach((b,c)=>{if(b.overrides.delete(a)){b=Array.from(b.overrides.values()).pop()||b.originalValue;var d=a.element;b?d.style.setProperty(c,b.value,b.priority):d.style.removeProperty(c)}})} 
function hs(a,b,c){c={value:c,priority:"important"};var d=a.i.get(b);if(!d){d=a.element;var e=d.style.getPropertyValue(b);d={originalValue:e?{value:e,priority:d.style.getPropertyPriority(b)}:null,overrides:new Map};a.i.set(b,d)}d.overrides.delete(a);d.overrides.set(a,c);a=a.element;c?a.style.setProperty(b,c.value,c.priority):a.style.removeProperty(b)} 
var is=class extends Q{constructor(a,b){super();this.element=b;a=a.googTempStyleOverrideInfo=a.googTempStyleOverrideInfo||new Map;var c=a.get(b);c?b=c:(c=new Map,a.set(b,c),b=c);this.i=b}g(){gs(this);super.g()}};function js(a){const b=new R(a.getValue());a.listen(c=>b.g(c));return b}function ks(a,b){const c=new R({first:a.O,second:b.O});a.listen(()=>c.g({first:a.O,second:b.O}));b.listen(()=>c.g({first:a.O,second:b.O}));return c}function ls(...a){const b=[...a],c=()=>b.every(f=>f.O),d=new R(c()),e=()=>{d.g(c())};b.forEach(f=>f.listen(e));return ms(d)}function ns(...a){const b=[...a],c=()=>b.findIndex(f=>f.O)!==-1,d=new R(c()),e=()=>{d.g(c())};b.forEach(f=>f.listen(e));return ms(d)} 
function ms(a,b=os){var c=a.O;const d=new R(a.O);a.listen(e=>{b(e,c)||(c=e,d.g(e))});return d}function ps(a,b,c){return a.i(d=>{d===b&&c()})}function qs(a,b,c){if(a.O===b)return c(),()=>{};const d={Ic:null};d.Ic=ps(a,b,()=>{d.Ic&&(d.Ic(),d.Ic=null);c()});return d.Ic}function rs(a,b,c){ms(a).listen(d=>{d===b&&c()})}function ss(a,b){a.A&&a.A();a.A=b.listen(c=>a.g(c),!0)} 
function ts(a,b,c,d){const e=new R(!1);var f=null;a=a.map(d);ps(a,!0,()=>{f===null&&(f=b.setTimeout(()=>{e.g(!0)},c))});ps(a,!1,()=>{e.g(!1);f!==null&&(b.clearTimeout(f),f=null)});return ms(e)}function us(a){return{listen:b=>a.listen(b),getValue:()=>a.O}} 
var R=class{constructor(a){this.O=a;this.j=new Map;this.C=1;this.A=null}listen(a,b=!1){const c=this.C++;this.j.set(c,a);b&&a(this.O);return()=>{this.j.delete(c)}}i(a){return this.listen(a,!0)}l(){return this.O}g(a){this.O=a;this.j.forEach(b=>{b(this.O)})}map(a){const b=new R(a(this.O));this.listen(c=>b.g(a(c)));return b}};function os(a,b){return a==b};function vs(a){return new ws(a)}function xs(a,b){Na(a.g,c=>{c(b)})}var ys=class{constructor(){this.g=[]}};class ws{constructor(a){this.g=a}listen(a){this.g.g.push(a)}map(a){const b=new ys;this.listen(c=>xs(b,a(c)));return vs(b)}delay(a,b){const c=new ys;this.listen(d=>{a.setTimeout(()=>{xs(c,d)},b)});return vs(c)}}function zs(...a){const b=new ys;a.forEach(c=>{c.listen(d=>{xs(b,d)})});return vs(b)};function As(a){return ms(ks(a.g,a.j).map(b=>{var c=b.first;b=b.second;return c==null||b==null?null:Bs(c,b)}))}var Ds=class{constructor(a){this.i=a;this.g=new R(null);this.j=new R(null);this.A=new ys;this.H=b=>{this.g.O==null&&b.touches.length==1&&this.g.g(b.touches[0])};this.l=b=>{const c=this.g.O;c!=null&&(b=Cs(c,b.changedTouches),b!=null&&(this.g.g(null),this.j.g(null),xs(this.A,Bs(c,b))))};this.C=b=>{var c=this.g.O;c!=null&&(c=Cs(c,b.changedTouches),c!=null&&(this.j.g(c),b.preventDefault()))}}}; 
function Bs(a,b){return{yi:b.pageX-a.pageX,zi:b.pageY-a.pageY}}function Cs(a,b){if(b==null)return null;for(let c=0;c<b.length;++c)if(b[c].identifier==a.identifier)return b[c];return null};function Es(a){return ms(ks(a.g,a.i).map(b=>{var c=b.first;b=b.second;return c==null||b==null?null:Fs(c,b)}))}var Gs=class{constructor(a,b){this.A=a;this.l=b;this.g=new R(null);this.i=new R(null);this.j=new ys;this.D=c=>{this.g.g(c)};this.C=c=>{const d=this.g.O;d!=null&&(this.g.g(null),this.i.g(null),xs(this.j,Fs(d,c)))};this.H=c=>{this.g.O!=null&&(this.i.g(c),c.preventDefault())}}};function Fs(a,b){return{yi:b.screenX-a.screenX,zi:b.screenY-a.screenY}};var Js=(a,b,c)=>{const d=new Hs(a,b,c);return()=>Is(d)};function Is(a){if(a.g)return!1;if(a.i==null)return Ks(a),!0;const b=a.i+a.l-(new Date).getTime();if(b<1)return Ks(a),!0;Ls(a,b);return!0}function Ks(a){a.i=(new Date).getTime();a.A()}function Ls(a,b){a.g=!0;a.j.setTimeout(()=>{a.g=!1;Ks(a)},b)}class Hs{constructor(a,b,c){this.j=a;this.l=b;this.A=c;this.i=null;this.g=!1}};function Ms(a){return Ns(Es(a.g),As(a.i))}function Os(a){return zs(vs(a.g.j),vs(a.i.A))}var Ps=class{constructor(a,b){this.g=a;this.i=b}};function Ns(a,b){return ks(a,b).map(({first:c,second:d})=>c||d||null)};var Qs=class{constructor(){this.cache=new Map}getBoundingClientRect(a){var b=this.cache.get(a);if(b)return b;b=a.getBoundingClientRect();this.cache.set(a,b);return b}};function Rs(a){a.C==null&&(a.C=new R(a.D.getBoundingClientRect()));return a.C}var Ss=class extends Q{constructor(a,b){super();this.j=a;this.D=b;this.G=!1;this.C=null;this.l=()=>{Rs(this).g(this.D.getBoundingClientRect())}}i(){this.G||(this.G=!0,this.j.addEventListener("resize",this.l),this.j.addEventListener("scroll",this.l));return Rs(this)}g(){this.j.removeEventListener("resize",this.l);this.j.removeEventListener("scroll",this.l);super.g()}};function Ts(a,b){return new Us(a,b)}function Vs(a){a.B.requestAnimationFrame(()=>{a.A||a.j.g(new Sl(a.element.offsetWidth,a.element.offsetHeight))})}function Ws(a){a.i||(a.i=!0,a.l.observe(a.element));return ms(a.j,Tl)}var Us=class extends Q{constructor(a,b){super();this.B=a;this.element=b;this.i=!1;this.j=new R(new Sl(this.element.offsetWidth,this.element.offsetHeight));this.l=new ResizeObserver(()=>{Vs(this)})}g(){this.l.disconnect();super.g()}};function Xs(a,b){return{top:a.g-b,right:a.j+a.i,bottom:a.g+b,left:a.j}}var Ys=class{constructor(a,b,c){this.j=a;this.g=b;this.i=c}};function Zs(a,b){a=a.getBoundingClientRect();return new $s(a.top+Lr(b),a.bottom-a.top)}function at(a){return new $s(Math.round(a.g),Math.round(a.i))}var $s=class{constructor(a,b){this.g=a;this.i=b}getHeight(){return this.i}};var ct=(a,b)=>{const c=a.google_pso_loaded_fonts||(a.google_pso_loaded_fonts=[]),d=new Xr(c);b=b.filter(e=>!d.contains(e));b.length&&(bt(a,b),fb(c,b))};function bt(a,b){for(const d of b){const e=Zd("LINK",a.document);e.type="text/css";b=e;var c=Rd`//fonts.googleapis.com/css?family=${d}`;b.href=Oc(c).toString();b.rel="stylesheet";a.document.head.appendChild(e)}};function dt(a,b){a.G?b(a.l):a.j.push(b)}function et(a,b){a.G=!0;a.l=b;a.j.forEach(c=>{c(a.l)});a.j=[]} 
var ft=class extends Q{constructor(a){super();this.i=a;this.j=[];this.G=!1;this.D=this.l=null;this.J=Js(a,1E3,()=>{if(this.D!=null){var b=Fr(this.i,!0)-this.D;b>1E3&&et(this,b)}});this.C=null}init(a,b){a==null?(this.D=a=Fr(this.i,!0),this.i.addEventListener("scroll",this.J),b!=null&&b(a)):this.C=this.i.setTimeout(()=>{this.init(void 0,b)},a)}g(){this.C!=null&&this.i.clearTimeout(this.C);this.i.removeEventListener("scroll",this.J);this.j=[];this.l=null;super.g()}};var gt=(a,b)=>a.reduce((c,d)=>c.concat(b(d)),[]);var ht=class{constructor(a=1){this.g=a}next(){const a=48271*this.g%2147483647;this.g=a*2147483647<0?a+2147483647:a;return this.g/2147483647}};function it(a,b,c){const d=[];for(const e of a.g)b(e)?d.push(e):c(e);return new jt(d)}function kt(a){return a.g.slice(0)}function lt(a){return a.g.length}function mt(a,b=1){a=kt(a);const c=new ht(b);lb(a,()=>c.next());return new jt(a)} 
var jt=class{constructor(a){this.g=a.slice(0)}forEach(a){this.g.forEach((b,c)=>void a(b,c,this))}filter(a){return new jt(Qa(this.g,a))}apply(a){return new jt(a(kt(this)))}sort(a){return new jt(kt(this).sort(a))}get(a){return this.g[a]}add(a){const b=kt(this);b.push(a);return new jt(b)}};var nt=class{constructor(a){this.g=new Xr(a)}contains(a){return this.g.contains(a)}};function ot(a){return new pt({value:a},null)}function qt(a){return new pt(null,a)}function rt(a){try{return ot(a())}catch(b){return qt(b)}}function st(a){return a.i!=null}function tt(a){return st(a)?a.getValue():null}function ut(a,b){st(a)&&b(a.getValue());return a}function vt(a,b){return st(a)?a:qt(b(a.g))}function wt(a,b){return vt(a,c=>Error(`${b}${c.message}`))}function xt(a,b){st(a)||b(a.g);return a} 
var pt=class{constructor(a,b){this.i=a;this.g=b}getValue(){return this.i.value}map(a){return st(this)?(a=a(this.getValue()),a instanceof pt?a:ot(a)):this}};var yt=class{constructor(){this.g=new Wr}set(a,b){let c=this.g.get(a);c||(c=new Xr,this.g.set(a,c));c.add(b)}};function zt(a){return!a}function At(a){return b=>{for(const c of a)c(b)}};function Bt(a){return a!==null};var Ct=class extends H{getId(){return Ji(this,3)}};var Dt=class{constructor(a,{Tg:b,Ji:c,dk:d,di:e}){this.l=a;this.j=c;this.A=new jt(b||[]);this.i=e;this.g=d}};var Et=a=>{var b=a.split("~").filter(c=>c.length>0);a=new Wr;for(const c of b)b=c.indexOf("."),b==-1?a.set(c,""):a.set(c.substring(0,b),c.substring(b+1));return a},Gt=a=>{var b=Ft(a);a=[];for(let c of b)b=String(c.Mc),a.push(c.Ub+"."+(b.length<=20?b:b.slice(0,19)+"_"));return a.join("~")}; 
const Ft=a=>{const b=[],c=a.A;c&&lt(c)&&b.push({Ub:"a",Mc:Ht(c)});a.j!=null&&b.push({Ub:"as",Mc:a.j});a.g!=null&&b.push({Ub:"i",Mc:String(a.g)});a.i!=null&&b.push({Ub:"rp",Mc:String(a.i)});b.sort(function(d,e){return d.Ub.localeCompare(e.Ub)});b.unshift({Ub:"t",Mc:It(a.l)});return b},It=a=>{switch(a){case 0:return"aa";case 1:return"ma";default:throw Error("Invalid slot type"+a);}},Ht=a=>{a=kt(a).map(Jt);a=JSON.stringify(a);return de(a)},Jt=a=>{const b={};ti(a,7)!=null&&(b.q=Ji(a,7));yg(y(a,2))!=null&& 
(b.o=Ci(a,2));yg(y(a,5))!=null&&(b.p=Ci(a,5));return b};function Kt(){var a=new Lt;return Si(a,2,1)}var Lt=class extends H{setLocation(a){return Si(this,1,a)}g(){return wg(y(this,1))}};function Mt(a){const b=[].slice.call(arguments).filter(rc(e=>e===null));if(!b.length)return null;let c=[],d={};b.forEach(e=>{c=c.concat(e.Yg||[]);d=Object.assign(d,e.bd())});return new Nt(c,d)}function Ot(a){switch(a){case 1:return new Nt(null,{google_ad_semantic_area:"mc"});case 2:return new Nt(null,{google_ad_semantic_area:"h"});case 3:return new Nt(null,{google_ad_semantic_area:"f"});case 4:return new Nt(null,{google_ad_semantic_area:"s"});default:return null}} 
function Pt(a){return a==null?null:new Nt(null,{google_ml_rank:a})}function Qt(a){return a==null?null:new Nt(null,{google_placement_id:Gt(a)})}function Rt({pj:a,Ij:b=null}){if(a==null)return null;a={google_daaos_ts:a};b!=null&&(a.google_erank=b+1);return new Nt(null,a)}var Nt=class{constructor(a,b){this.Yg=a;this.g=b}bd(){return this.g}};var St=class extends H{};var Tt=class extends H{};var Ut=class extends H{i(){return Ji(this,2)}g(){return Ji(this,5)}A(){return mi(this,Tt,3,Th())}l(){return yg(y(this,4))}C(){return Sh(this,6)}H(){return Oh(this,St,7)}};var Vt=class extends H{};var Wt=class extends H{A(){return C(this,12)}i(){return Ug(y(this,13))}g(){return si(this,23)}};var Xt=class extends H{};function Yt(a){return Sh(a,1,Kh)}var Zt=class extends H{g(){return Ki(this,3)}i(){return Bi(this,6)}};var $t=class extends H{};var au=class extends H{};var bu=class extends H{ia(){return z(this,Ct,1)}i(){return Ki(this,2)}};var cu=class extends H{};var du=class extends H{};var eu=class extends H{getName(){return Ji(this,4)}},fu=[1,2,3];var gu=class extends H{g(){return z(this,Zt,10)}};function hu(a){return Bi(a,1)}var iu=class extends H{g(){return Bi(this,2)}i(){return Bi(this,3)}};var ju=class extends H{g(){return Ug(y(this,1,void 0,Kh))}};var ku=class extends H{g(){return vi(this,1)}};var lu=class extends H{g(){return E(this,1)}i(){return E(this,2)}};var mu=class extends H{A(){return C(this,1)}l(){return C(this,3)}C(){return C(this,7)}g(){return C(this,4)}i(){return C(this,5)}};var nu=class extends H{g(){return z(this,ku,6)}i(){return z(this,mu,12)}};var ou=class extends H{};var pu=class extends H{};var qu=class extends H{};var ru=class extends H{g(){return mi(this,qu,1,Th())}};var su=class extends H{setProperty(a){return Qi(this,1,a)}getValue(){return Ji(this,2)}};var tu=class extends H{};var uu=class extends H{};var vu=class extends H{ia(){return z(this,Ct,1)}i(){return Ki(this,2)}};var wu=class extends H{};var xu=class extends H{};var yu=class extends H{g(){return yi(this,6)}};var zu=class extends H{Lf(){return Oh(this,xu,2)}};var Au=class extends H{g(){return vi(this,1)}};var Bu=class extends H{};var Du=class extends H{g(){return Ai(this,Bu,2,Cu)}},Cu=[1,2];var Eu=class extends H{g(){return z(this,Du,3)}};var Fu=class extends H{};var Gu=class extends H{g(){return mi(this,Fu,1,Th())}};var Hu=class extends H{g(){return yi(this,1)}i(){return z(this,Eu,3)}};var Iu=Hk(class extends H{g(){return z(this,Wt,15)}});var Ju=class extends H{},Ku=Hk(Ju);function Lu(a){try{const b=a.localStorage.getItem("google_ama_settings");return b?Ku(b):null}catch(b){return null}}function Mu(a,b){if(a.zf!==void 0){var c=Lu(b);c||(c=new Ju);a.zf!==void 0&&Li(c,2,a.zf);a=Date.now()+864E5;Number.isFinite(a)&&Mh(c,1,Hg(Math.round(a)));c=Ui(c);try{b.localStorage.setItem("google_ama_settings",c)}catch(d){}}else{if(c=a=Lu(b))c=vi(a,1),c=BigInt(c)<Date.now();if(c)try{b.localStorage.removeItem("google_ama_settings")}catch(d){}}};var Nu={nc:"ama_success",ub:.1,Hb:!0,sc:!0},Ou={nc:"ama_failure",ub:.1,Hb:!0,sc:!0},Pu={nc:"ama_coverage",ub:.1,Hb:!0,sc:!0},Qu={nc:"ama_opt",ub:.1,Hb:!0,sc:!1},Ru={nc:"ama_auto_rs",ub:1,Hb:!0,sc:!1},Su={nc:"ama_constraints",ub:0,Hb:!0,sc:!0};function Tu(a){if(a!=null)return Uu(a)}function Uu(a){return ag(a)?Number(a):String(a)};function Vu(a,b){Wu(a.i,Ru,{...b,evt:"place",vh:Cr(a.B),eid:Tu(a.g.g()?.g())||0,hl:z(a.g,lu,5)?.g()||""})}function Xu(a,b,c){b={sts:b};c&&(b.excp_n=c.name,b.excp_m=c.message&&c.message.substring(0,512),b.excp_s=c.stack&&Om(c.stack,"")||"");Vu(a,b)}var Yu=class{constructor(a,b,c){this.B=a;this.i=b;this.g=c}};const Zu=["-webkit-text-fill-color"];function $u(a,b){if(fc){{const d=$d(a.document.body,a);if(d){a={};var c=d.length;for(let e=0;e<c;++e)a[d[e]]="initial";a=av(a)}else a=bv()}}else a=bv();v(b,a)}function bv(){const a={all:"initial"};Na(Zu,b=>{a[b]="unset"});return a}function av(a){Na(Zu,b=>{delete a[b]});return a};var cv=class{constructor(a){this.g=a}Yc(a){const b=a.document.createElement("div");$u(a,b);v(b,{width:"100%","max-width":"1000px",margin:"auto"});b.appendChild(this.g);const c=a.document.createElement("div");$u(a,c);v(c,{width:"100%","text-align":"center",display:"block",padding:"5px 5px 2px","box-sizing":"border-box","background-color":"#FFF"});c.appendChild(b);return c}};function dv(a){if(a.nodeType!=1)var b=!1;else if(b=a.tagName=="INS")a:{b=["adsbygoogle-placeholder"];var c=a.className?a.className.split(/\s+/):[];a={};for(let d=0;d<c.length;++d)a[c[d]]=!0;for(c=0;c<b.length;++c)if(!a[b[c]]){b=!1;break a}b=!0}return b}function ev(a){return Rr(a.querySelectorAll("ins.adsbygoogle-ablated-ad-slot"))};function fv(a,b){a=rm(new fm(a),"DIV");const c=a.style;c.width="100%";c.height="auto";c.clear=b?"both":"none";return a}function gv(a,b,c){switch(c){case 0:b.parentNode&&b.parentNode.insertBefore(a,b);break;case 3:if(c=b.parentNode){let d=b.nextSibling;if(d&&d.parentNode!=c)for(;d&&d.nodeType==8;)d=d.nextSibling;c.insertBefore(a,d)}break;case 1:b.insertBefore(a,b.firstChild);break;case 2:b.appendChild(a)}dv(b)&&(b.setAttribute("data-init-display",b.style.display),b.style.display="block")} 
function hv(a){if(a&&a.parentNode){const b=a.parentNode;b.removeChild(a);dv(b)&&(b.style.display=b.getAttribute("data-init-display")||"none")}};var S=class{constructor(a,b=!1){this.g=a;this.defaultValue=b}},T=class{constructor(a,b=0){this.g=a;this.defaultValue=b}},iv=class{constructor(a,b=""){this.g=a;this.defaultValue=b}},jv=class{constructor(a,b=[]){this.g=a;this.defaultValue=b}},kv=class{constructor(a,b=[]){this.g=a;this.defaultValue=b}};var lv=new T(619278254,10),mv=new T(1359),nv=new T(1358),ov=new S(1360),pv=new T(1357),qv=new S(1345),rv=new S(687716473),sv=new S(1377),tv=new S(676894296,!0),uv=new S(682658313,!0),vv=new T(1130,100),wv=new T(1340,.2),xv=new T(1338,.3),yv=new T(1336,1),zv=new T(1339,.3),Av=new S(1337),Bv=new T(1032,200),Cv=new S(736254284),Dv=new iv(14),Ev=new T(1224,.01),Fv=new T(1346,6),Gv=new T(1347,3),Hv=new S(1342),Iv=new S(1344),Jv=new T(1343,300),Kv=new S(1384),Lv=new S(1260),Mv=new S(316),Nv=new S(1290), 
Ov=new S(334),Pv=new S(1383),Qv=new T(1263,-1),Rv=new T(54),Sv=new T(1323,-1),Tv=new T(1265,-1),Uv=new T(1264,-1),Vv=new S(1291),Wv=new S(1267,!0),Xv=new S(1266),Yv=new S(313),Zv=new T(66,-1),$v=new T(65,-1),aw=new S(1256),bw=new S(369),cw=new S(1241,!0),dw=new S(368),ew=new S(1300,!0),fw=new jv(1273,["en","de","fr","es","ja"]),gw=new jv(1261,["44786015","44786016"]),hw=new S(1361),iw=new S(1372,!0),jw=new S(290),kw=new S(1382),lw=new S(1222),mw=new S(1354),nw=new S(1341),ow=new S(1350),pw=new S(1356), 
qw=new S(626390500),rw=new S(566279275),sw=new S(622128248),tw=new S(566279276),uw=new kv(635821288,["29_18","30_19"]),vw=new iv(716722045,"600px"),ww=new S(741481545),xw=new kv(631402549),yw=new S(636570127,!0),zw=new T(626062006,670),Aw=new T(666400580,22),Bw=new jv(712458671," ar bn en es fr hi id ja ko mr pt ru sr te th tr uk vi zh".split(" ")),Cw=new T(751018117),Dw=new S(748685380),Ew=new S(748689018),Fw=new kv(683929765),Gw=new S(742688665,!0),Hw=new S(683614711),Iw=new S(747408261),Jw=new S(506914611), 
Kw=new S(655991266,!0),Lw=new S(750973575),Mw=new kv(630330362),Nw=new T(717888910,.7),Ow=new T(643258048,.15),Pw=new T(643258049,.16),Qw=new T(618163195,15E3),Rw=new T(624950166,15E3),Sw=new T(623405755,300),Tw=new T(508040914,500),Uw=new T(547455356,49),Vw=new T(717888911,.7),Ww=new T(717888912,.7),Xw=new T(727864505,3),Yw=new T(652486359,3),Zw=new T(748662193,8),$w=new T(688905693,2),ax=new T(650548030,2),bx=new T(650548032,300),cx=new T(650548031,1),dx=new T(655966487,300),ex=new T(655966486, 
250),fx=new T(687270738,500),gx=new T(469675170,6E4),Ir=new S(737425145),hx=new S(675298507),ix=new S(644381219),jx=new S(644381220),kx=new S(676460084),lx=new S(710737579),mx=new S(45650663),nx=new T(684147713,-1),ox=new S(570863962,!0),px=new iv(570879859,"control_1\\.\\d"),qx=new T(570863961,50),rx=new S(570879858,!0),sx=new T(63,30),tx=new S(1134,!0),ux=new S(751557128),vx=new S(562874197),wx=new S(562874196),xx=new S(555237685,!0),yx=new S(750586557),zx=new T(550718588,250),Ax=new T(624290870, 
50),Bx=new S(506738118),Cx=new S(77),Dx=new S(78),Ex=new S(83),Fx=new S(80),Gx=new S(76),Hx=new S(84),Ix=new S(1973),Jx=new S(188),Kx=new S(485990406);var Lx=class{constructor(){const a={};this.j=(b,c)=>a[b]!=null?a[b]:c;this.l=(b,c)=>a[b]!=null?a[b]:c;this.C=(b,c)=>a[b]!=null?a[b]:c;this.g=(b,c)=>a[b]!=null?a[b]:c;this.A=(b,c)=>a[b]!=null?c.concat(a[b]):c;this.i=()=>{}}};function O(a){return N(Lx).j(a.g,a.defaultValue)}function W(a){return N(Lx).l(a.g,a.defaultValue)}function Mx(a){return N(Lx).C(a.g,a.defaultValue)}function Nx(a){return N(Lx).A(a.g,a.defaultValue)};var Px=(a,b,c,d=0)=>{var e=Ox(b,c,d);if(e.init){for(c=b=e.init;c=e.Xd(c);)b=c;e={anchor:b,position:e.ze}}else e={anchor:b,position:c};a["google-ama-order-assurance"]=d;gv(a,e.anchor,e.position)},Qx=(a,b,c,d=0)=>{O(Yv)?Px(a,b,c,d):gv(a,b,c)}; 
function Ox(a,b,c){const d=f=>{f=Rx(f);return f==null?!1:c<f},e=f=>{f=Rx(f);return f==null?!1:c>f};switch(b){case 0:return{init:Sx(a.previousSibling,d),Xd:f=>Sx(f.previousSibling,d),ze:0};case 2:return{init:Sx(a.lastChild,d),Xd:f=>Sx(f.previousSibling,d),ze:0};case 3:return{init:Sx(a.nextSibling,e),Xd:f=>Sx(f.nextSibling,e),ze:3};case 1:return{init:Sx(a.firstChild,e),Xd:f=>Sx(f.nextSibling,e),ze:3}}throw Error("Un-handled RelativePosition: "+b);} 
function Rx(a){return a.hasOwnProperty("google-ama-order-assurance")?a["google-ama-order-assurance"]:null}function Sx(a,b){return a&&b(a)?a:null};var Tx={rectangle:1,horizontal:2,vertical:4};function Ux(a,b){do{const c=$d(a,b);if(c&&c.position=="fixed")return!1}while(a=a.parentElement);return!0};function Vx(a,b){var c=["width","height"];for(let e=0;e<c.length;e++){const f="google_ad_"+c[e];if(!b.hasOwnProperty(f)){var d=oe(a[c[e]]);d=d===null?null:Math.round(d);d!=null&&(b[f]=d)}}}function Wx(a,b){return!((he.test(b.google_ad_width)||ge.test(a.style.width))&&(he.test(b.google_ad_height)||ge.test(a.style.height)))}function Xx(a,b){return(a=Yx(a,b))?a.y:0} 
function Yx(a,b){try{const c=b.document.documentElement.getBoundingClientRect(),d=a.getBoundingClientRect();return{x:d.left-c.left,y:d.top-c.top}}catch(c){return null}}function Zx(a,b){const c=a.google_reactive_ad_format===40,d=a.google_reactive_ad_format===16;return!!a.google_ad_resizable&&(!a.google_reactive_ad_format||c)&&!d&&!!b.navigator&&/iPhone|iPod|iPad|Android|BlackBerry/.test(b.navigator.userAgent)&&b===b.top} 
function $x(a,b,c,d,e){if(a!==a.top)return Xd(a)?3:16;if(!(Br(a)<488))return 4;if(!(a.innerHeight>=a.innerWidth))return 5;const f=Br(a);if(!f||(f-c)/f>d)a=6;else{if(c=e.google_full_width_responsive!=="true")a:{c=b.parentElement;for(b=Br(a);c;c=c.parentElement)if((d=$d(c,a))&&(e=oe(d.width))&&!(e>=b)&&d.overflow!=="visible"){c=!0;break a}c=!1}a=c?7:!0}return a} 
function ay(a,b,c,d){const e=$x(b,c,a,W(zv),d);e!==!0?a=e:d.google_full_width_responsive==="true"||Ux(c,b)?(b=Br(b),a=b-a,a=b&&a>=0?!0:b?a<-10?11:a<0?14:12:10):a=9;return a}function by(a,b,c){a=a.style;b==="rtl"?a.marginRight=c:a.marginLeft=c} 
function cy(a,b){if(b.nodeType===3)return/\S/.test(b.data);if(b.nodeType===1){if(/^(script|style)$/i.test(b.nodeName))return!1;let c;try{c=$d(b,a)}catch(d){}return!c||c.display!=="none"&&!(c.position==="absolute"&&(c.visibility==="hidden"||c.visibility==="collapse"))}return!1}function dy(a,b,c){a=Yx(b,a);return c==="rtl"?-a.x:a.x}function ey(a,b){b=b.parentElement;return b?(a=$d(b,a))?a.direction:"":""} 
function fy(a,b,c){if(dy(a,b,c)!==0){by(b,c,"0px");var d=dy(a,b,c);by(b,c,`${-1*d}px`);a=dy(a,b,c);a!==0&&a!==d&&by(b,c,`${d/(a-d)*d}px`)}}function gy(a,b){const c=ey(a,b);if(c){var d=b.style;d.border=d.borderStyle=d.outline=d.outlineStyle=d.transition="none";d.borderSpacing=d.padding="0";by(b,c,"0px");d.width=`${Br(a)}px`;fy(a,b,c);d.zIndex="30"}};function hy(a,b,c){let d;return a.style&&!!a.style[c]&&oe(a.style[c])||(d=$d(a,b))&&!!d[c]&&oe(d[c])||null}function iy(a,b){const c=Gm(a)===0;return b&&c?Math.max(250,2*Cr(a)/3):250}function jy(a,b){let c;return a.style&&a.style.zIndex||(c=$d(a,b))&&c.zIndex||null}function ky(a){return b=>b.g<=a}function ly(a,b,c,d){const e=a&&my(c,b),f=iy(b,d);return g=>!(e&&g.height()>=f)}function ny(a){return b=>b.height()<=a}function my(a,b){a=Xx(a,b);b=Cr(b);return a<b-100} 
function oy(a,b){var c=hy(b,a,"height");if(c)return c;var d=b.style.height;b.style.height="inherit";c=hy(b,a,"height");b.style.height=d;if(c)return c;c=Infinity;do(d=b.style&&oe(b.style.height))&&(c=Math.min(c,d)),(d=hy(b,a,"maxHeight"))&&(c=Math.min(c,d));while(b.parentElement&&(b=b.parentElement)&&b.tagName!=="HTML");return c};const py=RegExp("(^| )adsbygoogle($| )");function qy(a,b){for(let c=0;c<b.length;c++){const d=b[c],e=vd(d.property);a[e]=d.value}}function ry(a,b,c,d,e,f){a=sy(a,e);a.ua.setAttribute("data-ad-format",d?d:"auto");ty(a,b,c,f);return a}function uy(a,b,c=null){a=sy(a,{});ty(a,b,null,c);return a}function ty(a,b,c,d){var e=[];if(d=d&&d.Yg)a.Fb.className=d.join(" ");a=a.ua;a.className="adsbygoogle";a.setAttribute("data-ad-client",b);c&&a.setAttribute("data-ad-slot",c);e.length&&a.setAttribute("data-ad-channel",e.join("+"))} 
function sy(a,b){const c=fv(a,b.clearBoth||!1);var d=c.style;d.textAlign="center";b.ye&&qy(d,b.ye);a=rm(new fm(a),"INS");d=a.style;d.display="block";d.margin="auto";d.backgroundColor="transparent";b.Ig&&(d.marginTop=b.Ig);b.jf&&(d.marginBottom=b.jf);b.Hc&&qy(d,b.Hc);c.appendChild(a);return{Fb:c,ua:a}} 
function vy(a,b,c){b.dataset.adsbygoogleStatus="reserved";b.className+=" adsbygoogle-noablate";const d={element:b};c=c&&c.bd();if(b.hasAttribute("data-pub-vars")){try{c=JSON.parse(b.getAttribute("data-pub-vars"))}catch(e){return}b.removeAttribute("data-pub-vars")}c&&(d.params=c);(a.adsbygoogle=a.adsbygoogle||[]).push(d)} 
function wy(a){const b=ev(a.document);Na(b,function(c){const d=xy(a,c);var e;if(e=d){e=Xx(c,a);const f=Cr(a);e=!(e<f)}e&&(c.setAttribute("data-pub-vars",JSON.stringify(d)),c.removeAttribute("height"),c.style.removeProperty("height"),c.removeAttribute("width"),c.style.removeProperty("width"),vy(a,c))})}function xy(a,b){b=b.getAttribute("google_element_uid");a=a.google_sv_map;if(!b||!a||!a[b])return null;a=a[b];b={};for(let c in mb)a[mb[c]]&&(b[mb[c]]=a[mb[c]]);return b};var zy=(a,b,c)=>{if(!b||!c)return!1;var d=b.parentElement;const e=c.parentElement;if(!d||!e||d!=e)return!1;d=0;for(b=b.nextSibling;d<10&&b;){if(b==c)return!0;if(yy(a,b))break;b=b.nextSibling;d++}return!1}; 
const yy=(a,b)=>{if(b.nodeType==3)return b.nodeType==3?(b=b.data,a=b.indexOf("&")!=-1?nd(b,a.document):b,a=/\S/.test(a)):a=!1,a;if(b.nodeType==1){var c=a.getComputedStyle(b);if(c.opacity=="0"||c.display=="none"||c.visibility=="hidden")return!1;if((c=b.tagName)&&Yr.contains(c.toUpperCase()))return!0;b=b.childNodes;for(c=0;c<b.length;c++)if(yy(a,b[c]))return!0}return!1}; 
var Ay=a=>{if(a>=460)return a=Math.min(a,1200),Math.ceil(a<800?a/4:200);a=Math.min(a,600);return a<=420?Math.ceil(a/1.2):Math.ceil(a/1.91)+130};var By=class{constructor(){this.g={clearBoth:!0}}i(a,b,c,d){return ry(d.document,a,null,null,this.g,b)}j(a){return Ay(Math.min(a.screen.width||0,a.screen.height||0))}};function Cy(a){const b=[];Qr(a.getElementsByTagName("p"),function(c){Dy(c)>=100&&b.push(c)});return b}function Dy(a){if(a.nodeType==3)return a.length;if(a.nodeType!=1||a.tagName=="SCRIPT")return 0;let b=0;Qr(a.childNodes,function(c){b+=Dy(c)});return b}function Ey(a){return a.length==0||isNaN(a[0])?a:"\\"+(30+parseInt(a[0],10))+" "+a.substring(1)} 
function Fy(a,b){if(a.g==null)return b;switch(a.g){case 1:return b.slice(1);case 2:return b.slice(0,b.length-1);case 3:return b.slice(1,b.length-1);case 0:return b;default:throw Error("Unknown ignore mode: "+a.g);}} 
function Gy(a,b){var c=[];try{c=b.querySelectorAll(a.A)}catch(d){}if(!c.length)return[];b=eb(c);b=Fy(a,b);typeof a.i==="number"&&(c=a.i,c<0&&(c+=b.length),b=c>=0&&c<b.length?[b[c]]:[]);if(typeof a.j==="number"){c=[];for(let d=0;d<b.length;d++){const e=Cy(b[d]);let f=a.j;f<0&&(f+=e.length);f>=0&&f<e.length&&c.push(e[f])}b=c}return b} 
var Hy=class{constructor(a,b,c,d){this.A=a;this.i=b;this.j=c;this.g=d}toString(){return JSON.stringify({nativeQuery:this.A,occurrenceIndex:this.i,paragraphIndex:this.j,ignoreMode:this.g})}};var Iy=class{constructor(){this.g=Rd`https://pagead2.googlesyndication.com/pagead/js/err_rep.js`}ma(a,b,c=.01,d="jserror"){if(Math.random()>c)return!1;b.error&&b.meta&&b.id||(b=new Lm(b,{context:a,id:d}));r.google_js_errors=r.google_js_errors||[];r.google_js_errors.push(b);r.error_rep_loaded||(Yd(r.document,this.g),r.error_rep_loaded=!0);return!1}sb(a,b){try{return b()}catch(c){if(!this.ma(a,c,.01,"jserror"))throw c;}}tb(a,b,c){return(...d)=>this.sb(a,()=>b.apply(c,d))}Sa(a,b){b.catch(c=>{c=c?c:"unknown rejection"; 
this.ma(a,c instanceof Error?c:Error(c),void 0)})}};function Jy(a,b){b=b.google_js_reporting_queue=b.google_js_reporting_queue||[];b.length<2048&&b.push(a)} 
function Ky(a,b,c,d){const e=d||window,f=typeof queueMicrotask!=="undefined";return function(...g){f&&queueMicrotask(()=>{e.google_rum_task_id_counter=e.google_rum_task_id_counter||1;e.google_rum_task_id_counter+=1});const h=Vm();let k,l=3;try{k=b.apply(this,g)}catch(m){l=13;if(!c)throw m;c(a,m)}finally{e.google_measure_js_timing&&h&&Jy({label:a.toString(),value:h,duration:(Vm()||0)-h,type:l,...(f&&{taskId:e.google_rum_task_id_counter=e.google_rum_task_id_counter||1})},e)}return k}} 
function Ly(a,b){return Ky(754,a,(c,d)=>{(new Iy).ma(c,d)},b)};function My(a,b,c){return Ky(a,b,void 0,c).apply()}function Ny(a,b){return Ly(a,b).apply()}function Oy(a){if(!a)return null;var b=Ji(a,7);if(Ji(a,1)||a.getId()||yi(a,4).length>0){var c=a.getId(),d=Ji(a,1),e=yi(a,4);b=Ci(a,2);var f=Ci(a,5);a=Py(Ki(a,6));let g="";d&&(g+=d);c&&(g+="#"+Ey(c));if(e)for(c=0;c<e.length;c++)g+="."+Ey(e[c]);b=(e=g)?new Hy(e,b,f,a):null}else b=b?new Hy(b,Ci(a,2),Ci(a,5),Py(Ki(a,6))):null;return b}const Qy={1:1,2:2,3:3,0:0};function Py(a){return a==null?a:Qy[a]} 
function Ry(a){const b=[];for(let c=0;c<a.length;c++){const d=Ji(a[c],1),e=a[c].getValue();d&&e!=null&&b.push({property:d,value:e})}return b}function Sy(a,b){const c={};a&&(c.Ig=Ji(a,1),c.jf=Ji(a,2),c.clearBoth=!!Bi(a,3));b&&(c.ye=Ry(mi(b,su,3,Th()).map(d=>nh(d))),c.Hc=Ry(mi(b,su,4,Th()).map(d=>nh(d))));return c}const Ty={1:0,2:1,3:2,4:3},Uy={0:1,1:2,2:3,3:4};function Vy(a){return a};var Wy=class{constructor(a){this.g=a}i(a,b,c,d){return ry(d.document,a,null,null,this.g,b)}j(){return null}};var Xy=class{constructor(a){this.i=a}g(a){a=Math.floor(a.i);const b=Ay(a);return new Nt(["ap_container"],{google_reactive_ad_format:27,google_responsive_auto_format:16,google_max_num_ads:1,google_ad_type:this.i,google_ad_format:a+"x"+b,google_ad_width:a,google_ad_height:b})}};var Yy=class{constructor(a,b){this.A=a;this.j=b}g(){return this.A}i(){return this.j}};var Zy=class{constructor(a){this.g=a}i(a,b,c,d){var e=mi(this.g,tu,9,Th()).length>0?mi(this.g,tu,9,Th())[0]:null,f=Sy(ki(this.g,uu,3),e);if(!e)return null;if(e=Ji(e,1)){d=d.document;var g=c.tagName;c=rm(new fm(d),g);c.style.clear=f.clearBoth?"both":"none";g=="A"&&(c.style.display="block");c.style.padding="0px";c.style.margin="0px";f.ye&&qy(c.style,f.ye);d=rm(new fm(d),"INS");f.Hc&&qy(d.style,f.Hc);c.appendChild(d);f={Fb:c,ua:d};f.ua.setAttribute("data-ad-type","text");f.ua.setAttribute("data-native-settings-key", 
e);ty(f,a,null,b);a=f}else a=null;return a}j(){var a=mi(this.g,tu,9,Th()).length>0?mi(this.g,tu,9,Th())[0]:null;if(!a)return null;a=mi(a,su,3,Th());for(let b=0;b<a.length;b++){const c=a[b];if(Ji(c,1)=="height"&&parseInt(c.getValue(),10)>0)return parseInt(c.getValue(),10)}return null}};var $y=class{constructor(a){this.g=a}i(a,b,c,d){if(!this.g)return null;const e=this.g.google_ad_format||null,f=this.g.google_ad_slot||null;if(c=c.style){var g=[];for(let h=0;h<c.length;h++){const k=c.item(h);k!=="width"&&k!=="height"&&g.push({property:k,value:c.getPropertyValue(k)})}c={Hc:g}}else c={};a=ry(d.document,a,f,e,c,b);a.ua.setAttribute("data-pub-vars",JSON.stringify(this.g));return a}j(){return this.g?parseInt(this.g.google_ad_height,10)||null:null}bd(){return this.g}};var az=class{constructor(a){this.i=a}g(){return new Nt([],{google_ad_type:this.i,google_reactive_ad_format:26,google_ad_format:"fluid"})}};var bz=class{constructor(a,b){this.A=a;this.j=b}i(){return this.j}g(a){a=Gy(this.A,a.document);return a.length>0?a[0]:null}};function cz(a,b,c){const d=[];for(let p=0;p<a.length;p++){a:{var e=a[p];var f=p,g=b,h=c,k=Vy(e.ia());if(!k){e=null;break a}var l=Oy(k);if(!l){e=null;break a}var m=e.i();m=Ty[m];var n=m===void 0?null:m;if(n===null){e=null;break a}m=(m=z(e,uu,3))?Bi(m,3):null;l=new bz(l,n);n=zi(e,10).slice(0);yg(y(k,5))!=null&&n.push(1);k=Ci(e,12);const w=Oh(e,Lt,4)?Vy(z(e,Lt,4)):null;wg(y(e,8))==1?(h=h&&h.Yi||null,e=new dz(l,new Wy(Sy(Vy(z(e,uu,3)),null)),h,m,0,n,w,g,f,k,e)):e=wg(y(e,8))==2?new dz(l,new Zy(e),h&&h.ek|| 
new az("text"),m,1,n,w,g,f,k,e):null}e!==null&&d.push(e)}return d}function ez(a){return a.A}function fz(a){return a.Ka}function gz(a){return a.C instanceof $y?a.C.bd():null}function hz(a,b,c){Sr(a.M,b)||a.M.set(b,[]);a.M.get(b).push(c)}function iz(a){return a.C.j(a.g)}function jz(a,b=null,c=null){return new dz(a.D,b||a.C,c||a.V,a.G,a.uc,a.ed,a.Je,a.g,a.pa,a.H,a.j,a.l,a.X)} 
var dz=class{constructor(a,b,c,d,e,f,g,h,k,l=null,m=null,n=null,p=null){this.D=a;this.C=b;this.V=c;this.G=d;this.uc=e;this.ed=f;this.Je=g?g:new Lt;this.g=h;this.pa=k;this.H=l;this.j=m;(a=!m)||((a=!m.ia())||(m=m.ia(),a=yg(y(m,5))==null),a=!!a);this.Ka=!a;this.l=n;this.X=p;this.J=[];this.A=!1;this.M=new Wr}da(){return this.g}i(){return this.D.i()}};function kz(a,b,c,d,e,f){const g=Kt();return new dz(new Yy(c,e),new By,new Xy(a),!0,2,[],g,d,null,null,null,b,f)}function lz(a,b,c,d,e){const f=Kt();return new dz(new Yy(b,d),new Wy({clearBoth:!0}),null,!0,2,[],f,c,null,null,null,a,e)};var mz=class{constructor(a,b,c){this.articleStructure=a;this.element=b;this.B=c}da(){return this.B}l(a){return kz(a,this.articleStructure,this.element,this.B,3,null)}j(){return lz(this.articleStructure,this.element,this.B,3,null)}};const nz={TABLE:{Rc:new nt([1,2])},THEAD:{Rc:new nt([0,3,1,2])},TBODY:{Rc:new nt([0,3,1,2])},TR:{Rc:new nt([0,3,1,2])},TD:{Rc:new nt([0,3])}};function oz(a,b,c,d){const e=c.childNodes;c=c.querySelectorAll(b);b=[];for(const f of c)c=Ma(e,f),c<0||b.push(new pz(a,[f],c,f,3,nm(f).trim(),d));return b} 
function qz(a,b,c){let d=[];const e=[],f=b.childNodes,g=f.length;let h=0,k="";for(let n=0;n<g;n++){var l=f[n];if(l.nodeType==1||l.nodeType==3){if(l.nodeType!=1)var m=null;else l.tagName=="BR"?m=l:(m=c.getComputedStyle(l).getPropertyValue("display"),m=m=="inline"||m=="inline-block"?null:l);m?(d.length&&k&&e.push(new pz(a,d,n-1,m,0,k,c)),d=[],h=n+1,k=""):(d.push(l),l=nm(l).trim(),k+=l&&k?" "+l:l)}}d.length&&k&&e.push(new pz(a,d,h,b,2,k,c));return e}function rz(a,b){return a.g-b.g} 
var pz=class{constructor(a,b,c,d,e,f,g){this.A=a;this.Md=b.slice(0);this.g=c;this.Pe=d;this.Qe=e;this.C=f;this.i=g}da(){return this.i}l(a){return kz(a,this.A,this.Pe,this.i,this.Qe,this.g)}j(){return lz(this.A,this.Pe,this.i,this.Qe,this.g)}};function sz(a){return bb(a.C?qz(a.g,a.j,a.i):[],a.l?oz(a.g,a.l,a.j,a.i):[]).filter(b=>{var c=b.Pe.tagName;c?(c=nz[c.toUpperCase()],b=c!=null&&c.Rc.contains(b.Qe)):b=!1;return!b})}var tz=class{constructor(a,b,c){this.j=a;this.l=b.Jd;this.C=b.oh;this.g=b.articleStructure;this.i=c;this.A=b.Sg}};function uz(a,b){if(!b)return!1;const c=ua(b),d=a.g.get(c);if(d!=null)return d;if(b.nodeType==1&&(b.tagName=="UL"||b.tagName=="OL")&&a.i.getComputedStyle(b).getPropertyValue("list-style-type")!="none")return a.g.set(c,!0),!0;b=uz(a,b.parentNode);a.g.set(c,b);return b}function vz(a,b){return Xa(b.Md,c=>uz(a,c))}var wz=class{constructor(a){this.g=new Wr;this.i=a}};var xz=class{constructor(a,b){this.A=a;this.g=[];this.i=[];this.j=b}};var zz=(a,{Ch:b=!1,Ag:c=!1,Nh:d=c?2:3,zg:e=null}={})=>{a=sz(a);return yz(a,{Ch:b,Ag:c,Nh:d,zg:e})},yz=(a,{Ch:b=!1,Ag:c=!1,Nh:d=c?2:3,zg:e=null}={})=>{if(d<2)throw Error("minGroupSize should be at least 2, found "+d);var f=a.slice(0);f.sort(rz);a=[];b=new xz(b,e);for(const g of f){e={Ae:g,Zd:g.C.length<51?!1:b.j!=null?!vz(b.j,g):!0};if(b.A||e.Zd)b.g.length?(f=b.g[b.g.length-1].Ae,f=zy(f.da(),f.Md[f.Md.length-1],e.Ae.Md[0])):f=!0,f?(b.g.push(e),e.Zd&&b.i.push(e.Ae)):(b.g=[e],b.i=e.Zd?[e.Ae]:[]);if(b.i.length>= 
d){e=b;f=c?0:1;if(f<0||f>=e.i.length)e=null;else{for(f=e.i[f];e.g.length&&!e.g[0].Zd;)e.g.shift();e.g.shift();e.i.shift();e=f}e&&a.push(e)}}return a};var Bz=(a,b,c=!1)=>{a=Az(a,b);const d=new wz(b);return gt(a,e=>zz(e,{Ag:c,zg:d}))},Cz=(a,b)=>{a=Az(a,b);const c=new wz(b);return gt(a,d=>{if(d.A){var e=d.g;var f=d.i;d=d.j.querySelectorAll(d.A);var g=[];for(var h of d)g.push(new mz(e,h,f));e=g}else e=[];d=e.slice(0);if(d.length){e=[];f=d[0];for(g=1;g<d.length;g++){const m=d[g];h=f;b:{if(h.element.hasAttributes())for(l of h.element.attributes)if(l.name.toLowerCase()==="style"&&l.value.toLowerCase().includes("background-image")){var k=!0;break b}k= 
h.element.tagName;k=k==="IMG"||k==="SVG"}(k||h.element.textContent.length>1)&&!uz(c,f.element)&&zy(m.da(),f.element,m.element)&&e.push(f);f=m}var l=e}else l=[];return l})},Az=(a,b)=>{const c=new Wr;a.forEach(d=>{var e=Oy(ki(d,Ct,1));if(e){var f=e.toString();Sr(c,f)||c.set(f,{articleStructure:d,Ti:e,Jd:null,oh:!1,Sg:null});e=c.get(f);(f=(f=z(d,Ct,2))?Ji(f,7):null)?e.Jd=e.Jd?e.Jd+","+f:f:e.oh=!0;d=z(d,Ct,4);e.Sg=d?Ji(d,7):null}});return Vr(c).map(d=>{const e=Gy(d.Ti,b.document);return e.length?new tz(e[0], 
d,b):null}).filter(d=>d!=null)};var Dz=a=>a?.google_ad_slot?ot(new Dt(1,{Ji:a.google_ad_slot})):qt(Error("Missing dimension when creating placement id")),Fz=a=>{switch(a.uc){case 0:case 1:var b=a.j;b==null?a=null:(a=b.ia(),a==null?a=null:(b=b.i(),a=b==null?null:new Dt(0,{Tg:[a],di:b})));return a!=null?ot(a):qt(Error("Missing dimension when creating placement id"));case 2:return a=Ez(a),a!=null?ot(a):qt(Error("Missing dimension when creating placement id"));default:return qt(Error("Invalid type: "+a.uc))}}; 
const Ez=a=>{if(a==null||a.l==null)return null;const b=z(a.l,Ct,1),c=z(a.l,Ct,2);if(b==null||c==null)return null;const d=a.X;if(d==null)return null;a=a.i();return a==null?null:new Dt(0,{Tg:[b,c],dk:d,di:Uy[a]})};function Gz(a){const b=gz(a.ga);return(b?Dz(b):Fz(a.ga)).map(c=>Gt(c))}function Hz(a){a.g=a.g||Gz(a);return a.g}function Iz(a,b){if(a.ga.A)throw Error("AMA:AP:AP");Qx(b,a.ia(),a.ga.i());a=a.ga;a.A=!0;b!=null&&a.J.push(b)}const Jz=class{constructor(a,b,c){this.ga=a;this.i=b;this.na=c;this.g=null}ia(){return this.i}fill(a,b){var c=this.ga;(a=c.C.i(a,b,this.i,c.g))&&Iz(this,a.Fb);return a}};function Kz(a,b){return Ny(()=>{const c=[],d=[];try{var e=[];for(var f=0;f<a.length;f++){var g=a[f],h=g.D.g(g.g);h&&e.push({Yh:g,anchorElement:h})}for(g=0;g<e.length;g++){f=d;var k=f.push;{var l=e[g];const u=l.anchorElement,t=l.Yh;var m=t.G;const B=t.g.document.createElement("div");B.className="google-auto-placed";const I=B.style;I.textAlign="center";I.width="100%";I.height="0px";I.clear=m?"both":"none";h=B;try{Qx(h,u,t.i());var n=h}catch(U){throw hv(h),U;}}k.call(f,n)}const p=Lr(b),w=Mr(b);for(k= 
0;k<d.length;k++){const u=d[k].getBoundingClientRect(),t=e[k];c.push(new Jz(t.Yh,t.anchorElement,new Ys(u.left+w,u.top+p,u.right-u.left)))}}finally{for(e=0;e<d.length;e++)hv(d[e])}return c},b)};const Lz={1:"0.5vp",2:"300px"},Mz={1:700,2:1200},Nz={[1]:{ni:"3vp",Dg:"1vp",mi:"0.3vp"},[2]:{ni:"900px",Dg:"300px",mi:"90px"}}; 
function AB(a,b,c){var d=BB(a),e=Cr(a)||Mz[d],f=void 0;c&&(f=(c=(c=CB(mi(c,Ut,2,Th()),d))?z(c,St,7):void 0)?DB(c,e):void 0);c=f;f=BB(a);a=Cr(a)||Mz[f];const g=EB(Nz[f].Dg,a);a=g===null?FB(f,a):new GB(g,g,HB(g,8),8,.3,c);c=EB(Nz[d].ni,e);f=EB(Nz[d].Dg,e);d=EB(Nz[d].mi,e);e=a.j;c&&d&&f&&b!==void 0&&(e=b<=.5?f+(1-2*b)*(c-f):d+(2-2*b)*(f-d));return new GB(e,e,HB(e,a.i),a.i,a.A,a.g)}function IB(a,b){const c=Tg(y(a,4,void 0,Kh));a=Sh(a,5,Kh);return c==null||a==null?b:new GB(a,0,[],c,1)} 
function JB(a,b){const c=BB(a);a=Cr(a)||Mz[c];if(!b)return FB(c,a);if(b=CB(mi(b,Ut,2,Th()),c))if(b=KB(b,a))return b;return FB(c,a)}function LB(a){const b=BB(a);a=Cr(a)||Mz[b];return FB(b,a)}function MB(){return O(Nv)?new GB(0,null,[],8,.3):new GB(0,null,[],3,null)}function NB(a,b){let c={ld:a.j,Mb:a.C};for(let d of a.l)d.adCount<=b&&(c=d.xd);return c} 
function OB(a,b,c){var d=Bi(b,2);b=z(b,Ut,1);var e=BB(c);var f=Cr(c)||Mz[e];c=EB(b?.i(),f)??a.j;e=EB(b?.g(),f)??a.C;d=d?[]:PB(b?.A(),f)??a.l;const g=b?.l()??a.i,h=b?.C()??a.A;a=(b?.H()?DB(z(b,St,7),f):null)??a.g;return new GB(c,e,d,g,h,a)} 
function QB(a,b){var c=BB(b);const d=new Vt,e=new Ut;let f=!1;var g=W(Qv);g>=0&&(Ni(e,4,g),f=!0);g=null;c===1?(c=W(Uv),c>=0&&(g=c+"vp")):(c=W(Tv),c>=0&&(g=c+"px"));c=W(Sv);c>=0&&(g=c+"px");g!==null&&(Qi(e,2,g),f=!0);c=O(Wv)?"0px":null;c!==null&&(Qi(e,5,c),f=!0);if(O(Xv))Li(d,2,!0),f=!0;else if(c!==null||g!==null){const m=[];for(const n of a.l){var h=m,k=h.push;var l=new Tt;l=Ni(l,1,n.adCount);l=Qi(l,3,c??n.xd.Mb+"px");l=Qi(l,2,g??n.xd.ld+"px");k.call(h,l)}pi(e,3,m)}return f?(A(d,1,e),OB(a,d,b)):a} 
var GB=class{constructor(a,b,c,d,e,f){this.j=a;this.C=b;this.l=c.sort((g,h)=>g.adCount-h.adCount);this.i=d;this.A=e;this.g=f}};function CB(a,b){for(let c of a)if(wg(y(c,1))==b)return c;return null}function PB(a,b){if(a===void 0)return null;const c=[];for(let d of a){a=Ci(d,1);const e=EB(Ji(d,2),b),f=EB(Ji(d,3),b);if(typeof a!=="number"||e===null)return null;c.push({adCount:a,xd:{ld:e,Mb:f}})}return c} 
function KB(a,b){const c=EB(a.i(),b),d=EB(a.g(),b);if(c===null)return null;const e=Ci(a,4);if(e==null)return null;var f=a.A();f=PB(f,b);if(f===null)return null;const g=z(a,St,7);b=g?DB(g,b):void 0;return new GB(c,d,f,e,Sh(a,6,Kh),b)}function FB(a,b){a=EB(Lz[a],b);return O(Nv)?new GB(a===null?Infinity:a,null,[],8,.3):new GB(a===null?Infinity:a,null,[],3,null)}function EB(a,b){if(!a)return null;const c=parseFloat(a);return isNaN(c)?null:a.endsWith("px")?c:a.endsWith("vp")?c*b:null} 
function BB(a){a=Br(a)>=900;return wc()&&!a?1:2}function HB(a,b){if(b<4)return[];const c=Math.ceil(b/2);return[{adCount:c,xd:{ld:a*2,Mb:a*2}},{adCount:c+Math.ceil((b-c)/2),xd:{ld:a*3,Mb:a*3}}]}function DB(a,b){const c=EB(Ji(a,2),b)||0,d=Ci(a,3)||1;a=EB(Ji(a,1),b)||0;return{Oh:c,Lh:d,Kc:a}};function RB(a,b,c){return ur({top:a.g.top-(c+1),right:a.g.right+(c+1),bottom:a.g.bottom+(c+1),left:a.g.left-(c+1)},b.g)}function SB(a){if(!a.length)return null;const b=vr(a.map(c=>c.g));a=a.reduce((c,d)=>c+d.i,0);return new TB(b,a)}var TB=class{constructor(a,b){this.g=a;this.i=b}};function vq(){return"m202505060101"};var UB=Fk(bq);var uq=Fk(wq);function VB(a,b){return b(a)?a:void 0} 
function WB(a,b,c,d,e){c=c instanceof Lm?c.error:c;var f=new Aq;const g=new zq;try{var h=Ge(window);Pi(g,1,h)}catch(p){}try{var k=N(kr).g();ci(g,2,k,xg)}catch(p){}try{Ri(g,3,window.document.URL)}catch(p){}h=A(f,2,g);k=new yq;b=G(k,1,b);try{var l=pb(c?.name)?c.name:"Unknown error";Ri(b,2,l)}catch(p){}try{var m=pb(c?.message)?c.message:`Caught ${c}`;Ri(b,3,m)}catch(p){}try{var n=pb(c?.stack)?c.stack:Error().stack;n&&ci(b,4,n.split(/\n\s*/),$g)}catch(p){}l=oi(h,1,Bq,b);if(e){m=0;switch(e.errSrc){case "LCC":m= 
1;break;case "PVC":m=2}n=tq();b=VB(e.shv,pb);n=Ri(n,2,b);m=G(n,6,m);n=Ti(UB());b=VB(e.es,Bb());n=ci(n,1,b,xg);n=Gh(n);m=A(m,4,n);n=VB(e.client,pb);m=Qi(m,3,n);n=VB(e.slotname,pb);m=Ri(m,7,n);e=VB(e.tag_origin,pb);e=Ri(m,8,e);e=Gh(e)}else e=Vi(tq());e=oi(l,6,Cq,e);d=Pi(e,5,d??1);a.ii(d)};var YB=class{constructor(){this.g=XB}};function XB(){return{Ck:Eb()+(Eb()&2**21-1)*2**32,rj:Number.MAX_SAFE_INTEGER}};var aC=class{constructor(a=!1){var b=ZB;this.I=$B;this.i=a;this.A=b;this.g=null;this.eb=this.ma}j(a){this.g=a}l(){}sb(a,b,c){let d;try{d=b()}catch(e){b=this.i;try{b=this.eb(a,Mm(e),void 0,c)}catch(f){this.ma(217,f)}if(b)window.console?.error?.(e);else throw e;}return d}tb(a,b,c,d){return(...e)=>this.sb(a,()=>b.apply(c,e),d)}Sa(a,b,c){b.catch(d=>{d=d?d:"unknown rejection";this.ma(a,d instanceof Error?d:Error(d),void 0,c)})}ma(a,b,c,d){try{const g=c===void 0?1/this.A:c===0?0:1/c;var e=(new YB).g(); 
if(g>0&&e.Ck*g<=e.rj){var f=this.I;c={};if(this.g)try{this.g(c)}catch(h){}if(d)try{d(c)}catch(h){}WB(f,a,b,g,c)}}catch(g){}return this.i}};var bC=class extends Error{constructor(a=""){super();this.name="TagError";this.message=a?"adsbygoogle.push() error: "+a:"";Error.captureStackTrace?Error.captureStackTrace(this,bC):this.stack=Error().stack||""}};let $B,cC,dC,eC,ZB;const fC=new cn(r);(function(a,b,c=!0){({Rk:ZB,Rj:dC}=gC());cC=a||new nr;mr(cC,dC);$B=b||new hr(vq(),1E3);eC=new aC(c);r.document.readyState==="complete"?r.google_measure_js_timing||an(fC):fC.g&&Lk(r,"load",()=>{r.google_measure_js_timing||an(fC)})})();function hC(a,b,c){return eC.sb(a,b,c)}function iC(a,b){return eC.tb(a,b)}function jC(a,b,c){eC.Sa(a,b,c)}function kC(a,b,c=.01){const d=N(kr).g();!b.eid&&d.length&&(b.eid=d.toString());ln(cC,a,b,!0,c)} 
function lC(a,b,c=ZB,d){return eC.ma(a,b,c,d,void 0)}function gC(){let a,b;typeof r.google_srt==="number"?(b=r.google_srt,a=r.google_srt===0?1:.01):(b=Math.random(),a=.01);return{Rk:a,Rj:b}};function mC(a=null){({googletag:a}=a??window);return a?.apiReady?a:void 0};function nC(a,b){var c=oC(b,".google-auto-placed");const d=pC(b),e=qC(b),f=rC(b),g=sC(b),h=tC(b),k=oC(b,"div.googlepublisherpluginad"),l=oC(b,"html > ins.adsbygoogle");let m=[].concat(...oC(b,"iframe[id^=aswift_],iframe[id^=google_ads_frame]"),...oC(b,"body ins.adsbygoogle"));b=[];for(const [n,p]of[[a.Yd,c],[a.qc,d],[a.bk,e],[a.Sf,f],[a.Tf,g],[a.Zj,h],[a.ak,k],[a.ck,l]])n===!1?b=b.concat(p):m=m.concat(p);a=uC(m);c=uC(b);a=a.slice(0);for(const n of c)for(c=0;c<a.length;c++)(n.contains(a[c])||a[c].contains(n))&& 
a.splice(c,1);return a}function vC(a){return!!a.className&&a.className.indexOf("google-auto-placed")!=-1}function wC(a){const b=mC(a);return b?Qa(Va(b.pubads().getSlots(),c=>a.document.getElementById(c.getSlotElementId())),c=>c!=null):null}function oC(a,b){return eb(a.document.querySelectorAll(b))}function pC(a){return oC(a,"ins.adsbygoogle[data-anchor-status]")}function qC(a){return oC(a,"ins.adsbygoogle[data-ad-format=autorelaxed]")} 
function rC(a){return(wC(a)||oC(a,"div[id^=div-gpt-ad]")).concat(oC(a,"iframe[id^=google_ads_iframe]"))} 
function sC(a){return oC(a,"div.trc_related_container,div.OUTBRAIN,div[id^=rcjsload],div[id^=ligatusframe],div[id^=crt-],iframe[id^=cto_iframe],div[id^=yandex_], div[id^=Ya_sync],iframe[src*=adnxs],div.advertisement--appnexus,div[id^=apn-ad],div[id^=amzn-native-ad],iframe[src*=amazon-adsystem],iframe[id^=ox_],iframe[src*=openx],img[src*=openx],div[class*=adtech],div[id^=adtech],iframe[src*=adtech],div[data-content-ad-placement=true],div.wpcnt div[id^=atatags-]")} 
function tC(a){return oC(a,"ins.adsbygoogle-ablated-ad-slot")}function uC(a){const b=[];for(const c of a){a=!0;for(let d=0;d<b.length;d++){const e=b[d];if(e.contains(c)){a=!1;break}if(c.contains(e)){a=!1;b[d]=c;break}}a&&b.push(c)}return b};var xC=iC(453,nC),yC=iC(454,function(a,b){const c=oC(b,".google-auto-placed"),d=pC(b),e=qC(b),f=rC(b),g=sC(b),h=tC(b),k=oC(b,"div.googlepublisherpluginad");b=oC(b,"html > ins.adsbygoogle");return uC([...(a.Yd===!0?c:[]),...(a.qc===!0?d:[]),...(a.bk===!0?e:[]),...(a.Sf===!0?f:[]),...(a.Tf===!0?g:[]),...(a.Zj===!0?h:[]),...(a.ak===!0?k:[]),...(a.ck===!0?b:[])])});function zC(a,b,c){const d=AC(a);b=BC(d,b,c);return new CC(a,d,b)}function DC(a){return(a.bottom-a.top)*(a.right-a.left)>1}function EC(a){return a.g.map(b=>b.box)}function FC(a){return a.g.reduce((b,c)=>b+c.box.bottom-c.box.top,0)}var CC=class{constructor(a,b,c){this.j=a;this.g=b.slice(0);this.A=c.slice(0);this.i=null}}; 
function AC(a){const b=xC({qc:!1},a),c=Mr(a),d=Lr(a);return b.map(e=>{const f=e.getBoundingClientRect();return(e=vC(e))||DC(f)?{box:{top:f.top+d,right:f.right+c,bottom:f.bottom+d,left:f.left+c},ep:e?1:0}:null}).filter(rc(e=>e===null))}function BC(a,b,c){return b!=void 0&&a.length<=(c!=void 0?c:8)?GC(a,b):Va(a,d=>new TB(d.box,1))} 
function GC(a,b){a=Va(a,d=>new TB(d.box,1));const c=[];for(;a.length>0;){let d=a.pop(),e=!0;for(;e;){e=!1;for(let f=0;f<a.length;f++)if(RB(d,a[f],b)){d=SB([d,a[f]]);Array.prototype.splice.call(a,f,1);e=!0;break}}c.push(d)}return c};function HC(a,b,c){const d=Xs(c,b);return!Xa(a,e=>ur(e,d))}function IC(a,b,c,d,e){e=e.na;const f=Xs(e,b),g=Xs(e,c),h=Xs(e,d);return!Xa(a,k=>ur(k,g)||ur(k,f)&&!ur(k,h))}function JC(a,b,c,d){const e=EC(a);if(HC(e,b,d.na))return!0;if(!IC(e,b,c.Oh,c.Kc,d))return!1;const f=new TB(Xs(d.na,0),1);a=Qa(a.A,g=>RB(g,f,c.Kc));b=Wa(a,(g,h)=>g+h.i);return a.length===0||b>c.Lh?!1:!0};var KC=(a,b)=>{const c=[];let d=a;for(a=()=>{c.push({anchor:d.anchor,position:d.position});return d.anchor==b.anchor&&d.position==b.position};d;){switch(d.position){case 1:if(a())return c;d.position=2;case 2:if(a())return c;if(d.anchor.firstChild){d={anchor:d.anchor.firstChild,position:1};continue}else d.position=3;case 3:if(a())return c;d.position=4;case 4:if(a())return c}for(;d&&!d.anchor.nextSibling&&d.anchor.parentNode!=d.anchor.ownerDocument.body;){d={anchor:d.anchor.parentNode,position:3};if(a())return c; 
d.position=4;if(a())return c}d&&d.anchor.nextSibling?d={anchor:d.anchor.nextSibling,position:1}:d=null}return c};function LC(a,b){const c=new yt,d=new Xr;b.forEach(e=>{if(Ai(e,cu,1,fu)){e=Ai(e,cu,1,fu);if(z(e,bu,1)&&z(e,bu,1).ia()&&z(e,bu,2)&&z(e,bu,2).ia()){const g=MC(a,z(e,bu,1).ia()),h=MC(a,z(e,bu,2).ia());if(g&&h)for(var f of KC({anchor:g,position:z(e,bu,1).i()},{anchor:h,position:z(e,bu,2).i()}))c.set(ua(f.anchor),f.position)}z(e,bu,3)&&z(e,bu,3).ia()&&(f=MC(a,z(e,bu,3).ia()))&&c.set(ua(f),z(e,bu,3).i())}else Ai(e,du,2,fu)?NC(a,Ai(e,du,2,fu),c):Ai(e,au,3,fu)&&OC(a,Ai(e,au,3,fu),d)});return new PC(c,d)} 
var PC=class{constructor(a,b){this.i=a;this.g=b}};const NC=(a,b,c)=>{z(b,bu,2)?(b=z(b,bu,2),(a=MC(a,b.ia()))&&c.set(ua(a),b.i())):z(b,Ct,1)&&(a=QC(a,z(b,Ct,1)))&&a.forEach(d=>{d=ua(d);c.set(d,1);c.set(d,4);c.set(d,2);c.set(d,3)})},OC=(a,b,c)=>{z(b,Ct,1)&&(a=QC(a,z(b,Ct,1)))&&a.forEach(d=>{c.add(ua(d))})},MC=(a,b)=>(a=QC(a,b))&&a.length>0?a[0]:null,QC=(a,b)=>(b=Oy(b))?Gy(b,a):null;var RC=class{constructor(){var a=Math.random;this.g=Math.floor(a()*2**52);this.i=0}};function SC(a,b,c){switch(c){case 2:case 3:break;case 1:case 4:b=b.parentElement;break;default:throw Error("Unknown RelativePosition: "+c);}for(c=[];b;){if(TC(b))return!0;if(a.g.has(b))break;c.push(b);b=b.parentElement}c.forEach(d=>a.g.add(d));return!1}function UC(a){a=VC(a);return a.has("all")||a.has("after")}function WC(a){a=VC(a);return a.has("all")||a.has("before")}function VC(a){return(a=a&&a.getAttribute("data-no-auto-ads"))?new Set(a.split("|")):new Set} 
function TC(a){const b=VC(a);return a&&(a.tagName==="AUTO-ADS-EXCLUSION-AREA"||b.has("inside")||b.has("all"))}var XC=class{constructor(){this.g=new Set;this.i=new RC}};function YC(a){return function(b){return Kz(b,a)}}function ZC(a){const b=Cr(a);return b?Ea($C,b+Lr(a)):oc}function aD(a,b,c){if(a<0)throw Error("ama::ead:nd");if(a===Infinity)return oc;const d=EC(c||zC(b));return e=>HC(d,a,e.na)}function bD(a,b,c,d){if(a<0||b.Oh<0||b.Lh<0||b.Kc<0)throw Error("ama::ead:nd");return a===Infinity?oc:e=>JC(d||zC(c,b.Kc),a,b,e)}function cD(a){if(!a.length)return oc;const b=new nt(a);return c=>b.contains(c.uc)} 
function dD(a){return function(b){for(let c of b.ed)if(a.indexOf(c)>-1)return!1;return!0}}function eD(a){return a.length?function(b){const c=b.ed;return a.some(d=>c.indexOf(d)>-1)}:pc}function fD(a,b){if(a<=0)return pc;const c=Gr(b).scrollHeight-a;return function(d){return d.na.g<=c}}function gD(a){const b={};a&&a.forEach(c=>{b[c]=!0});return function(c){return!b[Ki(c.Je,2)||0]}}function hD(a){return a.length?b=>a.includes(Ki(b.Je,1)||0):pc} 
function iD(a,b){const c=LC(a,b);return function(d){var e=d.ia();d=d.ga.i();d=Uy[d];var f=c.i,g=ua(e);f=f.g.get(g);if(!(f=f?f.contains(d):!1))a:{if(c.g.contains(ua(e)))switch(d){case 2:case 3:f=!0;break a;default:f=!1;break a}for(e=e.parentElement;e;){if(c.g.contains(ua(e))){f=!0;break a}e=e.parentElement}f=!1}return!f}} 
function jD(){const a=new XC;return function(b){var c=b.ia();b=b.ga.i();var d=Uy[b];a:switch(d){case 1:b=UC(c.previousElementSibling)||WC(c);break a;case 4:b=UC(c)||WC(c.nextElementSibling);break a;case 2:b=WC(c.firstElementChild);break a;case 3:b=UC(c.lastElementChild);break a;default:throw Error("Unknown RelativePosition: "+d);}c=SC(a,c,d);d=a.i;kC("ama_exclusion_zone",{typ:b?c?"siuex":"siex":c?"suex":"noex",cor:d.g,num:d.i++,dvc:ve()},.1);return!(b||c)}} 
const $C=(a,b)=>b.na.g>=a,kD=(a,b,c)=>{c=c.na.i;return a<=c&&c<=b};function lD(a,b,c,d,e){var f=mD(nD(a,b),a);if(f.length===0){var g=!!z(b,ru,6)?.g()?.length;f=z(b,nu,28)?.i()?.i()&&g?mD(oD(a,b),a):f}if(f.length===0)return Xu(d,"pfno"),[];b=f;a=e.Rd?pD(a,b,c):{Cb:b,Td:null};const {Cb:h,Td:k}=a;f=h;return f.length===0&&k?(Xu(d,k),[]):[f[e.zl?0:e.xl?Math.floor(f.length/4):Math.floor(f.length/2)]]} 
function pD(a,b,c){c=c?mi(c,eu,5,Th()):[];const d=iD(a.document,c),e=jD();b=b.filter(f=>d(f));if(b.length===0)return{Cb:[],Td:"pfaz"};b=b.filter(f=>e(f));return b.length===0?{Cb:[],Td:"pfet"}:{Cb:b,Td:null}}function qD(a,b){return a.na.g-b.na.g}function nD(a,b){const c=z(b,ru,6);if(!c)return[];b=z(b,nu,28)?.i();return(b?.g()?Cz(c.g(),a):Bz(c.g(),a,!!b?.A())).map(d=>d.j())}function oD(a,b){b=mi(b,vu,1,Th())||[];return cz(b,a,{}).filter(c=>!c.ed.includes(6))} 
function mD(a,b){a=Kz(a,b);const c=ZC(b);a=a.filter(d=>c(d));return a.sort(qD)};var rD={},sD={},tD={},uD={},vD={};function wD(){throw Error("Do not instantiate directly");}wD.prototype.ah=null;wD.prototype.Yc=function(){return this.content};wD.prototype.toString=function(){return this.content};function xD(a){if(a.bh!==rD)throw Error("Sanitized content was not of kind HTML.");return cd(a.toString())}function yD(){wD.call(this)}Ja(yD,wD);yD.prototype.bh=rD;function zD(a){if(a!=null)switch(a.ah){case 1:return 1;case -1:return-1;case 0:return 0}return null}function AD(a){return BD(a,rD)?a:a instanceof bd?CD(dd(a).toString()):CD(String(String(a)).replace(DD,ED),zD(a))}var CD=function(a){function b(c){this.content=c}b.prototype=a.prototype;return function(c,d){c=new b(String(c));d!==void 0&&(c.ah=d);return c}}(yD);function FD(a){return GD(String(a),()=>"").replace(HD,"&lt;")} 
const ID=RegExp.prototype.hasOwnProperty("sticky"),JD=new RegExp((ID?"":"^")+"(?:!|/?([a-zA-Z][a-zA-Z0-9:-]*))",ID?"gy":"g"); 
function GD(a,b){const c=[],d=a.length;let e=0,f=[],g,h,k=0;for(;k<d;){switch(e){case 0:var l=a.indexOf("<",k);if(l<0){if(c.length===0)return a;c.push(a.substring(k));k=d}else c.push(a.substring(k,l)),h=l,k=l+1,ID?(JD.lastIndex=k,l=JD.exec(a)):(JD.lastIndex=0,l=JD.exec(a.substring(k))),l?(f=["<",l[0]],g=l[1],e=1,k+=l[0].length):c.push("<");break;case 1:l=a.charAt(k++);switch(l){case "'":case '"':let m=a.indexOf(l,k);m<0?k=d:(f.push(l,a.substring(k,m+1)),k=m+1);break;case ">":f.push(l);c.push(b(f.join(""), 
g));e=0;f=[];h=g=null;break;default:f.push(l)}break;default:throw Error();}e===1&&k>=d&&(k=h+1,c.push("<"),e=0,f=[],h=g=null)}return c.join("")}function KD(a,b){a=a.replace(/<\//g,"<\\/").replace(/\]\]>/g,"]]\\>");return b?a.replace(/{/g," \\{").replace(/}/g," \\}").replace(/\/\*/g,"/ *").replace(/\\$/,"\\ "):a}function X(a){BD(a,rD)?(a=FD(a.Yc()),a=String(a).replace(LD,ED)):a=String(a).replace(DD,ED);return a} 
function MD(a){a=String(a);const b=(d,e,f)=>{const g=Math.min(e.length-f,d.length);for(let k=0;k<g;k++){var h=e[f+k];if(d[k]!==("A"<=h&&h<="Z"?h.toLowerCase():h))return!1}return!0};for(var c=0;(c=a.indexOf("<",c))!=-1;){if(b("\x3c/script",a,c)||b("\x3c!--",a,c))return"zSoyz";c+=1}return a}function ND(a){if(a==null)return" null ";if(BD(a,sD))return a.Yc();switch(typeof a){case "boolean":case "number":return" "+a+" ";default:return"'"+String(String(a)).replace(OD,PD)+"'"}}const QD=/['()]/g; 
function RD(a){return"%"+a.charCodeAt(0).toString(16)}function SD(a){return String(a).replace(TD,UD)}function Z(a){return BD(a,vD)?KD(a.Yc(),!1):a==null?"":a instanceof kd?KD(ld(a),!1):KD(String(a),!0)}function BD(a,b){return a!=null&&a.bh===b} 
const VD={"\x00":"&#0;","\t":"&#9;","\n":"&#10;","\v":"&#11;","\f":"&#12;","\r":"&#13;"," ":"&#32;",'"':"&quot;","&":"&amp;","'":"&#39;","-":"&#45;","/":"&#47;","<":"&lt;","=":"&#61;",">":"&gt;","`":"&#96;","\u0085":"&#133;","\u00a0":"&#160;","\u2028":"&#8232;","\u2029":"&#8233;"};function ED(a){return VD[a]} 
const WD={"\x00":"\\x00","\b":"\\x08","\t":"\\t","\n":"\\n","\v":"\\x0b","\f":"\\f","\r":"\\r",'"':"\\x22",$:"\\x24","&":"\\x26","'":"\\x27","(":"\\x28",")":"\\x29","*":"\\x2a","+":"\\x2b",",":"\\x2c","-":"\\x2d",".":"\\x2e","/":"\\/",":":"\\x3a","<":"\\x3c","=":"\\x3d",">":"\\x3e","?":"\\x3f","[":"\\x5b","\\":"\\\\","]":"\\x5d","^":"\\x5e","{":"\\x7b","|":"\\x7c","}":"\\x7d","\u0085":"\\x85","\u2028":"\\u2028","\u2029":"\\u2029"};function PD(a){return WD[a]} 
const XD={"\x00":"%00","\u0001":"%01","\u0002":"%02","\u0003":"%03","\u0004":"%04","\u0005":"%05","\u0006":"%06","\u0007":"%07","\b":"%08","\t":"%09","\n":"%0A","\v":"%0B","\f":"%0C","\r":"%0D","\u000e":"%0E","\u000f":"%0F","\u0010":"%10","\u0011":"%11","\u0012":"%12","\u0013":"%13","\u0014":"%14","\u0015":"%15","\u0016":"%16","\u0017":"%17","\u0018":"%18","\u0019":"%19","\u001a":"%1A","\u001b":"%1B","\u001c":"%1C","\u001d":"%1D","\u001e":"%1E","\u001f":"%1F"," ":"%20",'"':"%22","'":"%27","(":"%28", 
")":"%29","<":"%3C",">":"%3E","\\":"%5C","{":"%7B","}":"%7D","\u007f":"%7F","\u0085":"%C2%85","\u00a0":"%C2%A0","\u2028":"%E2%80%A8","\u2029":"%E2%80%A9","\uff01":"%EF%BC%81","\uff03":"%EF%BC%83","\uff04":"%EF%BC%84","\uff06":"%EF%BC%86","\uff07":"%EF%BC%87","\uff08":"%EF%BC%88","\uff09":"%EF%BC%89","\uff0a":"%EF%BC%8A","\uff0b":"%EF%BC%8B","\uff0c":"%EF%BC%8C","\uff0f":"%EF%BC%8F","\uff1a":"%EF%BC%9A","\uff1b":"%EF%BC%9B","\uff1d":"%EF%BC%9D","\uff1f":"%EF%BC%9F","\uff20":"%EF%BC%A0","\uff3b":"%EF%BC%BB", 
"\uff3d":"%EF%BC%BD"};function UD(a){return XD[a]} 
const DD=/[\x00\x22\x26\x27\x3c\x3e]/g,LD=/[\x00\x22\x27\x3c\x3e]/g,OD=/[\x00\x08-\x0d\x22\x26\x27\/\x3c-\x3e\x5b-\x5d\x7b\x7d\x85\u2028\u2029]/g,TD=/[\x00- \x22\x27-\x29\x3c\x3e\\\x7b\x7d\x7f\x85\xa0\u2028\u2029\uff01\uff03\uff04\uff06-\uff0c\uff0f\uff1a\uff1b\uff1d\uff1f\uff20\uff3b\uff3d]/g,YD=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:\/?#]*(?:[\/?#]|$))/i,ZD=/^[^&:\/?#]*(?:[\/?#]|$)|^https?:|^ftp:|^data:image\/[a-z0-9+-]+;base64,[a-z0-9+\/]+=*$|^blob:/i,$D=/^[a-zA-Z0-9+\/_-]+={0,2}$/; 
function aE(a){a=String(a);return $D.test(a)?a:"zSoyz"}const HD=/</g;/* 
 
 
 Copyright Mathias Bynens <http://mathiasbynens.be/> 
 
 Permission is hereby granted, free of charge, to any person obtaining 
 a copy of this software and associated documentation files (the 
 "Software"), to deal in the Software without restriction, including 
 without limitation the rights to use, copy, modify, merge, publish, 
 distribute, sublicense, and/or sell copies of the Software, and to 
 permit persons to whom the Software is furnished to do so, subject to 
 the following conditions: 
 
 The above copyright notice and this permission notice shall be 
 included in all copies or substantial portions of the Software. 
 
 THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, 
 EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF 
 MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND 
 NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE 
 LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION 
 OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION 
 WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE. 
*/ 
const bE=Math.floor;var cE=/^xn--/,dE=/[\x2E\u3002\uFF0E\uFF61]/g;const eE={Zn:"Overflow: input needs wider integers to process",Wn:"Illegal input >= 0x80 (not a basic code point)",In:"Invalid input"};function fE(a){throw RangeError(eE[a]);}function gE(a,b){const c=a.split("@");let d="";c.length>1&&(d=c[0]+"@",a=c[1]);a=a.replace(dE,".");a=a.split(".").map(b).join(".");return d+a} 
function hE(a){return gE(a,b=>{if(cE.test(b)&&b.length>4){b=b.slice(4).toLowerCase();const h=[],k=b.length;let l=0,m=128;var c=72,d=b.lastIndexOf("-");d<0&&(d=0);for(var e=0;e<d;++e)b.charCodeAt(e)>=128&&fE("Illegal input >= 0x80 (not a basic code point)"),h.push(b.charCodeAt(e));for(d=d>0?d+1:0;d<k;){e=l;for(let n=1,p=36;;p+=36){d>=k&&fE("Invalid input");var f=b.charCodeAt(d++);f=f-48<10?f-22:f-65<26?f-65:f-97<26?f-97:36;(f>=36||f>bE((2147483647-l)/n))&&fE("Overflow: input needs wider integers to process"); 
l+=f*n;var g=p<=c?1:p>=c+26?26:p-c;if(f<g)break;f=36-g;n>bE(2147483647/f)&&fE("Overflow: input needs wider integers to process");n*=f}f=h.length+1;c=l-e;g=0;c=e==0?bE(c/700):c>>1;for(c+=bE(c/f);c>455;g+=36)c=bE(c/35);c=bE(g+36*c/(c+38));bE(l/f)>2147483647-m&&fE("Overflow: input needs wider integers to process");m+=bE(l/f);l%=f;h.splice(l++,0,m)}b=String.fromCodePoint.apply(null,h)}return b})};function iE(a,b,c){var d=a.Ca.contentWindow;a.Ya?(b={action:"search",searchTerm:b,rsToken:c},b.experimentId=a.Wa,a.postMessage(d,b)):(d=d.google.search.cse.element.getElement(a.jc),c={rsToken:c,hostName:a.host},a.Xa||typeof a.Wa!=="number"||(c.afsExperimentId=a.Wa),d.execute(b,void 0,c))} 
var jE=class{constructor(a){this.Ca=a.Ca;this.Na=a.Na;this.jc=a.jc;this.Za=a.Za;this.yd=a.yd;this.host=a.location.host;this.origin=a.location.origin;this.language=a.language;this.tc=a.tc;this.Wa=a.Wa;this.od=a.od||!1;this.Ya=a.Ya;this.Pb=a.Pb;this.g=a.Bg||!1;this.Xa=a.Xa||!1;this.Cg=!!a.Cg}postMessage(a,b){a?.postMessage(b,"https://www.gstatic.com")}init(){this.Ca.setAttribute("id","prose-iframe");this.Ca.setAttribute("width","100%");this.Ca.setAttribute("height","100%");this.Ca.style.cssText="box-sizing:border-box;border:unset;"; 
var a="https://www.google.com/s2/favicons?sz=64&domain_url="+encodeURIComponent(this.host);var b=Xc(a,Wc)||Qc;var c=hE(this.host.startsWith("www.")?this.host.slice(4):this.host),d={};a=this.jc;var e=this.Za,f=this.yd;const g=this.host;c=this.tc.replace("${website}",c);var h=this.od;const k=this.g,l=this.Cg,m=d&&d.Oc,n=d&&d.nj;d=CD;h="<style"+(m?' nonce="'+X(aE(m))+'"':"")+">.cse-favicon {display: block; float: left; height: 16px; position: absolute; left: 15px; width: 16px;}.cse-header {font-size: 16px; font-family: Arial; margin-left: 35px; margin-top: 6px; margin-bottom: unset; line-height: 16px;}.gsc-search-box {max-width: 520px !important;}.gsc-input {padding-right: 0 !important;}.gsc-input-box {border-radius: 16px 0 0 16px !important;}.gsc-search-button-v2 {border-left: 0 !important; border-radius: 0 16px 16px 0 !important; min-height: 30px !important; margin-left: 0 !important;}.gsc-cursor-page, .gsc-cursor-next-page, .gsc-cursor-numbered-page {color: #1a73e8 !important;}.gsc-cursor-chevron {fill: #1a73e8 !important;}.gsc-cursor-box {text-align: center !important;}.gsc-cursor-current-page {color: #000 !important;}.gcsc-find-more-on-google-root, .gcsc-find-more-on-google {display: none !important;}.prose-container {max-width: 652px;}#prose-empty-serp-container {display: flex; flex-direction: column; align-items: center; padding: 0; gap: 52px; position: relative; width: 248px; height: 259px; margin: auto; top: 100px;}#prose-empty-serp-icon-image {display: flex; flex-direction: row; justify-content: center; align-items: center; padding: 30px; gap: 10px; width: 124px; height: 124px; border-radius: 62px; flex: none; order: 1; flex-grow: 0; position: absolute; top: 0;}#prose-empty-serp-text-container {display: flex; flex-direction: column; align-items: center; padding: 0; gap: 19px; width: 248px; height: 83px; flex: none; order: 2; align-self: stretch; flex-grow: 0; position: absolute; top: 208px;}#prose-empty-serp-text-div {display: flex; flex-direction: column; align-items: flex-start; padding: 0; gap: 11px; width: 248px; height: 83px; flex: none; order: 0; align-self: stretch; flex-grow: 0;}#prose-empty-serp-supporting-text {width: 248px; height: 40px; font-family: 'Arial'; font-style: normal; font-weight: 400; font-size: 14px; line-height: 20px; text-align: center; letter-spacing: 0.2px; color: #202124; flex: none; order: 1; align-self: stretch; flex-grow: 0;}</style>"+ 
(h?"<script"+(n?' nonce="'+X(aE(n))+'"':"")+'>window.__gcse={initializationCallback:function(){top.postMessage({action:"init",adChannel:"'+String(f).replace(OD,PD)+'"},top.location.origin);}};\x3c/script>':"")+'<div class="prose-container"><img class="cse-favicon" src="';BD(b,tD)||BD(b,uD)?b=String(b).replace(TD,UD):Rc(b)?b=SD(Sc(b)):b instanceof Mc?b=SD(Oc(b).toString()):(b=String(b),b=ZD.test(b)?b.replace(TD,UD):"about:invalid#zSoyz");a=d(h+X(b)+'" alt="'+X(g)+' icon"><p class="cse-header"><strong>'+ 
AD(c)+"</strong></p>"+(l?'<div class="gcse-searchresults-only" data-gname="'+X(a)+'" data-adclient="'+X(e)+'" data-adchannel="'+X(f)+'" data-as_sitesearch="'+X(g)+'" data-personalizedAds="false" data-disableAds="true"></div>':'<div class="gcse-search" data-gname="'+X(a)+'" data-adclient="'+X(e)+'" data-adchannel="'+X(f)+'" data-as_sitesearch="'+X(g)+'" data-personalizedAds="false"></div>')+"</div>"+(k?"<div id=\"prose-empty-serp-container\"><img id='prose-empty-serp-icon-image' src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTI0IiBoZWlnaHQ9IjEyNCIgdmlld0JveD0iMCAwIDEyNCAxMjQiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMjQiIGhlaWdodD0iMTI0IiByeD0iNjIiIGZpbGw9IiNGMUYzRjQiLz4KPHBhdGggZmlsbC1ydWxlPSJldmVub2RkIiBjbGlwLXJ1bGU9ImV2ZW5vZGQiIGQ9Ik02OS4zNiA2NS4zODY3TDg0LjY0IDgwLjY2NjdMODAuNjY2NyA4NC42NEw2NS4zODY3IDY5LjM2QzYyLjUzMzMgNzEuNDEzMyA1OS4wOTMzIDcyLjY2NjcgNTUuMzMzMyA3Mi42NjY3QzQ1Ljc2IDcyLjY2NjcgMzggNjQuOTA2NyAzOCA1NS4zMzMzQzM4IDQ1Ljc2IDQ1Ljc2IDM4IDU1LjMzMzMgMzhDNjQuOTA2NyAzOCA3Mi42NjY3IDQ1Ljc2IDcyLjY2NjcgNTUuMzMzM0M3Mi42NjY3IDU5LjA5MzMgNzEuNDEzMyA2Mi41MzMzIDY5LjM2IDY1LjM4NjdaTTU1LjMzMzMgNDMuMzMzM0M0OC42OTMzIDQzLjMzMzMgNDMuMzMzMyA0OC42OTMzIDQzLjMzMzMgNTUuMzMzM0M0My4zMzMzIDYxLjk3MzMgNDguNjkzMyA2Ny4zMzMzIDU1LjMzMzMgNjcuMzMzM0M2MS45NzMzIDY3LjMzMzMgNjcuMzMzMyA2MS45NzMzIDY3LjMzMzMgNTUuMzMzM0M2Ny4zMzMzIDQ4LjY5MzMgNjEuOTczMyA0My4zMzMzIDU1LjMzMzMgNDMuMzMzM1oiIGZpbGw9IiM5QUEwQTYiLz4KPC9zdmc+Cg==' alt=''><div id='prose-empty-serp-text-container'><div id='prose-empty-serp-text-div'><div id='prose-empty-serp-supporting-text'>Search this website by entering a keyword.</div></div></div></div>": 
""));a=xD(a);this.Ya?(a=this.Ca,e=Rd`https://www.gstatic.com/prose/protected/${this.Pb||"558153351"}/iframe.html?cx=${this.Na}&host=${this.host}&hl=${this.language}&lrh=${this.tc}&client=${this.Za}&origin=${this.origin}`,a.src=Oc(e).toString()):(e=new Map([["cx",this.Na],["language",this.language]]),this.Xa&&(f=Array.isArray(this.Wa)?this.Wa:[this.Wa],f.length&&e.set("fexp",f.join())),e=Sd(Rd`https://cse.google.com/cse.js`,e),e=Oc(e).toString(),e=cd(`<script src="${Cd(e)}"`+">\x3c/script>"),a=Kd("body", 
{style:"margin:0;"},[a,e]),this.Ca.srcdoc=dd(a))}};function kE(a){a.google_reactive_ads_global_state?(a.google_reactive_ads_global_state.sideRailProcessedFixedElements==null&&(a.google_reactive_ads_global_state.sideRailProcessedFixedElements=new Set),a.google_reactive_ads_global_state.sideRailAvailableSpace==null&&(a.google_reactive_ads_global_state.sideRailAvailableSpace=new Map),a.google_reactive_ads_global_state.sideRailPlasParam==null&&(a.google_reactive_ads_global_state.sideRailPlasParam=new Map),a.google_reactive_ads_global_state.sideRailMutationCallbacks== 
null&&(a.google_reactive_ads_global_state.sideRailMutationCallbacks=[])):a.google_reactive_ads_global_state=new lE;return a.google_reactive_ads_global_state} 
var lE=class{constructor(){this.wasPlaTagProcessed=!1;this.wasReactiveAdConfigReceived={};this.adCount={};this.wasReactiveAdVisible={};this.stateForType={};this.reactiveTypeEnabledInAsfe={};this.wasReactiveTagRequestSent=!1;this.reactiveTypeDisabledByPublisher={};this.tagSpecificState={};this.messageValidationEnabled=!1;this.floatingAdsStacking=new mE;this.sideRailProcessedFixedElements=new Set;this.sideRailAvailableSpace=new Map;this.sideRailPlasParam=new Map;this.sideRailMutationCallbacks=[];this.g= 
null;this.clickTriggeredInterstitialMayBeDisplayed=!1}},mE=class{constructor(){this.maxZIndexRestrictions={};this.nextRestrictionId=0;this.maxZIndexListeners=[]}};function nE(a,b){return new oE(a,b)}function pE(a){const b=qE(a);Na(a.floatingAdsStacking.maxZIndexListeners,c=>c(b))}function qE(a){a=ce(a.floatingAdsStacking.maxZIndexRestrictions);return a.length?Math.min.apply(null,a):null}function rE(a,b){ab(a.floatingAdsStacking.maxZIndexListeners,c=>c===b)}var sE=class{constructor(a){this.floatingAdsStacking=kE(a).floatingAdsStacking}}; 
function tE(a){if(a.g==null){var b=a.controller,c=a.Jb;const d=b.floatingAdsStacking.nextRestrictionId++;b.floatingAdsStacking.maxZIndexRestrictions[d]=c;pE(b);a.g=d}}function uE(a){if(a.g!=null){var b=a.controller;delete b.floatingAdsStacking.maxZIndexRestrictions[a.g];pE(b);a.g=null}}var oE=class{constructor(a,b){this.controller=a;this.Jb=b;this.g=null}};function vE(a){a=a.activeElement;const b=a?.shadowRoot;return b?vE(b)||a:a}function wE(a,b){return xE(b,a.document.body).flatMap(c=>yE(c))}function xE(a,b){var c=a;for(a=[];c&&c!==b;){a.push(c);let e;var d;(d=c.parentElement)||(c=c.getRootNode(),d=((e=c.mode&&c.host?c:null)==null?void 0:e.host)||null);c=d}return c!==b?[]:a}function yE(a){const b=a.parentElement;return b?Array.from(b.children).filter(c=>c!==a):[]};function zE(a){a.g!==null&&(a.g.Dj.forEach(b=>{b.inert=!1}),a.g.Pk?.focus(),a.g=null)}function AE(a,b){zE(a);const c=vE(a.B.document);b=wE(a.B,b).filter(d=>!d.inert);b.forEach(d=>{d.inert=!0});a.g={Pk:c,Dj:b}}var BE=class{constructor(a){this.B=a;this.g=null}Oe(){zE(this)}};function CE(a){return new DE(a,new is(a,a.document.body),new is(a,a.document.documentElement),new is(a,a.document.documentElement))} 
function EE(a){hs(a.j,"scroll-behavior","auto");var b=FE(a.B);b.activePageScrollPreventers.add(a);b.previousWindowScroll===null&&(b.previousWindowScroll=a.B.scrollY);hs(a.g,"position","fixed");hs(a.g,"top",`${-b.previousWindowScroll}px`);hs(a.g,"width","100%");hs(a.g,"overflow-x","hidden");hs(a.g,"overflow-y","hidden");a.dontOverrideDocumentOverflowUnlessNeeded()?(b=getComputedStyle(a.B.document.documentElement),GE(b.overflowX)&&hs(a.i,"overflow-x","unset"),GE(b.overflowY)&&hs(a.i,"overflow-y","unset")): 
(hs(a.i,"overflow-x","hidden"),hs(a.i,"overflow-y","hidden"))}function GE(a){return a==="scroll"||a==="auto"}function HE(a){gs(a.g);gs(a.i);const b=FE(a.B);b.activePageScrollPreventers.delete(a);b.activePageScrollPreventers.size===0&&(a.B.scrollTo(0,b.previousWindowScroll||0),b.previousWindowScroll=null);gs(a.j)}var DE=class{constructor(a,b,c,d){this.B=a;this.g=b;this.i=c;this.j=d}dontOverrideDocumentOverflowUnlessNeeded(){return FE(this.B).dontOverrideDocumentOverflowUnlessNeeded}}; 
function FE(a){return a.googPageScrollPreventerInfo=a.googPageScrollPreventerInfo||{previousWindowScroll:null,activePageScrollPreventers:new Set,dontOverrideDocumentOverflowUnlessNeeded:!1}}function IE(a){return a.googPageScrollPreventerInfo&&a.googPageScrollPreventerInfo.activePageScrollPreventers.size>0?!0:!1};function JE(a,b){return KE(`#${a}`,b)}function LE(a,b){return KE(`.${a}`,b)}function KE(a,b){b=b.querySelector(a);if(!b)throw Error(`Element (${a}) does not exist`);return b};function ME(a,b){const c=a.document.createElement("div");$u(a,c);a=c.attachShadow({mode:"open"});b&&c.classList.add(b);return{wb:c,shadowRoot:a}};function NE(a,b){b=ME(a,b);a.document.body.appendChild(b.wb);return b}function OE(a,b){const c=new R(b.O);rs(b,!0,()=>void c.g(!0));rs(b,!1,()=>{a.setTimeout(()=>{b.O||c.g(!1)},700)});return ms(c)};function PE(a){var b={},c=a.Vd,d=a.vg,e=a.Sd;const f=a.Ld,g=a.Vg,h=a.zIndex,k=a.cf;a=a.bb;b=b&&b.Oc;c="<style"+(b?' nonce="'+X(aE(b))+'"':"")+">#hd-drawer-container {position: fixed; left: 0; top: 0; width: 100vw; height: 100%; overflow: hidden; z-index: "+Z(h)+"; pointer-events: none;}#hd-drawer-container.hd-revealed {pointer-events: auto;}#hd-modal-background {position: absolute; left: 0; bottom: 0; background-color: black; transition: opacity .5s ease-in-out; width: 100%; height: 100%; opacity: 0;}.hd-revealed > #hd-modal-background {opacity: 0.5;}#hd-drawer {position: absolute; top: 0; height: 100%; width: "+ 
Z(c)+"; background-color: white; display: flex; flex-direction: column; box-sizing: border-box; padding-bottom: ";d=d?a?20:16:0;c+=Z(d)+"px; transition: transform "+Z(k)+"s ease-in-out;"+(e?"left: 0; border-top-right-radius: "+Z(d)+"px; border-bottom-right-radius: "+Z(d)+"px; transform: translateX(-100%);":"right: 0; border-top-left-radius: "+Z(d)+"px; border-bottom-left-radius: "+Z(d)+"px; transform: translateX(100%);")+"}.hd-revealed > #hd-drawer {transform: translateY(0);}#hd-control-bar {"+(a? 
"height: 24px;":"padding: 5px;")+"}.hd-control-button {border: none; background: none; cursor: pointer;"+(a?"":"padding: 5px;")+"}#hd-back-arrow-button {"+(e?"float: right;":"float: left;")+"}#hd-close-button {"+(e?"float: left;":"float: right;")+'}#hd-content-container {flex-grow: 1; overflow: auto;}#hd-content-container::-webkit-scrollbar * {background: transparent;}.hd-hidden {visibility: hidden;}</style><div id="hd-drawer-container" class="hd-hidden" aria-modal="true" role="dialog" tabindex="0"><div id="hd-modal-background"></div><div id="hd-drawer"><div id="hd-control-bar"><button id="hd-back-arrow-button" class="hd-control-button hd-hidden" aria-label="'+ 
X(g)+'">';e=a?"#5F6368":"#444746";c+='<svg xmlns="http://www.w3.org/2000/svg" height="24" width="24" fill="'+X(e)+'"><path d="m12 20-8-8 8-8 1.425 1.4-5.6 5.6H20v2H7.825l5.6 5.6Z"/></svg></button><button id="hd-close-button" class="hd-control-button" aria-label="'+X(f)+'"><svg xmlns="http://www.w3.org/2000/svg" height="24" width="24" fill="'+X(e)+'"><path d="M6.4 19 5 17.6 10.6 12 5 6.4 6.4 5 12 10.6 17.6 5 19 6.4 13.4 12 19 17.6 17.6 19 12 13.4Z"/></svg></button></div><div id="hd-content-container"></div></div></div>'; 
return CD(c)};function QE(a){a=a.top;if(!a)return null;try{var b=a.history}catch(c){b=null}b=b&&b.pushState&&typeof b.pushState==="function"?b:null;if(!b)return null;if(a.googNavStack)return a.googNavStack;b=new RE(a,b);b.init();return b?a.googNavStack=b:null}function SE(a,b){return b?b.googNavStackId===a.i?b:null:null}function TE(a,b){for(let c=b.length-1;c>=0;--c){const d=c===0;a.L.requestAnimationFrame(()=>void b[c].bl({isFinal:d}))}} 
function UE(a,b){b=hb(a.stack,b,(c,d)=>c-d.yh.googNavStackStateId);if(b>=0)return a.stack.splice(b,a.stack.length-b);b=-b-1;return a.stack.splice(b,a.stack.length-b)} 
class RE extends Q{constructor(a,b){super();this.L=a;this.history=b;this.stack=[];this.i=Math.random()*1E9>>>0;this.l=0;this.j=c=>{(c=SE(this,c.state))?TE(this,UE(this,c.googNavStackStateId+.5)):TE(this,this.stack.splice(0,this.stack.length))}}pushEvent(){const a={googNavStackId:this.i,googNavStackStateId:this.l++},b=new Promise(c=>{this.stack.push({bl:c,yh:a})});this.history.pushState(a,"");return{navigatedBack:b,triggerNavigateBack:()=>{const c=UE(this,a.googNavStackStateId);var d;if(d=c.length> 
0){d=c[0].yh;const e=SE(this,this.history.state);d=e&&e.googNavStackId===d.googNavStackId&&e.googNavStackStateId===d.googNavStackStateId}d&&this.history.go(-c.length);TE(this,c)}}}init(){this.L.addEventListener("popstate",this.j)}g(){this.L.removeEventListener("popstate",this.j);super.g()}};function VE(a){return(a=QE(a))?new WE(a):null}function XE(a){if(!a.i){var {navigatedBack:b,triggerNavigateBack:c}=a.l.pushEvent();a.i=c;b.then(()=>{a.i&&!a.A&&(a.i=null,xs(a.j))})}}var WE=class extends Q{constructor(a){super();this.l=a;this.j=new ys;this.i=null}};function YE(a,b,c){var d=c.xf?null:new BE(a);const e=nE(new sE(a),c.zIndex-1);b=ZE(a,b,c);d=new $E(a,b,d,c.Wc,CE(a),e);d.init();(c.jh||c.jh===void 0)&&aF(d);c.Tc&&((a=VE(a))?bF(d,a,c.hg):c.hg?.(Error("Unable to create closeNavigator")));return d}function aF(a){a.C=b=>{b.key==="Escape"&&a.i.O&&a.collapse()};a.B.document.body.addEventListener("keydown",a.C)} 
function bF(a,b,c){rs(a.i,!0,()=>{try{XE(b)}catch(d){c?.(d)}});rs(a.i,!1,()=>{try{b.i&&(b.i(),b.i=null)}catch(d){c?.(d)}});vs(b.j).listen(()=>void a.collapse());es(a,b)}function cF(a){if(a.A)throw Error("Accessing domItems after disposal");return a.D}function dF(a){a.B.setTimeout(()=>{a.i.O&&cF(a).Oa.focus()},500)}function eF(a){const {gg:b,jj:c}=cF(a);b.addEventListener("click",()=>void a.collapse());c.addEventListener("click",()=>void a.collapse())} 
function fF(a){rs(a.j,!1,()=>{cF(a).Oa.classList.add("hd-hidden")})} 
var $E=class extends Q{constructor(a,b,c,d=!0,e,f){super();this.B=a;this.D=b;this.l=c;this.Wc=d;this.i=new R(!1);this.j=OE(a,this.i);rs(this.j,!0,()=>{EE(e);tE(f)});rs(this.j,!1,()=>{HE(e);uE(f)})}show({hh:a=!1}={}){if(this.A)throw Error("Cannot show drawer after disposal");cF(this).Oa.classList.remove("hd-hidden");cs(this.B);cF(this).Oa.classList.add("hd-revealed");this.i.g(!0);this.l&&(AE(this.l,cF(this).xb.wb),this.Wc&&dF(this));a&&rs(this.j,!1,()=>{this.dispose()})}collapse(){cF(this).Oa.classList.remove("hd-revealed"); 
this.i.g(!1);this.l?.Oe()}isVisible(){return this.j}init(){eF(this);fF(this)}g(){this.C&&this.B.document.body.removeEventListener("keydown",this.C);const a=this.D.xb.wb,b=a.parentNode;b&&b.removeChild(a);this.l?.Oe();super.g()}}; 
function ZE(a,b,c){const d=NE(a,c.yf),e=d.shadowRoot;e.appendChild(sm(new fm(a.document),xD(PE({Vd:c.Vd,vg:c.vg??!0,Sd:c.Sd||!1,Ld:c.Ld,Vg:c.Vg||"",zIndex:c.zIndex,cf:.5,bb:c.bb||!1}))));const f=JE("hd-drawer-container",e);c.Ef?.i(g=>{f.setAttribute("aria-label",g)});c=JE("hd-content-container",e);c.appendChild(b);cs(a);return{Oa:f,gg:JE("hd-modal-background",e),rf:c,jj:JE("hd-close-button",e),hp:JE("hd-back-arrow-button",e),xb:d}};function gF(a){{var b={};var c=a.Kk;const f=a.Qj;var d=a.zIndex,e=a.cf;a=a.bb;b=b&&b.Oc;d="<style"+(b?' nonce="'+X(aE(b))+'"':"")+">#ved-drawer-container {position:  fixed; left: 0; top: 0; width: 100vw; height: 100%; overflow: hidden; z-index: "+Z(d)+"; pointer-events: none;}#ved-drawer-container.ved-revealed {pointer-events: auto;}#ved-modal-background {position: absolute; left: 0; bottom: 0; background-color: black; transition: opacity .5s ease-in-out; width: 100%; height: 100%; opacity: 0;}.ved-revealed > #ved-modal-background {opacity: 0.5;}#ved-ui-revealer {position: absolute; left: 0; bottom: 0; width: 100%; height: "+ 
Z(f)+"%; transition: transform "+Z(e)+"s ease-in-out; transform: translateY(100%);}#ved-ui-revealer.ved-no-animation {transition-property: none;}.ved-revealed > #ved-ui-revealer {transform: translateY(0);}#ved-scroller-container {position: absolute; left: 0; bottom: 0; width: 100%; height: 100%; clip-path: inset(0 0 -50px 0 round ";e=a?20:28;d+=Z(e)+"px);}#ved-scroller {position: relative; width: 100%; height: 100%; overflow-y: scroll; -ms-overflow-style: none; scrollbar-width: none; overflow-y: scroll; overscroll-behavior: none; scroll-snap-type: y mandatory;}#ved-scroller.ved-scrolling-paused {overflow: hidden;}#ved-scroller.ved-no-snap {scroll-snap-type: none;}#ved-scroller::-webkit-scrollbar {display: none;}#ved-scrolled-stack {width: 100%; height: 100%; overflow: visible;}#ved-scrolled-stack.ved-with-background {background-color: white;}.ved-snap-point-top {scroll-snap-align: start;}.ved-snap-point-bottom {scroll-snap-align: end;}#ved-fully-closed-anchor {height: "+ 
Z(c/f*100)+"%;}.ved-with-background #ved-fully-closed-anchor {background-color: white;}#ved-partially-extended-anchor {height: "+Z((f-c)/f*100)+"%;}.ved-with-background #ved-partially-extended-anchor {background-color: white;}#ved-moving-handle-holder {scroll-snap-stop: always;}.ved-with-background #ved-moving-handle-holder {background-color: white;}#ved-fixed-handle-holder {position: absolute; left: 0; top: 0; width: 100%;}#ved-visible-scrolled-items {display: flex; flex-direction: column; min-height: "+ 
Z(c/f*100)+"%;}#ved-content-background {width: 100%; flex-grow: 1; padding-top: 1px; margin-top: -1px; background-color: white;}#ved-content-sizer {overflow: hidden; width: 100%; height: 100%;}#ved-content-container {width: 100%;}#ved-over-scroll-block {display: flex; flex-direction: column; position: absolute; bottom: 0; left: 0; width: 100%; height: "+Z(c/f*100)+"%; pointer-events: none;}#ved-over-scroll-handle-spacer {height: "+Z(80)+"px;}#ved-over-scroll-background {flex-grow: 1; background-color: white;}.ved-handle {align-items: flex-end; border-radius: "+ 
Z(e)+"px "+Z(e)+"px 0 0; background: white; display: flex; height: "+Z(30)+"px; justify-content: center; cursor: grab;}.ved-handle-icon {"+(a?"background: #dadce0; width: 50px;":"background: #747775; opacity: 0.4; width: 32px;")+'border-radius: 2px; height: 4px; margin-bottom: 8px;}.ved-hidden {visibility: hidden;}</style><div id="ved-drawer-container" class="ved-hidden" aria-modal="true" role="dialog" tabindex="0"><div id="ved-modal-background"></div><div id="ved-ui-revealer"><div id="ved-over-scroll-block" class="ved-hidden"><div id=\'ved-over-scroll-handle-spacer\'></div><div id=\'ved-over-scroll-background\'></div></div><div id="ved-scroller-container"><div id="ved-scroller"><div id="ved-scrolled-stack"><div id="ved-fully-closed-anchor" class="ved-snap-point-top"></div><div id="ved-partially-extended-anchor" class="ved-snap-point-top"></div><div id="ved-visible-scrolled-items"><div id="ved-moving-handle-holder" class="ved-snap-point-top">'+ 
hF("ved-moving-handle")+'</div><div id="ved-content-background"><div id="ved-content-sizer" class="ved-snap-point-bottom"><div id="ved-content-container"></div></div></div></div></div></div></div><div id="ved-fixed-handle-holder" class="ved-hidden">'+hF("ved-fixed-handle")+"</div></div></div>";c=CD(d)}return c}function hF(a){return CD('<div class="ved-handle" id="'+X(a)+'"><div class="ved-handle-icon"></div></div>')};function iF(a){return Ms(a.g).map(b=>b?jF(a,b):0)}function jF(a,b){switch(a.direction){case 0:return kF(-b.zi);case 1:return kF(-b.yi);default:throw Error(`Unhandled direction: ${a.direction}`);}}function lF(a){return Os(a.g).map(b=>jF(a,b))}var mF=class{constructor(a){this.g=a;this.direction=0}};function kF(a){return a===0?0:a};function nF(a){if(a.A)throw Error("Accessing domItems after disposal");return a.D}function oF(a){a.B.setTimeout(()=>{a.i.O&&nF(a).Oa.focus()},500)}function pF(a){nF(a).Oa.classList.remove("ved-hidden");cs(a.B);const {ra:b,rb:c}=nF(a);c.getBoundingClientRect().top<=b.getBoundingClientRect().top||qF(a);nF(a).Oa.classList.add("ved-revealed");a.i.g(!0);a.j&&(AE(a.j,nF(a).xb.wb),a.Wc&&oF(a))}function rF(a){return OE(a.B,a.i)} 
function sF(a,b){const c=new R(b());vs(a.J).listen(()=>void c.g(b()));return ms(c)}function tF(a){const {ra:b,xe:c}=nF(a);return sF(a,()=>c.getBoundingClientRect().top<=b.getBoundingClientRect().top)}function uF(a){const {ra:b,xe:c}=nF(a);return sF(a,()=>c.getBoundingClientRect().top<=b.getBoundingClientRect().top-1)}function vF(a){const {ra:b}=nF(a);return sF(a,()=>b.scrollTop===b.scrollHeight-b.clientHeight)}function wF(a){return ns(tF(a),vF(a))} 
function xF(a){const {ra:b,rb:c}=nF(a);return sF(a,()=>c.getBoundingClientRect().top<b.getBoundingClientRect().top-1)}function qF(a){nF(a).rb.classList.add("ved-snap-point-top");var b=yF(a,nF(a).rb);nF(a).ra.scrollTop=b;zF(a)}function AF(a){ps(tF(a),!0,()=>{const {rh:b,vd:c}=nF(a);b.classList.remove("ved-hidden");c.classList.add("ved-with-background")});ps(tF(a),!1,()=>{const {rh:b,vd:c}=nF(a);b.classList.add("ved-hidden");c.classList.remove("ved-with-background")})} 
function BF(a){const b=Ts(a.B,nF(a).rf);Ws(b).i(()=>void CF(a));es(a,b)}function DF(a){ps(EF(a),!0,()=>{nF(a).Uh.classList.remove("ved-hidden")});ps(EF(a),!1,()=>{nF(a).Uh.classList.add("ved-hidden")})}function FF(a){const b=()=>void xs(a.G),{gg:c,rb:d,Pj:e}=nF(a);c.addEventListener("click",b);d.addEventListener("click",b);e.addEventListener("click",b);rs(GF(a),!0,b)}function HF(a){rs(rF(a),!1,()=>{qF(a);nF(a).Oa.classList.add("ved-hidden")})}function zF(a){qs(a.l,!1,()=>void xs(a.J))} 
function CF(a){if(!a.l.O){var {eh:b,rf:c}=nF(a),d=c.getBoundingClientRect().height;d=Math.max(IF(a),d);a.l.g(!0);var e=JF(a);b.style.setProperty("height",`${d}px`);e();a.B.requestAnimationFrame(()=>{a.B.requestAnimationFrame(()=>{a.l.g(!1)})})}}function EF(a){const {ra:b,rb:c}=nF(a);return sF(a,()=>c.getBoundingClientRect().top<=b.getBoundingClientRect().top)}function GF(a){return ls(a.C.map(zt),KF(a))}function KF(a){return sF(a,()=>nF(a).ra.scrollTop===0)} 
function yF(a,b){({vd:a}=nF(a));a=a.getBoundingClientRect().top;return b.getBoundingClientRect().top-a}function LF(a,b){a.C.g(!0);const {vd:c,ra:d}=nF(a);d.scrollTop=0;d.classList.add("ved-scrolling-paused");c.style.setProperty("margin-top",`-${b}px`);return()=>void MF(a,b)}function MF(a,b){const {vd:c,ra:d}=nF(a);c.style.removeProperty("margin-top");d.classList.remove("ved-scrolling-paused");nF(a).ra.scrollTop=b;zF(a);a.C.g(!1)} 
function JF(a){const b=nF(a).ra.scrollTop;LF(a,b);return()=>void MF(a,b)}function IF(a){const {ra:b,xe:c,eh:d,rb:e}=nF(a);a=b.getBoundingClientRect();const f=c.getBoundingClientRect();var g=d.getBoundingClientRect();const h=e.getBoundingClientRect();g=g.top-f.top;return Math.max(a.height-h.height-g,Math.min(a.height,a.bottom-f.top)-g)} 
var NF=class extends Q{constructor(a,b,c,d,e=!0){super();this.B=a;this.D=b;this.M=c;this.j=d;this.Wc=e;this.G=new ys;this.J=new ys;this.i=new R(!1);this.C=new R(!1);this.l=new R(!1)}init(){qF(this);AF(this);BF(this);DF(this);FF(this);HF(this);nF(this).ra.addEventListener("scroll",()=>void zF(this))}g(){const a=this.D.xb.wb,b=a.parentNode;b&&b.removeChild(a);this.j?.Oe();super.g()}}; 
function OF(a,b,c){const d=NE(a,c.yf),e=d.shadowRoot;e.appendChild(sm(new fm(a.document),xD(gF({Kk:c.Wh*100,Qj:c.sh*100,zIndex:c.zIndex,cf:.5,bb:c.bb||!1}))));const f=JE("ved-drawer-container",e);c.Ef?.i(g=>{f.setAttribute("aria-label",g)});c=JE("ved-content-container",e);c.appendChild(b);cs(a);return{Oa:f,gg:JE("ved-modal-background",e),ti:JE("ved-ui-revealer",e),ra:JE("ved-scroller",e),vd:JE("ved-scrolled-stack",e),Pj:JE("ved-fully-closed-anchor",e),rb:JE("ved-partially-extended-anchor",e),eh:JE("ved-content-sizer", 
e),rf:c,tp:JE("ved-moving-handle",e),xe:JE("ved-moving-handle-holder",e),Mj:JE("ved-fixed-handle",e),rh:JE("ved-fixed-handle-holder",e),Uh:JE("ved-over-scroll-block",e),xb:d}};function PF(a,b,c){var d=nE(new sE(a),c.zIndex-1);b=OF(a,b,c);const e=c.xf?null:new BE(a);var f=b.Mj;f=new Ps(new Gs(a,f),new Ds(f));var g=f.g;g.l.addEventListener("mousedown",g.D);g.A.addEventListener("mouseup",g.C);g.A.addEventListener("mousemove",g.H,{passive:!1});g=f.i;g.i.addEventListener("touchstart",g.H);g.i.addEventListener("touchend",g.l);g.i.addEventListener("touchmove",g.C,{passive:!1});b=new NF(a,b,new mF(f),e,c.Wc);b.init();d=new QF(a,b,CE(a),d);es(d,b);d.init();c.Tc&&((a=VE(a))?RF(d, 
a,c.hg):c.hg?.(Error("Unable to create closeNavigator")));return d}function RF(a,b,c){rs(a.i.i,!0,()=>{try{XE(b)}catch(d){c?.(d)}});rs(a.i.i,!1,()=>{try{b.i&&(b.i(),b.i=null)}catch(d){c?.(d)}});vs(b.j).listen(()=>void a.collapse());es(a,b)} 
function SF(a){rs(ls(wF(a.i),xF(a.i)),!0,()=>{nF(a.i).rb.classList.remove("ved-snap-point-top")});ps(uF(a.i),!0,()=>{nF(a.i).ra.classList.add("ved-no-snap")});ps(uF(a.i),!1,()=>{nF(a.i).ra.classList.remove("ved-no-snap")});rs(uF(a.i),!1,()=>{var b=a.i;var c=nF(b).xe;c=LF(b,yF(b,c));b.B.setTimeout(c,100)})} 
function TF(a){const b=a.i.M;iF(b).listen(c=>{c=-c;if(c>0){const {ti:d}=nF(a.i);d.classList.add("ved-no-animation");d.style.setProperty("transform",`translateY(${c}px)`)}else({ti:c}=nF(a.i)),c.classList.remove("ved-no-animation"),c.style.removeProperty("transform")});lF(b).listen(c=>{-c>30&&a.collapse()})} 
var QF=class extends Q{constructor(a,b,c,d){super();this.B=a;this.i=b;rs(rF(b),!0,()=>{EE(c);tE(d)});rs(rF(b),!1,()=>{HE(c);uE(d)})}show({hh:a=!1}={}){if(this.A)throw Error("Cannot show drawer after disposal");pF(this.i);a&&rs(rF(this.i),!1,()=>{this.dispose()})}collapse(){var a=this.i;nF(a).Oa.classList.remove("ved-revealed");a.i.g(!1);a.j?.Oe()}isVisible(){return rF(this.i)}init(){vs(this.i.G).listen(()=>{this.collapse()});SF(this);TF(this);cs(this.B)}};function UF(a,b){return ve()===2?PF(a.B,b,{Wh:.95,sh:.95,zIndex:2147483645,Tc:!0,bb:!0}):YE(a.B,b,{Vd:"min(65vw, 768px)",Ld:"",Sd:!1,zIndex:2147483645,Tc:!0,vg:!1,bb:!0})} 
function VF(a){((d,e)=>{d[e]=d[e]||function(){(d[e].q=d[e].q||[]).push(arguments)};d[e].t=(new Date).getTime()})(a.B,"_googCsa");const b=a.J.map(d=>({container:d,relatedSearches:5})),c={pubId:a.Za,styleId:"5134551505",hl:a.language,fexp:a.l,channel:"AutoRsVariant",resultsPageBaseUrl:"http://google.com",resultsPageQueryParam:"q",relatedSearchTargeting:"content",relatedSearchResultClickedCallback:a.nb.bind(a),relatedSearchUseResultCallback:!0,cx:a.Na};a.V&&(c.adLoadedCallback=a.X.bind(a));a.Xa&&a.j instanceof 
Array&&(c.fexp=a.j.join(","));a.B._googCsa("relatedsearch",c,b)}function WF(a){a.B.addEventListener("message",b=>{b.origin==="https://www.gstatic.com"&&b.data.action==="resize"&&(a.i.style.height=`${Math.ceil(b.data.height)+1}px`)})} 
var XF=class extends Q{constructor(a,b,c,d,e,f,g,h,k=()=>{}){super();this.B=a;this.J=b;this.G=e;this.l=f;this.Ya=!0;this.Pb=h;this.Ka=k;this.language=d?.g()||"en";this.pa=d?.i()||"Search results from ${website}";this.V=O(cw);this.Za=c.replace("ca","partner");this.D=new fm(a.document);this.i=rm(this.D,"IFRAME");this.Na=g.g?g.Na:"9d449ff4a772956c6";this.j=(this.Xa=!0,N(kr).g().concat(this.l));this.C=new jE({Ca:this.i,Na:this.Na,jc:"auto-rs-prose",Za:this.Za,yd:"AutoRsVariant",location:a.location,language:this.language, 
tc:this.pa,Wa:this.j,Ya:this.Ya,Pb:this.Pb,od:!1,Bg:!1,Xa:this.Xa});this.M=UF(this,this.i);es(this,this.M)}init(){this.J.length!==0&&(this.V||My(1075,()=>{this.C.init()},this.B),My(1076,()=>{const a=rm(this.D,"SCRIPT");fd(a,Rd`https://www.google.com/adsense/search/async-ads.js`);this.B.document.head.appendChild(a)},this.B),VF(this),Vu(this.G,{sts:"ok"}),this.Ya&&WF(this))}X(a,b){b?My(1075,()=>{this.C.init()},this.B):(this.Ka(),Xu(this.G,"pfns"))}nb(a,b){iE(this.C,a,b);(()=>{if(!this.Ya){const c=new ResizeObserver(async e=> 
{this.i.height="0";q(await q(new Promise(f=>{this.B.requestAnimationFrame(f)})));this.i.height=e[0].target.scrollHeight.toString()}),d=()=>{const e=this.i.contentDocument?.documentElement;e?c.observe(e):(console.warn("iframe body missing"),setTimeout(d,1E3))};d()}this.M.show()})()}};var YF=class{constructor(a,b){this.g=a;this.Na=b}};var ZF=class{constructor(a,b,c){this.l=a;this.i=b;this.C=c;this.A="autors-widget";this.g=null;this.j=new R(null)}init(){var a=this.i.ga;a=fv(a.g.document,a.G||!1);const b=this.C.Yc(this.l);a.appendChild(b);this.A&&(a.className=this.A);this.g=a;Iz(this.i,this.g);this.j.g(b)}};async function $F(a){q(await q(new Promise(b=>{setTimeout(()=>{a.run();b()})})))} 
function aG(a){if((!a.Rd||!bG(a.config,a.ca,a.i))&&cG(z(a.g,lu,5),a.i)){var b=a.g.i();b=lD(a.B,a.config,a.ca,a.i,{zl:!!b?.l(),Rd:a.Rd,up:!!b?.g(),xl:!!b?.C()});b=dG(b,a.B);var c=Object.keys(b),d=Object.values(b),e=Tu(a.g.g()?.g())||0,f=eG(a.g),g=String(E(a.g,13));if(!z(a.config,iu,25)?.g()){var h=()=>{d.forEach(k=>{k.g&&k.g.parentNode&&k.g.parentNode.removeChild(k.g);k.g=null;k.j.g(null)})};My(1074,()=>{(new XF(a.B,c,a.Va,z(a.g,lu,5),a.i,e,f,g,h)).init()},a.B)}}} 
var fG=class{constructor(a,b,c,d,e){this.B=a;this.config=c;this.Va=d;this.ca=e;this.Rd=!0;this.g=qb(z(this.config,nu,28));this.i=new Yu(a,b,this.g)}run(){try{aG(this)}catch(a){Xu(this.i,"pfere",a)}}};function bG(a,b,c){a=Tu(z(a,nu,28)?.g()?.g())||0;const d=N(Lx).g(gw.g,gw.defaultValue);return d&&d.includes(a.toString())?!1:(b?zi(b,2):[]).length===0?(Xu(c,"pfeu"),!0):!1} 
function cG(a,b){const c=N(Lx).g(fw.g,fw.defaultValue);a=a?.g()||"";return c&&c.length!==0&&!c.includes(a.toString())?(Xu(b,"pflna"),!1):!0}function dG(a,b){const c={};for(let e=0;e<a.length;e++){var d=a[e];const f="autors-container-"+e.toString(),g=b.document.createElement("div");g.setAttribute("id",f);d=new ZF(b,d,new cv(g));d.init();c[f]=d}return c}function eG(a){const b=C(a,11)||!1;a=E(a,8)||"";return new YF(b,a)};var gG=(a,b)=>{const c=[];z(a,wu,18)&&c.push(2);b.ca&&c.push(0);if(b=z(a,nu,28))b=z(a,nu,28),b=F(b,1)==1;b&&c.push(1);if(b=z(a,zu,34))a=z(a,zu,34),b=C(a,3);b&&c.push(7);return c};var hG=a=>a.googlefc=a.googlefc||{},iG=a=>{a=a.googlefc=a.googlefc||{};return a.__fcusi=a.__fcusi||{}},jG=a=>{a=a.googlefc=a.googlefc||{};if(!a.getFloatingToolbarTranslatedMessages)return null;if(a=a.getFloatingToolbarTranslatedMessages()){var b=new ou;b=Qi(b,1,a.defaultFloatingToolbarToggleExpansionText);b=Qi(b,2,a.defaultFloatingToolbarTogglePrivacySettings);a=Qi(b,3,a.defaultFloatingToolbarDismissPrivacySettings);a=Gh(a)}else a=null;return a};function kG(a,b){b=b.filter(c=>z(c,Lt,4)?.g()===5&&wg(y(c,8))===1);b=cz(b,a);a=Kz(b,a);a.sort((c,d)=>d.na.g-c.na.g);return a[0]||null};function lG({Eg:a,Ff:b,ig:c,Fg:d,Gf:e,jg:f}){const g=[];for(let n=0;n<f;n++)for(let p=0;p<c;p++){var h=p,k=c-1,l=n,m=f-1;g.push({x:a+(k===0?0:h/k)*(b-a),y:d+(m===0?0:l/m)*(e-d)})}return g}function mG(a,b){a.hasOwnProperty("_goog_efp_called_")||(a._goog_efp_called_=a.elementFromPoint(b.x,b.y));return a.elementFromPoint(b.x,b.y)};function nG(a,b){var c=lG({Eg:b.left,Ff:b.right,ig:10,Fg:b.top,Gf:b.bottom,jg:10});b=new Set;for(const d of c)(c=oG(a,d))&&b.add(c);return b}function pG(a,b,c=!1){for(const d of b)if((b=oG(a,d))&&!b.hasAttribute("google-allow-overlap")){if(c){const e=b.getBoundingClientRect();if(e.width>=a.L.innerWidth&&e.height>=a.L.innerHeight)continue}return b}return null}function qG(a,b,c=!1){return pG(a,b,c)!=null} 
function rG(a,b,c){if(ym(b,"position")!=="fixed")return null;var d=b.getAttribute("class")==="GoogleActiveViewInnerContainer"||Bm(b).width<=1&&Bm(b).height<=1||a.g.Bj&&!a.g.Bj(b)?!0:!1;a.g.qh&&a.g.qh(b,c,d);return d?null:b}function oG(a,b){var c=mG(a.L.document,b);if(c){var d;if(!(d=rG(a,c,b)))a:{d=a.L.document;for(c=c.offsetParent;c&&c!==d.body;c=c.offsetParent){const e=rG(a,c,b);if(e){d=e;break a}}d=null}a=d||null}else a=null;return a}var sG=class{constructor(a,b={}){this.L=a;this.g=b}};var tG=class{constructor(a,b,c){this.position=a;this.Xb=b;this.Mf=c}};function uG(a,b){this.start=a<b?a:b;this.end=a<b?b:a};function vG(a,b,c){var d=Cr(a);d=new tG(b.Ec.Ph(b.Eb),b.Xb+2*b.Eb,Math.min(d,b.ke)-b.Ec.Wd()+2*b.Eb);d=d.position.fh(a,d.Xb,d.Mf);var e=Br(a),f=Cr(a);c=wG(a,new Pl(Ll(d.top,0,f-1),Ll(d.right,0,e-1),Ll(d.bottom,0,f-1),Ll(d.left,0,e-1)),c);f=xG(c);let g=d.top;e=[];for(let h=0;h<f.length;h++)f[h].start>g&&e.push(new uG(g,f[h].start)),g=f[h].end;g<d.bottom&&e.push(new uG(g,d.bottom));a=Cr(a);d=[];for(f=e.length-1;f>=0;f--)d.push(new uG(a-e[f].end,a-e[f].start));a:{for(const h of d)if(a=h.start+b.Eb,a> 
b.Ec.Wd()+b.ag?a=null:(d=Math.min(h.end-b.Eb,b.ke)-a,a=d<b.fg?null:{position:b.Ec.xi(a),jd:d}),a){b=a;break a}b=null}return{hf:b,gp:c}}function wG(a,b,c){const d=nG(new sG(a),b);c.forEach(e=>void d.delete(e));return d}function xG(a){return[...a].map(yG).sort((b,c)=>b.start-c.start)}function yG(a){a=a.getBoundingClientRect();return new uG(a.top,a.bottom)};function zG({ha:a,Da:b}){return new AG(a,b)}var AG=class{constructor(a,b){this.ha=a;this.Da=b}Ph(a){return new AG(this.ha-a,this.Da-a)}fh(a,b,c){a=Cr(a)-this.ha-c;return new Pl(a,this.Da+b,a+c,this.Da)}Ug(a){a.bottom=`${this.ha}px`;a.left=`${this.Da}px`;a.right=""}th(){return 0}Wd(){return this.ha}xi(a){return new AG(a,this.Da)}};function BG({ha:a,Ua:b}){return new CG(a,b)} 
var CG=class{constructor(a,b){this.ha=a;this.Ua=b}Ph(a){return new CG(this.ha-a,this.Ua-a)}fh(a,b,c){var d=Br(a);a=Cr(a)-this.ha-c;d=d-this.Ua-b;return new Pl(a,d+b,a+c,d)}Ug(a){a.bottom=`${this.ha}px`;a.right=`${this.Ua}px`;a.left=""}th(){return 1}Wd(){return this.ha}xi(a){return new CG(a,this.Ua)}};function DG(a){var b={};const c=a.Hj,d=a.lj,e=a.dj,f=a.fl,g=a.ej;a=a.cj;b=b&&b.Oc;return CD('<link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Google+Symbols:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-50..200"'+(b?' nonce="'+X(aE(b))+'"':"")+'/><link href="https://fonts.googleapis.com/css?family=Google+Sans+Text:400,500,700" rel="stylesheet"'+(b?' nonce="'+X(aE(b))+'"':"")+"><style"+(b?' nonce="'+X(aE(b))+'"':"")+">.ft-styless-button {border: none; background: none; user-select: none; cursor: pointer; border-radius: "+ 
Z(16)+"px;}.ft-container {position: fixed;}.ft-menu {position: absolute; bottom: 0; display: flex; flex-direction: column; justify-content: center; align-items: center; box-shadow: 0 4px 8px 3px rgba(60, 64, 67, 0.15), 0 1px 3px rgba(60, 64, 67, 0.3); min-height: "+Z(e)+"px;}.ft-menu:not(.ft-multiple-buttons *) {transition: padding 0.25s 0.25s, margin 0.25s 0.25s, border-radius 0.25s 0.25s, background-color 0s 0.5s; padding: 0; margin: "+Z(a)+"px; border-radius: "+Z(16)+"px; background-color: rgba(255, 255, 255, 0);}.ft-multiple-buttons .ft-menu {transition: margin 0.25s, padding 0.25s, border-radius 0.25s 0.25s, background-color 0s; padding: "+ 
Z(a)+"px; margin: 0; border-radius: "+Z(16+a)+"px; background-color: rgba(255, 255, 255, 1);}.ft-left-pos .ft-menu {left: 0;}.ft-right-pos .ft-menu {right: 0;}.ft-container.ft-hidden {transition: opacity 0.25s, visibility 0.5s 0s; opacity: 0; visibility: hidden;}.ft-container:not(.ft-hidden) {transition: opacity 0.25s, bottom 0.5s ease; opacity: 1;}.google-symbols {font-size: 26px; color: #3c4043;}.ft-button-holder {display: flex; flex-direction: column; justify-content: center; align-items: center; padding: 0;}.ft-flip-vertically {transform: scaleY(-1);}.ft-expand-toggle {width: "+ 
Z(e)+"px; height: "+Z(e)+"px;}.ft-collapsed .ft-expand-icon {transition: transform 0.25s; transform: rotate(180deg);}.ft-expand-icon:not(.ft-collapsed *) {transition: transform 0.25s; transform: rotate(0deg);}.ft-button {position: relative; height: "+Z(e)+"px; margin-bottom: "+Z(g)+"px; transform: margin 0.25s 0.25s;}.ft-button.ft-last-button {margin-bottom: 0;}.ft-button > button {position: relative; height: "+Z(e)+"px; width: "+Z(e)+"px; margin: 0; padding: 0; border: none;}.ft-button > button > * {position: relative;}.ft-button .ft-highlighter {position: absolute; left: 50%; top: 50%; transform: translate(-50%, -50%); height: "+ 
Z(e-6)+"px; width: "+Z(e-6)+"px; border-radius: "+Z(e/2)+"px; background-color: #d2e3fc; opacity: 0; transition: opacity 0.25s;}.ft-button.ft-highlighted .ft-highlighter {opacity: 1;}.ft-button-corner-info {display: none;}.ft-button.ft-show-corner-info .ft-button-corner-info {position: absolute; left: -5px; top: 4px; background: #b3261e; border: 1.5px solid #ffffff; box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15); border-radius: 100px; color: ffffff; font-family: 'Google Sans Text'; font-style: normal; font-weight: 700; font-size: 11px; line-height: 14px; min-width: 16px; height: 16px; display: flex; flex-direction: row; justify-content: center; align-items: center;}.ft-separator {display: block; width: 100%; height: "+ 
Z(f)+"px;}.ft-separator > span {display: block; width: 28px; margin: 0 auto 10px auto; height: 0; border-bottom: 1px solid #dadce0;}.ft-expand-toggle-container {height: "+Z(e)+"px;}.ft-hidden {transition: opacity 0.25s, visibility 0.5s 0s; opacity: 0; visibility: hidden;}:not(.ft-hidden) {transition: opacity 0.25s; opacity: 1;}.ft-collapsed .ft-collapsible, .ft-collapsible.ft-collapsed, .ft-expand-toggle-container.ft-collapsed {transition: opacity 0.25s, margin 0.25s 0.25s, height 0.25s 0.25s, overflow 0.25s 0s, visibility 1s 0s; height: 0; opacity: 0; overflow: hidden; visibility: hidden; margin: 0;}.ft-collapsible:not(.ft-collapsed *):not(.ft-collapsed), .ft-expand-toggle-container:not(.ft-collapsed) {transition: margin 0.25s, height 0.25s, opacity 0.25s 0.25s; opacity: 1;}.ft-symbol-font-load-test {position: fixed; left: -1000px; top: -1000px; font-size: 26px; visibility: hidden;}.ft-reg-bubble {position: absolute; bottom: 0; padding: 10px 10px 0 10px; background: #fff; box-shadow: 0 4px 8px 3px rgba(60, 64, 67, 0.15), 0 1px 3px rgba(60, 64, 67, 0.3); border-radius: "+ 
Z(16)+"px; max-width: calc(90vw - "+Z(e*2)+"px); width: 300px; height: 200px;}.ft-left-pos .ft-reg-bubble {left: "+Z(e+10+a)+"px;}.ft-right-pos .ft-reg-bubble {right: "+Z(e+10+a)+"px;}.ft-collapsed .ft-reg-bubble, .ft-reg-bubble.ft-collapsed {transition: width 0.25s ease-in 0.25s, height 0.25s ease-in 0.25s, opacity 0.05s linear 0.45s, overflow 0s 0.25s, visibility 0s 0.5s; width: 0; overflow: hidden; opacity: 0; visibility: hidden;}.ft-collapsed .ft-reg-bubble, .ft-reg-bubble.ft-no-messages {height: 0 !important;}.ft-reg-bubble:not(.ft-collapsed *):not(.ft-collapsed) {transition: width 0.25s ease-out, height 0.25s ease-out, opacity 0.05s linear;}.ft-reg-bubble-content {display: flex; flex-direction: row; max-width: calc(90vw - "+ 
Z(e*2)+'px); width: 300px;}.ft-collapsed .ft-reg-bubble-content {transition: opacity 0.25s; opacity: 0;}.ft-reg-bubble-content:not(.ft-collapsed *) {transition: opacity 0.25s 0.25s; opacity: 1;}.ft-reg-message-holder {flex-grow: 1; display: flex; flex-direction: column; height: auto;}.ft-reg-controls {flex-grow: 0; padding-left: 5px;}.ft-reg-bubble-close-icon {font-size: 16px;}.ft-reg-message {font-family: \'Google Sans Text\'; font-style: normal; font-weight: 400; font-size: 12px; line-height: 14px; padding-bottom: 5px; margin-bottom: 5px; border-bottom: 1px solid #dadce0;}.ft-reg-message:last-of-type {border-bottom: none;}.ft-reg-message-button {border: none; background: none; font-family: \'Google Sans Text\'; color: #0b57d0; font-weight: 500; font-size: 14px; line-height: 22px; cursor: pointer; margin: 0; padding: 0; text-align: start;}.ft-display-none {display: none;}</style><toolbar id="ft-floating-toolbar" class="ft-container ft-hidden"><div class="ft-menu"><div class="ft-button-holder"></div><div class="ft-separator ft-collapsible ft-collapsed"><span></span></div><div class="ft-bottom-button-holder"></div><div class="ft-expand-toggle-container"><button class="ft-expand-toggle ft-styless-button" aria-controls="ft-floating-toolbar" aria-label="'+ 
X(c)+'"><span class="google-symbols ft-expand-icon" aria-hidden="true">expand_more</span></button></div></div><div id="ft-reg-bubble" class="ft-reg-bubble ft-collapsed ft-no-messages"><div class="ft-reg-bubble-content"><div class="ft-reg-message-holder"></div><div class="ft-reg-controls"><button class="ft-reg-bubble-close ft-styless-button" aria-controls="ft-reg-bubble" aria-label="'+X(d)+'"><span class="google-symbols ft-reg-bubble-close-icon" aria-hidden="true">close</span></button></div></div></div></toolbar><span inert class="ft-symbol-font-load-test"><span class="ft-symbol-reference google-symbols" aria-hidden="true">keyboard_double_arrow_right</span><span class="ft-text-reference" aria-hidden="true">keyboard_double_arrow_right</span></span>')} 
function EG(a){const b=a.googleIconName,c=a.backgroundColorCss,d=a.iconColorCss;return CD('<div class="ft-button ft-collapsible ft-collapsed ft-last-button"><button class="ft-styless-button" aria-label="'+X(a.ariaLabel)+'" style="background-color: '+X(Z(c))+'"><span class="ft-highlighter"></span><span class="google-symbols" style="color: '+X(Z(d))+'" aria-hidden="true">'+AD(b)+'</span></button><span class="ft-button-corner-info"></span></div>')};const FG=["Google Symbols:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-50..200","Google Sans Text:400,500,700"];function GG(a,b){a=new HG(a,b,IG(a,b));a.init();return a}function JG(){({Lc:a}={Lc:2});var a;return a>1?50:120}function KG(a,b,c){LG(a)===0&&b.classList.remove("ft-collapsed");MG(b,c);cs(a.B);b.classList.remove("ft-collapsed");NG(a);return()=>void OG(a,b,c)} 
function PG(a){QG(a.i.ja.kd).length===0?(a.l.O?.Uk(),a.l.g(null),a.i.ja.Lf.g(!1),a.i.ja.Eh.g(!1),a.i.ja.Wf.g(!1)):(a.i.ja.Lf.g(!0),RG(a))}function SG(a,{Ki:b=0,fp:c=0}){b=Math.max(QG(a.i.ec).length+b,0);c=Math.max(QG(a.i.Db).length+c,0);const d=b+c;let e=d*50;b>0&&c>0&&(e+=11);e+=Math.max(0,d-1)*10;d>=a.j.Lc&&(e+=60);d>1&&(e+=10);return e}function LG(a){const b=a.i.Db;return QG(a.i.ec).length+QG(b).length} 
function NG(a){const b=a.i.Db,c=a.i.separator;QG(a.i.ec).length>0&&QG(b).length>0?c.classList.remove("ft-collapsed"):c.classList.add("ft-collapsed");LG(a)>=a.j.Lc?a.i.Dh.g(!0):a.i.Dh.g(!1);LG(a)>1?a.i.xh.g(!0):a.i.xh.g(!1);LG(a)>0?a.i.isVisible.g(!0):a.i.isVisible.g(!1);TG(a);UG(a)}function OG(a,b,c){b.classList.contains("ft-removing")||(b.classList.add("ft-removing"),b.classList.add("ft-collapsed"),NG(a),a.B.setTimeout(()=>{c.removeChild(b)},750))} 
function TG(a){const b=QG(a.i.ec).concat(QG(a.i.Db));b.forEach(c=>{c.classList.remove("ft-last-button")});LG(a)>=a.j.Lc||b[b.length-1]?.classList.add("ft-last-button")}function UG(a){const b=QG(a.i.ec).concat(QG(a.i.Db)).filter(c=>!c.classList.contains("ft-reg-button"));a.G.g(b.length>0)}function VG(a){Qr(a.i.ja.kd.children,b=>{const c=a.i.ja.qd;OG(a,b,a.i.ja.kd);const d=c.get(b);c.delete(b);d?.isDismissed.g(!0)});PG(a)} 
function RG(a){if(!a.l.O){var b=WG(a.B,{googleIconName:"verified_user",ariaLabel:E(a.j.gb,2),orderingIndex:0,onClick:()=>{a.i.ja.Eh.g(!a.i.ja.isVisible.O);for(const [,c]of a.i.ja.qd)c.Hh=!0;a.i.ja.Wf.g(!1)},backgroundColorCss:"#fff"});b.Gd.classList.add("ft-reg-button");KG(a,b.Gd,a.i.Db);ss(b.jk,a.i.ja.isVisible);a.l.g({kp:b,Uk:()=>void OG(a,b.Gd,a.i.Db)})}}function XG(a){var b=a.i.ja.Wf,c=b.g;a:{for([,d]of a.i.ja.qd)if(a=d,a.showUnlessUserInControl&&!a.Hh){var d=!0;break a}d=!1}c.call(b,d)} 
function YG(a){a.i.ja.kj.listen(()=>{VG(a)})} 
var HG=class extends Q{constructor(a,b,c){super();this.B=a;this.j=b;this.i=c;this.l=new R(null);this.G=new R(!1)}addButton(a){a=WG(this.B,a);return KG(this,a.Gd,this.i.ec)}addRegulatoryMessage(a){const b=this.i.ja.kd,c=ZG(this.B,a);MG(c.dg,b);this.i.ja.qd.set(c.dg,c);PG(this);return{showUnlessUserInControl:()=>{c.showUnlessUserInControl=!0;XG(this)},hideUnlessUserInControl:()=>{c.showUnlessUserInControl=!1;XG(this)},isDismissed:us(c.isDismissed),removeCallback:()=>{var d=c.dg;const e=this.i.ja.kd; 
d.parentNode===e&&e.removeChild(d);this.i.ja.qd.delete(d);PG(this)}}}J(){return ms(this.l.map(a=>a!=null))}D(){return ms(this.G)}C(){return[this.i.container]}g(){const a=this.i.xb.wb;a.parentNode?.removeChild(a);super.g()}init(){ct(this.B,FG);ss(this.i.Cl,this.j.Jb);this.B.document.body.appendChild(this.i.xb.wb);YG(this)}}; 
function IG(a,b){const c=ME(a),d=c.shadowRoot;d.appendChild(sm(new fm(a.document),xD(DG({Hj:E(b.gb,1),lj:E(b.gb,3),dj:50,fl:11,ej:10,cj:5}))));const e=LE("ft-container",d),f=LE("ft-expand-toggle",d),g=LE("ft-expand-toggle-container",d),h=new R(null);h.i(p=>{e.style.zIndex=String(p??2147483647)});const k=new R(!0);ps(k,!0,()=>{e.classList.remove("ft-collapsed");f.setAttribute("aria-expanded","true")});ps(k,!1,()=>{e.classList.add("ft-collapsed");f.setAttribute("aria-expanded","false")});f.addEventListener("click", 
()=>{k.g(!k.O)});const l=new R(!1);ps(l,!0,()=>{g.classList.remove("ft-collapsed");e.classList.add("ft-toolbar-collapsible")});ps(l,!1,()=>{g.classList.add("ft-collapsed");e.classList.remove("ft-toolbar-collapsible");k.g(!0)});const m=new R(!1);ps(m,!0,()=>{e.classList.add("ft-multiple-buttons")});ps(m,!1,()=>{e.classList.remove("ft-multiple-buttons")});b.position.i(p=>{if(p){p.Ug(e.style);p=p.th();switch(p){case 0:e.classList.add("ft-left-pos");e.classList.remove("ft-right-pos");break;case 1:e.classList.add("ft-right-pos"); 
e.classList.remove("ft-left-pos");break;default:throw Error(`Unknown HorizontalAnchoring: ${p}`);}cs(a)}});const n=new R(!1);b=ls($G(a,d),n,b.position.map(p=>p!==null));ps(b,!0,()=>{e.classList.remove("ft-hidden")});ps(b,!1,()=>{e.classList.add("ft-hidden")});b=aH(a,LE("ft-reg-bubble",d));return{container:e,ec:LE("ft-button-holder",d),Db:LE("ft-bottom-button-holder",d),separator:LE("ft-separator",d),xb:c,Cl:h,np:k,Dh:l,xh:m,isVisible:n,ja:b}} 
function aH(a,b){const c=new R(!1),d=new R(!1),e=ns(c,d);ps(e,!0,()=>{b.classList.remove("ft-collapsed")});ps(e,!1,()=>{b.classList.add("ft-collapsed")});const f=new R(!1);ps(f,!0,()=>{b.classList.remove("ft-no-messages")});ps(f,!1,()=>{b.classList.add("ft-no-messages")});const g=LE("ft-reg-bubble-close",b),h=new ys;g.addEventListener("click",()=>{xs(h)});const k=LE("ft-reg-message-holder",b);Ws(Ts(a,k)).i(()=>{b.style.height=`${k.offsetHeight}px`});return{kd:k,Eh:c,Wf:d,isVisible:e,Lf:f,qd:new Map, 
kj:vs(h)}} 
function WG(a,b){const c=sm(new fm(a.document),xD(EG({googleIconName:b.googleIconName,ariaLabel:b.ariaLabel,backgroundColorCss:b.backgroundColorCss||"#e2eaf6",iconColorCss:b.iconColorCss||"#3c4043"})));b.buttonExtension?.styleSheet&&c.appendChild(b.buttonExtension.styleSheet);if(b.cornerNumber!==void 0){const d=Ll(Math.round(b.cornerNumber),0,99);LE("ft-button-corner-info",c).appendChild(a.document.createTextNode(String(d)));c.classList.add("ft-show-corner-info")}c.orderingIndex=b.orderingIndex;b.onClick&& 
KE("BUTTON",c).addEventListener("click",b.onClick);a=new R(!1);ps(a,!0,()=>{c.classList.add("ft-highlighted")});ps(a,!1,()=>{c.classList.remove("ft-highlighted")});return{Gd:c,jk:a}} 
function ZG(a,b){a=new fm(a.document);var c=CD('<div class="ft-reg-message"><button class="ft-reg-message-button"></button><div class="ft-reg-message-info"></div></div>');a=sm(a,xD(c));c=LE("ft-reg-message-button",a);b.regulatoryMessage.actionButton?(c.appendChild(b.regulatoryMessage.actionButton.buttonText),c.addEventListener("click",b.regulatoryMessage.actionButton.onClick)):c.classList.add("ft-display-none");c=LE("ft-reg-message-info",a);b.regulatoryMessage.informationText?c.appendChild(b.regulatoryMessage.informationText): 
c.classList.add("ft-display-none");a.orderingIndex=b.orderingIndex;return{dg:a,showUnlessUserInControl:!1,Hh:!1,isDismissed:new R(!1)}}function MG(a,b){a:{var c=Array.from(b.children);for(let d=0;d<c.length;++d)if(c[d].orderingIndex>=a.orderingIndex){c=d;break a}c=c.length}b.insertBefore(a,b.childNodes[c]||null)}function QG(a){return Array.from(a.children).filter(b=>!b.classList.contains("ft-removing"))} 
function $G(a,b){const c=new R(!1),d=LE("ft-symbol-font-load-test",b);b=LE("ft-symbol-reference",d);const e=LE("ft-text-reference",d),f=Ts(a,b);qs(Ws(f).map(g=>g.width>0&&g.width<e.offsetWidth/2),!0,()=>{c.g(!0);d.parentNode?.removeChild(d);f.dispose()});return c};function bH(a){const b=new ys,c=Js(a,2500,()=>void xs(b));return new cH(a,()=>void dH(a,()=>void c()),vs(b))}function eH(a){const b=new MutationObserver(()=>{a.i()});b.observe(a.B.document.documentElement,{childList:!0,subtree:!0,attributes:!0,attributeFilter:["class","style"]});fs(a,()=>void b.disconnect())}function fH(a){a.B.addEventListener("resize",a.i);fs(a,()=>void a.B.removeEventListener("resize",a.i))}var cH=class extends Q{constructor(a,b,c){super();this.B=a;this.i=b;this.l=c;this.j=!1}}; 
function dH(a,b){b();a.setTimeout(b,1500)};function gH(a){return a.g[a.g.length-1]}var iH=class{constructor(){this.j=hH;this.g=[];this.i=new Set}add(a){if(this.i.has(a))return!1;const b=hb(this.g,a,this.j);this.g.splice(b>=0?b:-b-1,0,a);this.i.add(a);return!0}first(){return this.g[0]}has(a){return this.i.has(a)}delete(a){ab(this.g,b=>b===a);return this.i.delete(a)}clear(){this.i.clear();return this.g.splice(0,this.g.length)}size(){return this.g.length}};function jH(a){var b=a.jd.O;let c;for(;a.j.oj()>b&&(c=a.i.first());){var d=a,e=c;kH(d,e);d.g.add(e)}for(;(d=gH(a.g))&&a.j.Wj()<=b;)lH(a,d);for(;(d=gH(a.g))&&(c=a.i.first())&&d.priority>c.priority;)b=a,e=c,kH(b,e),b.g.add(e),lH(a,d)}function lH(a,b){a.g.delete(b);a.i.add(b)&&(b.Jg=a.j.addButton(b.buttonSpec));b.isInToolbar.g(!0)}function kH(a,b){b.Jg&&b.Jg();b.Jg=void 0;a.i.delete(b);b.isInToolbar.g(!1)} 
var mH=class{constructor(a,b){this.jd=a;this.j=b;this.g=new iH;this.i=new iH;this.A=0;this.jd.listen(()=>void jH(this))}addButton(a){const b={buttonSpec:a.buttonSpec,priority:a.priority,Lg:this.A++,isInToolbar:new R(!1)};this.g.add(b);jH(this);return{isInToolbar:us(ms(b.isInToolbar)),removeCallback:()=>{kH(this,b);this.g.delete(b);jH(this)}}}};function hH(a,b){return a.priority===b.priority?b.Lg-a.Lg:a.priority-b.priority};function nH(a){if(!IE(a.B)){if(a.j.O){const b=Lr(a.B);if(b>a.i+100||b<a.i-100)a.j.g(!1),a.i=Er(a.B)}a.l&&a.B.clearTimeout(a.l);a.l=a.B.setTimeout(()=>void oH(a),200)}}function oH(a){if(!IE(a.B)){var b=Er(a.B);a.i&&a.i>b&&(a.i=b);b=Lr(a.B);b>=a.i-100&&(a.i=Math.max(a.i,b),a.j.g(!0))}} 
var pH=class extends Q{constructor(a){super();this.B=a;this.j=new R(!1);this.i=0;this.l=null;this.C=()=>void nH(this)}init(){this.B.addEventListener("scroll",this.C);this.i=Er(this.B);oH(this)}g(){this.B.removeEventListener("scroll",this.C);this.j.g(!1);super.g()}};function qH(a){if(!a.i){var b=new pH(a.B);b.init();a.i=ms(b.j);es(a,b)}return a.i}function rH(a,b,c){const d=a.j.addRegulatoryMessage(b.messageSpec);b.messageSpec.regulatoryMessage.disableFloatingToolbarAutoShow||sH(a,d,c);qs(c,!0,()=>{d.removeCallback()})}function sH(a,b,c){a=qH(a);const d=ps(a,!0,()=>void b.showUnlessUserInControl()),e=ps(a,!1,()=>void b.hideUnlessUserInControl());ps(js(b.isDismissed),!0,()=>{d();e()});qs(c,!0,()=>{d();e()})} 
var tH=class extends Q{constructor(a,b){super();this.B=a;this.j=b;this.i=null}addRegulatoryMessage(a){const b=new R(!1),c=qs(qH(this),!0,()=>{rH(this,a,b)});return{removeCallback:()=>{b.g(!0);c()}}}};function uH(a,b){a.googFloatingToolbarManager||(a.googFloatingToolbarManager=new vH(a,b));return a.googFloatingToolbarManager}function wH(a){a.i||(a.i=xH(a.B,a.j,a.Jb),es(a,a.i.lc),es(a,a.i.ci),yH(a),zH(a,a.i.lc));return a.i}function AH(a){a.Jb.O===null&&a.i?.position.g(BH(a))}function CH(a){a.B.requestAnimationFrame(()=>void AH(a))} 
function BH(a){var b=[];a.i?.lc?.D().l()?(b.push(()=>DH(a)),b.push(()=>EH(a))):(b.push(()=>EH(a)),b.push(()=>DH(a)));a.i?.lc?.J()?.l()&&b.push(()=>{const c=Cr(a.B);return{position:zG({ha:Math.floor(c/3),Da:10}),jd:0}});for(const c of b)if(b=c())return b;return null}function yH(a){a.B.googFloatingToolbarManagerAsyncPositionUpdate?CH(a):AH(a)} 
function zH(a,b){const c=bH(a.B);c.j||(eH(c),fH(c),c.j=!0);c.l.listen(()=>void yH(a));es(a,c);b.J().listen(()=>void yH(a));b.D().listen(()=>void yH(a));a.Jb.listen(()=>void yH(a))}function DH(a){var b=a.B;const c=Cr(a.B);return vG(b,{Ec:BG({ha:50,Ua:10}),ag:Math.floor(c/3),Xb:60,fg:JG(),ke:Math.floor(c/2),Eb:20},[...(a.i?.lc.C()??[]),a.B.document.body]).hf} 
function EH(a){var b=a.B;const c=Cr(a.B);return vG(b,{Ec:zG({ha:50,Da:10}),ag:Math.floor(c/3),Xb:60,fg:JG(),ke:Math.floor(c/2),Eb:40},[...(a.i?.lc.C()??[]),a.B.document.body]).hf}class vH extends Q{constructor(a,b){super();this.B=a;this.j=b;this.i=null;this.Jb=FH(this.B,this)}addButton(a){return wH(this).Bk.addButton(a)}addRegulatoryMessage(a){return wH(this).ci.addRegulatoryMessage(a)}} 
function xH(a,b,c){const d=new R(null),e=GG(a,{Lc:2,position:d.map(f=>f?.position??null),gb:b,Jb:c});b=new mH(d.map(f=>f?.jd||0),{addButton:f=>e.addButton(f),oj:()=>SG(e,{}),Wj:()=>SG(e,{Ki:1})});a=new tH(a,{addRegulatoryMessage:f=>e.addRegulatoryMessage(f)});return{lc:e,position:d,Bk:b,ci:a}}function FH(a,b){const c=new sE(a),d=new R(null),e=f=>void d.g(f);fs(b,()=>{rE(c,e)});c.floatingAdsStacking.maxZIndexListeners.push(e);e(qE(c));return d};const GH=["Google Symbols:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-50..200","Google Sans Text:400,500"]; 
function HH(a,b,c,d,e){a=new IH(a,b,c,d,e);if(a.l){ct(a.B,GH);var f=a.B;b=a.message;c=ME(f);e=c.shadowRoot;d=e.appendChild;f=new fm(f.document);var g=(g={},g.Oc);g=CD('<link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Google+Symbols:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-50..200"'+(g?' nonce="'+X(aE(g))+'"':"")+'/><link href="https://fonts.googleapis.com/css?family=Google+Sans+Text:400,500" rel="stylesheet"'+(g?' nonce="'+X(aE(g))+'"':"")+"><style"+(g?' nonce="'+X(aE(g))+'"': 
"")+'>.ipr-container {font-family: \'Google Sans Text\'; font-style: normal; font-weight: 400; font-size: 12px; line-height: 14px; color: #000; border-top: 2px solid rgb(236, 237, 237); border-bottom: 2px solid rgb(236, 237, 237); background-color: #fff; padding: 5px; margin: 5px 0; text-align: center;}.ipr-button {border: none; background: none; font-family: \'Google Sans Text\'; color: #0b57d0; font-weight: 500; font-size: 14px; line-height: 22px; cursor: pointer; margin: 0; padding: 0;}.ipr-display-none {display: none;}</style><div class="ipr-container"><button class="ipr-button"></button><div class="ipr-info"></div></div>'); 
d.call(e,sm(f,xD(g)));d=LE("ipr-container",e);e=LE("ipr-button",d);b.actionButton?(e.appendChild(b.actionButton.buttonText),e.addEventListener("click",b.actionButton.onClick)):e.classList.add("ipr-display-none");d=LE("ipr-info",d);b.informationText?d.appendChild(b.informationText):d.classList.add("ipr-display-none");a.i=c.wb;Iz(a.l,a.i);a.j&&a.j(ap(1));JH(a)}else KH(a);return a}function JH(a){const b=new ft(a.B);b.init(2E3);es(a,b);dt(b,()=>{LH(a);KH(a);b.dispose()})} 
function KH(a){const b=uH(a.B,a.C).addRegulatoryMessage({messageSpec:{regulatoryMessage:a.message,orderingIndex:0}});fs(a,()=>void b.removeCallback());a.j&&a.j(ap(2))}function LH(a){a.i&&(a.i.parentNode?.removeChild(a.i),a.i=null)}var IH=class extends Q{constructor(a,b,c,d,e){super();this.B=a;this.l=b;this.message=c;this.C=d;this.j=e;this.i=null}g(){LH(this);super.g()}};var NH=(a,b,c,d)=>MH(a,b,c,d);function MH(a,b,c,d){const e=HH(a,kG(a,d),{actionButton:{buttonText:a.document.createTextNode(b),onClick:c}},OH(a));return()=>e.dispose()}function OH(a){if(a=jG(a))return a;lC(1234,Error("No messages"));return Vi(new ou)};function PH(a,b){b&&(a.g=NH(a.i,b.localizedDnsText,()=>QH(a,b),a.A))}function RH(a){const b=hG(a.i);b.callbackQueue=b.callbackQueue||[];iG(a.i).overrideDnsLink=!0;b.callbackQueue.push({INITIAL_US_STATES_DATA_READY:c=>PH(a,c)})}function QH(a,b){tE(a.j);b.openConfirmationDialog(c=>{c&&a.g&&(a.g(),a.g=null);uE(a.j)})}var SH=class{constructor(a,b,c){this.i=a;this.j=nE(b,2147483643);this.A=c;this.g=null}};function TH(a){UH(a.j,b=>{var c=a.i,d=b.revocationText,e=b.attestationText,f=b.showRevocationMessage;b=kG(c,a.A);d={actionButton:{buttonText:c.document.createTextNode(d),onClick:f},informationText:c.document.createTextNode(e)};e=jG(c);e||(lC(1233,Error("No messages")),e=Vi(new ou));HH(c,b,d,e)},()=>{uE(a.g);VH(a)})}function WH(a){tE(a.g);TH(a)} 
function VH(a){a.i.__tcfapi?a.i.__tcfapi("addEventListener",2,(b,c)=>{c&&b.eventStatus=="cmpuishown"?tE(a.g):uE(a.g)}):lC(1250,Error("No TCF API function"))}var XH=class{constructor(a,b,c,d){this.i=a;this.g=nE(b,2147483643);this.A=c;this.j=d}};var YH=a=>{if(!a||wg(y(a,1))==null)return!1;a=F(a,1);switch(a){case 1:return!0;case 2:return!1;default:throw Error("Unhandled AutoConsentUiStatus: "+a);}},ZH=a=>{if(!a||wg(y(a,3))==null)return!1;a=F(a,3);switch(a){case 1:return!0;case 2:return!1;default:throw Error("Unhandled AutoCcpaUiStatus: "+a);}},$H=a=>a?C(a,5)===!0:!1;function aI(a){let b=a.location.href;if(a===a.top)return{url:b,Yf:!0};let c=!1;const d=a.document;d&&d.referrer&&(b=d.referrer,a.parent===a.top&&(c=!0));(a=a.location.ancestorOrigins)&&(a=a[a.length-1])&&b.indexOf(a)===-1&&(c=!1,b=a);return{url:b,Yf:c}};function bI(a,b){be(a,(c,d)=>{b[d]=c})}function cI(a){if(a===a.top)return 0;for(let b=a;b&&b!==b.top&&Ud(b);b=b.parent){if(a.sf_)return 2;if(a.$sf)return 3;if(a.inGptIF)return 4;if(a.inDapIF)return 5}return 1};function dI(){if(eI)return eI;var a=cm()||window;const b=a.google_persistent_state_async;return b!=null&&typeof b=="object"&&b.S!=null&&typeof b.S=="object"?eI=b:a.google_persistent_state_async=eI=new fI}function gI(a,b,c){b=hI[b]||`google_ps_${b}`;a=a.S;const d=a[b];return d===void 0?(a[b]=c(),a[b]):d}function iI(a,b,c){return gI(a,b,()=>c)}function jI(a,b,c){return a.S[hI[b]||`google_ps_${b}`]=c}function kI(a,b){return jI(a,b,iI(a,b,0)+1)}function lI(){var a=dI();return iI(a,20,{})} 
function mI(){var a=dI();const b=iI(a,41,!1);b||jI(a,41,!0);return!b}function nI(){var a=dI();return iI(a,26)}function oI(){var a=dI();return iI(a,28,[])}var fI=class{constructor(){this.S={}}},eI=null;const hI={[8]:"google_prev_ad_formats_by_region",[9]:"google_prev_ad_slotnames_by_region"};function pI(a){return a.google_ad_modifications=a.google_ad_modifications||{}}function qI(a,b){a=pI(a);a.processed_sra_frame_pingbacks=a.processed_sra_frame_pingbacks||{};const c=!a.processed_sra_frame_pingbacks[b];a.processed_sra_frame_pingbacks[b]=!0;return c};function fr(a,b){a.i.size>0||rI(a);const c=a.i.get(0);c?c.push(b):a.i.set(0,[b])}function sI(a,b,c,d){Lk(b,c,d);fs(a,()=>Mk(b,c,d))}function tI(a,b){a.j!==1&&(a.j=1,a.i.size>0&&uI(a,b))}function rI(a){a.B.document.visibilityState?sI(a,a.B.document,"visibilitychange",b=>{a.B.document.visibilityState==="hidden"&&tI(a,b);a.B.document.visibilityState==="visible"&&(a.j=0)}):"onpagehide"in a.B?(sI(a,a.B,"pagehide",b=>{tI(a,b)}),sI(a,a.B,"pageshow",()=>{a.j=0})):sI(a,a.B,"beforeunload",b=>{tI(a,b)})} 
function uI(a,b){for(let c=9;c>=0;c--)a.i.get(c)?.forEach(d=>{d(b)})}var vI=class extends Q{constructor(a){super();this.B=a;this.j=0;this.i=new Map}};async function wI(a,b){var c=10;return c<=0?Promise.reject(Error(`wfc bad input ${c} ${200}`)):b()?Promise.resolve():new Promise((d,e)=>{const f=a.setInterval(()=>{--c?b()&&(a.clearInterval(f),d()):(a.clearInterval(f),e(Error(`wfc timed out ${c}`)))},200)})};function xI(a){const b=a.g.pc;return b!==null&&b!==0?b:a.g.pc=Ge(a.B)}function yI(a){var b=a.g.wpc;if(b===null||b==="")b=a.g,a=a.B,a=a.google_ad_client?String(a.google_ad_client):pI(a).head_tag_slot_vars?.google_ad_client??a.document.querySelector(".adsbygoogle[data-ad-client]")?.getAttribute("data-ad-client")??"",b=b.wpc=a;return b}function zI(a,b){var c=new Rp;var d=xI(a);c=Pi(c,1,d);d=yI(a);c=Ri(c,2,d);c=Qp(c,a.g.sd);return Pi(c,7,Math.round(b||a.B.performance.now()))} 
function AI(a,b,c){b(a.I.se.Ne.Cd).Ha(c)}function BI(a,b,c){b(a.I.se.Ne.Cd).pd(c)}async function CI(a){q(await q(wI(a.B,()=>!(!xI(a)||!yI(a)))))}function DI(){var a=N(EI);a.i&&(a.g.tar+=1)}function FI(a){var b=N(EI);if(b.i){var c=b.A;a(c);b.g.cc=th(c)}}async function GI(a,b,c){if(a.i&&c.length&&!a.g.lgdp.includes(Number(b))){a.g.lgdp.push(Number(b));var d=a.B.performance.now();q(await q(CI(a)));var e=a.I,f=e.Rb;a=zI(a,d);d=new Po;b=G(d,1,b);c=ci(b,2,c,xg);c=oi(a,9,Sp,c);f.call(e,c)}} 
async function HI(a,b){q(await q(CI(a)));var c=zI(a);b=oi(c,5,Sp,b);a.i&&!a.g.le.includes(2)&&(a.g.le.push(2),a.I.Rb(b))}async function II(a,b,c){q(await q(CI(a)));var d=a.I,e=d.Rb;a=Qp(zI(a,c),1);b=oi(a,6,Sp,b);e.call(d,b)}async function JI(a,b,c){q(await q(CI(a)));AI(a,d=>b(d.ki),c)}async function KI(a,b,c){q(await q(CI(a)));BI(a,d=>b(d.ki),c)}async function LI(a,b){q(await q(CI(a)));var c=a.I,d=c.Rb;a=Qp(zI(a),1);b=oi(a,13,Sp,b);d.call(c,b)} 
async function MI(a,b){if(a.i){q(await q(CI(a)));var c=a.I,d=c.Rb;a=zI(a);b=oi(a,11,Sp,b);d.call(c,b)}}async function NI(a,b){q(await q(CI(a)));var c=a.I,d=c.Rb;a=Qp(zI(a),1);b=oi(a,16,Sp,b);d.call(c,b)} 
var EI=class{constructor(a,b){this.B=cm()||window;this.j=b??new vI(this.B);this.I=a??new hr(vq(),100,100,!0,this.j);this.g=gI(dI(),33,()=>{const c=W(vv);return{sd:c,ssp:c>0&&ae()<1/c,pc:null,wpc:null,cu:null,le:[],lgdp:[],psi:null,tar:0,cc:null}})}get i(){return this.g.ssp}get ic(){return this.g.cu}set ic(a){this.g.cu=a}get A(){return hC(1227,()=>Wi(Qo,uh(this.g.cc||[])))||new Qo}};var OI=class{constructor(a,b,c,d,e){this.i=a;this.j=b;this.g=c;this.l=d;this.A=e||null}run(){if(this.i.adsbygoogle_ama_fc_has_run!==!0){var a=YH(this.g),b=ZH(this.g),c=!1;a&&(WH(new XH(this.i,this.l,this.A||mi(this.g,vu,4,Th()),this.j)),c=!0);b&&(RH(new SH(this.i,this.l,this.A||mi(this.g,vu,4,Th()))),c=!0);FI(d=>{d=Mi(d,9,!0);d=Mi(d,10,a);Mi(d,11,b)});$H(this.g)&&(c=!0);c&&(this.j.start(!0),this.i.adsbygoogle_ama_fc_has_run=!0)}}};function PI(a,b,c,d,e,f){try{const g=a.g,h=Zd("SCRIPT",g);h.async=!0;fd(h,b);g.head.appendChild(h);h.addEventListener("load",()=>{e();d&&g.head.removeChild(h)});h.addEventListener("error",()=>{c>0?PI(a,b,c-1,d,e,f):(d&&g.head.removeChild(h),f())})}catch(g){f()}}function QI(a,b,c=()=>{},d=()=>{}){PI(em(a),b,0,!1,c,d)};function RI(a=null){a=a||r;return a.googlefc||(a.googlefc={})};Cc(sr).map(a=>Number(a));Cc(tr).map(a=>Number(a));const SI=r.URL;function TI(a){const b=c=>encodeURIComponent(c).replace(/[!()~']|(%20)/g,d=>({"!":"%21","(":"%28",")":"%29","%20":"+","'":"%27","~":"%7E"})[d]);return Array.from(a,c=>b(c[0])+"="+b(c[1])).join("&")};function UI(a){var b=(new SI(a.location.href)).searchParams;a=b.get("fcconsent");b=b.get("fc");return b==="alwaysshow"?b:a==="alwaysshow"?a:null}function VI(a){const b=["ab","gdpr","consent","ccpa","monetization"];return(a=(new SI(a.location.href)).searchParams.get("fctype"))&&b.indexOf(a)!==-1?a:null} 
function WI(a){var b=new SI(a),c={search:"",hash:""};a={};b&&(a.protocol=b.protocol,a.username=b.username,a.password=b.password,a.hostname=b.hostname,a.port=b.port,a.pathname=b.pathname,a.search=b.search,a.hash=b.hash);Object.assign(a,c);if(a.port&&a.port[0]===":")throw Error("port should not start with ':'");a.hash&&a.hash[0]!="#"&&(a.hash="#"+a.hash);c.search?c.search[0]!="?"&&(a.search="?"+c.search):c.searchParams&&(a.search="?"+TI(c.searchParams),a.searchParams=void 0);b="";a.protocol&&(b+=a.protocol+ 
"//");c=a.username;var d=a.password;b=b+(c&&d?c+":"+d+"@":c?c+"@":d?":"+d+"@":"")+(a.hostname||"");a.port&&(b+=":"+a.port);b+=a.pathname||"";b+=a.search||"";b+=a.hash||"";a=(new SI(b)).toString();a.charAt(a.length-1)==="/"&&(a=a.substring(0,a.length-1));return a.toString().length<=1E3?a:null};function XI(a,b){const c=a.document,d=()=>{if(!a.frames[b])if(c.body){const e=Zd("IFRAME",c);e.style.display="none";e.style.width="0px";e.style.height="0px";e.style.border="none";e.style.zIndex="-1000";e.style.left="-1000px";e.style.top="-1000px";e.name=b;c.body.appendChild(e)}else a.setTimeout(d,5)};d()};var YI=Hk(class extends H{});function ZI(a){if(a.i)return a.i;a.M&&a.M(a.j)?a.i=a.j:a.i=ue(a.j,a.V);return a.i??null}function $I(a){a.l||(a.l=b=>{try{var c=a.J?a.J(b):void 0;if(c){var d=c.og,e=a.G.get(d);e&&(e.Nk||a.G.delete(d),e.Ac?.(e.sj,c.payload))}}catch(f){}},Lk(a.j,"message",a.l))}function aJ(a,b,c){if(ZI(a))if(a.i===a.j)(b=a.D.get(b))&&b(a.i,c);else{var d=a.C.get(b);if(d&&d.hd){$I(a);var e=++a.X;a.G.set(e,{Ac:d.Ac,sj:d.ge(c),Nk:b==="addEventListener"});a.i.postMessage(d.hd(c,e),"*")}}} 
var bJ=class extends Q{constructor(a,b,c,d){super();this.V=b;this.M=c;this.J=d;this.D=new Map;this.X=0;this.C=new Map;this.G=new Map;this.l=void 0;this.j=a}g(){delete this.i;this.D.clear();this.C.clear();this.G.clear();this.l&&(Mk(this.j,"message",this.l),delete this.l);delete this.j;delete this.J;super.g()}};const cJ=(a,b)=>{const c={cb:d=>{d=YI(d);b.pb({Nc:d})}};b.spsp&&(c.spsp=b.spsp);a=a.googlefc||(a.googlefc={});a.__fci=a.__fci||[];a.__fci.push(b.command,c)},dJ={ge:a=>a.pb,hd:(a,b)=>({__fciCall:{callId:b,command:a.command,spsp:a.spsp||void 0}}),Ac:(a,b)=>{a({Nc:b})}};function eJ(a){a=YI(a.data.__fciReturn);return{payload:a,og:Uu(vi(a,1))}}function fJ(a,b=!1){if(b)return!1;a.j||(a.i=!!ZI(a.caller),a.j=!0);return a.i} 
function gJ(a){return new Promise(b=>{fJ(a)&&aJ(a.caller,"getDataWithCallback",{command:"loaded",pb:c=>{b(c.Nc)}})})}function hJ(a,b){fJ(a)&&aJ(a.caller,"getDataWithCallback",{command:"prov",spsp:Ui(b),pb:()=>{}})}var iJ=class extends Q{constructor(a){super();this.i=this.j=!1;this.caller=new bJ(a,"googlefcPresent",void 0,eJ);this.caller.D.set("getDataWithCallback",cJ);this.caller.C.set("getDataWithCallback",dJ)}g(){this.caller.dispose();super.g()}};function jJ(a){a.addtlConsent!==void 0&&typeof a.addtlConsent!=="string"&&(a.addtlConsent=void 0);a.gdprApplies!==void 0&&typeof a.gdprApplies!=="boolean"&&(a.gdprApplies=void 0);return a.tcString!==void 0&&typeof a.tcString!=="string"||a.listenerId!==void 0&&typeof a.listenerId!=="number"?2:a.cmpStatus&&a.cmpStatus!=="error"?0:3} 
function kJ(a){if(a.gdprApplies===!1)return!0;a.internalErrorState===void 0&&(a.internalErrorState=jJ(a));return a.cmpStatus==="error"||a.internalErrorState!==0?a.internalBlockOnErrors?(zl({e:String(a.internalErrorState)},"tcfe"),!1):!0:a.cmpStatus!=="loaded"||a.eventStatus!=="tcloaded"&&a.eventStatus!=="useractioncomplete"?!1:!0} 
function lJ(a,b={}){return kJ(a)?a.gdprApplies===!1?!0:a.tcString==="tcunavailable"?!b.idpcApplies:(b.idpcApplies||a.gdprApplies!==void 0||b.lp)&&(b.idpcApplies||typeof a.tcString==="string"&&a.tcString.length)?mJ(a,"1",0):!0:!1} 
function mJ(a,b,c){a:{if(a.publisher&&a.publisher.restrictions){var d=a.publisher.restrictions[b];if(d!==void 0){d=d["755"];break a}}d=void 0}if(d===0)return!1;let e=c;c===2?(e=0,d===2&&(e=1)):c===3&&(e=1,d===1&&(e=0));if(e===0)a=a.purpose&&a.vendor?(c=nJ(a.vendor.consents,"755"))&&b==="1"&&a.purposeOneTreatment&&a.publisherCC==="CH"?!0:c&&nJ(a.purpose.consents,b):!0;else{var f;e===1?f=a.purpose&&a.vendor?nJ(a.purpose.legitimateInterests,b)&&nJ(a.vendor.legitimateInterests,"755"):!0:f=!0;a=f}return a} 
function nJ(a,b){return!(!a||!a[b])}function oJ(a,b,c){return a.gdprApplies===!1?!0:b.every(d=>mJ(a,d,c))}function pJ(a){if(a.i)return a.i;a.i=ue(a.j,"__tcfapiLocator");return a.i}function qJ(a){return typeof a.j.__tcfapi==="function"||pJ(a)!=null}function rJ(a,b,c,d){c||(c=()=>{});var e=a.j;typeof e.__tcfapi==="function"?(a=e.__tcfapi,a(b,2,c,d)):pJ(a)?(sJ(a),e=++a.J,a.D[e]=c,a.i&&a.i.postMessage({__tcfapiCall:{command:b,version:2,callId:e,parameter:d}},"*")):c({},!1)} 
function tJ(a,b){let c={internalErrorState:0,internalBlockOnErrors:a.C};const d=tc(()=>b(c));let e=0;a.G!==-1&&(e=setTimeout(()=>{e=0;c.tcString="tcunavailable";c.internalErrorState=1;d()},a.G));rJ(a,"addEventListener",f=>{f&&(c=f,c.internalErrorState=jJ(c),c.internalBlockOnErrors=a.C,kJ(c)?(c.internalErrorState!==0&&(c.tcString="tcunavailable"),rJ(a,"removeEventListener",null,c.listenerId),(f=e)&&clearTimeout(f),d()):(c.cmpStatus==="error"||c.internalErrorState!==0)&&(f=e)&&clearTimeout(f))})} 
function sJ(a){if(!a.l){var b=c=>{try{var d=(typeof c.data==="string"?JSON.parse(c.data):c.data).__tcfapiReturn;a.D[d.callId](d.returnValue,d.success)}catch(e){}};a.l=b;Lk(a.j,"message",b)}} 
var uJ=class extends Q{constructor(a,b={}){super();this.i=null;this.D={};this.J=0;this.l=null;this.j=a;this.G=b.timeoutMs??500;this.C=b.Zi??!1}g(){this.D={};this.l&&(Mk(this.j,"message",this.l),delete this.l);delete this.D;delete this.j;delete this.i;super.g()}addEventListener(a){let b={internalBlockOnErrors:this.C};const c=tc(()=>a(b));let d=0;this.G!==-1&&(d=setTimeout(()=>{b.tcString="tcunavailable";b.internalErrorState=1;c()},this.G));const e=(f,g)=>{clearTimeout(d);f?(b=f,b.internalErrorState= 
jJ(b),b.internalBlockOnErrors=this.C,g&&b.internalErrorState===0||(b.tcString="tcunavailable",g||(b.internalErrorState=3))):(b.tcString="tcunavailable",b.internalErrorState=3);a(b)};try{rJ(this,"addEventListener",e)}catch(f){b.tcString="tcunavailable",b.internalErrorState=3,d&&(clearTimeout(d),d=0),c()}}removeEventListener(a){a&&a.listenerId&&rJ(this,"removeEventListener",null,a.listenerId)}};function UH(a,b,c){const d=RI(a.B);d.callbackQueue=d.callbackQueue||[];d.callbackQueue.push({CONSENT_DATA_READY:()=>{const e=RI(a.B),f=new uJ(a.B);qJ(f)&&tJ(f,g=>{g.cmpId===300&&g.tcString&&g.tcString!=="tcunavailable"&&g.gdprApplies&&b({revocationText:(0,e.getDefaultConsentRevocationText)(),closeText:(0,e.getDefaultConsentRevocationCloseText)(),attestationText:(0,e.getDefaultConsentRevocationAttestationText)(),showRevocationMessage:()=>{(0,e.showRevocationMessage)()}})});c()}})} 
function vJ(a,b=!1,c){const d={};try{const f=UI(a.B),g=VI(a.B);d.fc=f;d.fctype=g}catch(f){}let e;try{e=WI(a.B.location.href)}catch(f){}b&&e&&(d.href=e);b=wJ(a.g,d);QI(a.B,b,()=>{},()=>{});c&&hJ(new iJ(a.B),c)}var xJ=class{constructor(a,b){this.B=a;this.g=b}start(a=!1,b){if(this.B===this.B.top)try{XI(this.B,"googlefcPresent"),vJ(this,a,b)}catch(c){}}};function wJ(a,b){a=Rd`https://fundingchoicesmessages.google.com/i/${a}`;return Sd(a,{...b,ers:2})};const yJ=new Set(["ARTICLE","SECTION"]);var zJ=class{constructor(a){this.g=a}};function AJ(a,b){return Array.from(b.classList).map(c=>`${a}=${c}`)}function BJ(a){return a.classList.length>0}function CJ(a){return yJ.has(a.tagName)};var DJ=class{constructor(a,b){this.g=a;this.i=b}};function EJ(a){return ta(a)&&a.nodeType==1&&a.tagName=="FIGURE"?a:(a=a.parentNode)?EJ(a):null};var FJ=a=>{var b=a.src;const c=a.getAttribute("data-src")||a.getAttribute("data-lazy-src");(b&&b.startsWith("data:")&&c?c:b||c)?(a.getAttribute("srcset"),b=(b=EJ(a))?(b=b.getElementsByTagName("figcaption")[0])?b.textContent:null:null,a=new DJ(a,b||a.getAttribute("alt")||null)):a=null;return a};function GJ(a){return a.map} 
var HJ=class{constructor(){this.map=new Map}clear(){this.map.clear()}delete(a,b){const c=this.map.get(a);return c?(b=c.delete(b),c.size===0&&this.map.delete(a),b):!1}get(a){return[...(this.map.get(a)??[])]}keys(){return this.map.keys()}add(a,b){let c=this.map.get(a);c||this.map.set(a,c=new Set);c.add(b)}get size(){let a=0;for(const b of this.map.values())a+=b.size;return a}values(){const a=this.map;return function(){return function*(){for(const b of a.values())q(yield*b)}()}()}[Symbol.iterator](){const a=this.map; 
return function(){return function*(){for(const [b,c]of a){const d=b,e=c;for(const f of e)q(yield[d,f])}}()}()}};function IJ(a){return[a[0],[...a[1]]]};function JJ(a){return Array.from(GJ(KJ(a)).values()).filter(b=>b.size>=3).map(b=>[...b])}function LJ(a,b){return b.every(c=>{var d=a.g.getBoundingClientRect(c.g);if(d=d.height>=50&&d.width>=a.l){var e=a.g.getBoundingClientRect(c.g);d=a.A;e=new uG(e.left,e.right);d=Math.max(d.start,e.start)<=Math.min(d.end,e.end)}return d&&$r(a.j,{yb:c.g,qb:MJ,Zb:!0})===null})}function NJ(a){return JJ(a).sort(OJ).find(b=>LJ(a,b))||[]} 
function KJ(a){return PJ(Array.from(a.B.document.getElementsByTagName("IMG")).map(FJ).filter(Bt),b=>{var c=[...AJ("CLASS_NAME",b.g)],d=b.g.parentElement;d=[...(d?AJ("PARENT_CLASS_NAME",d):[])];var e=b.g.parentElement?.parentElement;e=[...(e?AJ("GRANDPARENT_CLASS_NAME",e):[])];var f=(f=$r(a.i.g,{yb:b.g,qb:BJ}))?AJ("NEAREST_ANCESTOR_CLASS_NAME",f):[];return[...c,...d,...e,...f,...(b.i?["HAS_CAPTION=true"]:[]),...($r(a.i.g,{yb:b.g,qb:CJ})?["ARTICLE_LIKE_ANCESTOR=true"]:[])]})} 
var QJ=class{constructor(a,b,c,d,e){var f=new Qs;this.B=a;this.A=b;this.l=c;this.g=f;this.j=d;this.i=e}};function PJ(a,b){const c=new HJ;for(const d of a)for(const e of b(d))c.add(e,d);return c}function MJ(a){return a.tagName==="A"&&a.hasAttribute("href")}function OJ(a,b){return b.length-a.length};function RJ(a){const b=a.l.parentNode;if(!b)throw Error("Image not in the DOM");const c=SJ(a.j),d=TJ(a.j);c.appendChild(d);b.insertBefore(c,a.l.nextSibling);a.C.i().i(e=>{var f=a.j;const g=d.getBoundingClientRect(),h=g.top-e.top,k=g.left-e.left,l=g.width-e.width;e=g.height-e.height;Math.abs(h)<1&&Math.abs(k)<1&&Math.abs(l)<1&&Math.abs(e)<1||(f=f.getComputedStyle(d),v(d,{top:parseInt(f.top,10)-h+"px",left:parseInt(f.left,10)-k+"px",width:parseInt(f.width,10)-l+"px",height:parseInt(f.height,10)-e+"px"}))}); 
return d}function UJ(a){a.i||(a.i=RJ(a));return a.i}var VJ=class extends Q{constructor(a,b,c){super();this.j=a;this.l=b;this.C=c;this.i=null}g(){if(this.i){var a=this.i;const b=a.parentNode;b&&b.removeChild(a);this.i=null}super.g()}};function TJ(a){const b=a.document.createElement("div");$u(a,b);v(b,{position:"absolute",left:"0",top:"0",width:"0",height:"0","pointer-events":"none"});return b} 
function SJ(a){const b=a.document.createElement("div");$u(a,b);v(b,{position:"relative",width:"0",height:"0"});return b};function WJ(a){const b=new R(a.dataset.adStatus||null);(new MutationObserver(()=>{b.g(a.dataset.adStatus||null)})).observe(a,{attributes:!0});return ms(b)};const XJ=["Google Material Icons","Roboto"];function YJ({B:a,xa:b,Yj:c,Va:d,gb:e,U:f}){const g=new Ss(a,c);c=new VJ(a,c,g);es(c,g);a=new ZJ(a,d,e,b,c,f);es(a,c);a.init()} 
var ZJ=class extends Q{constructor(a,b,c,d,e,f){super();this.B=a;this.Va=b;this.gb=c;this.xa=d;this.j=e;this.U=f;this.i=new R(!1)}init(){const a=$J(this.B,this.Va,this.gb);UJ(this.j).appendChild(a.Cj);vy(this.B,a.ua);WJ(a.ua).i(b=>{if(b!==null){switch(b){case "unfilled":this.dispose();break;case "filled":this.i.g(!0);break;default:this.U?.reportError("Unhandled AdStatus: "+String(b)),this.dispose()}this.U?.Xk(this.xa,b)}});qs(this.i,!0,()=>void a.gk.g(!0));a.yj.listen(()=>void this.dispose());a.xj.listen(()=> 
void this.U?.Vk(this.xa))}}; 
function $J(a,b,c){const d=new R(!1),e=a.document.createElement("div");$u(a,e);v(e,{position:"absolute",top:"50%",left:"0",transform:"translateY(-50%)",width:"100%",height:"100%",overflow:"hidden","background-color":"rgba(0, 0, 0, 0.75)",opacity:"0",transition:"opacity 0.25s ease-in-out","box-sizing":"border-box",padding:"40px 5px 5px 5px"});ps(d,!0,()=>void v(e,{opacity:"1"}));ps(d,!1,()=>void v(e,{opacity:"0"}));const f=a.document.createElement("div");$u(a,f);v(f,{display:"block",width:"100%",height:"100%"}); 
e.appendChild(f);const {ta:g,fk:h}=aK(a,b);f.appendChild(g);e.appendChild(bK(a,E(c,1)));b=cK(a,E(c,2));e.appendChild(b.fj);b.qf.listen(()=>void d.g(!1));return{gk:d,Cj:e,ua:h,xj:b.qf,yj:b.qf.delay(a,450)}}function bK(a,b){const c=a.document.createElement("div");$u(a,c);v(c,{position:"absolute",top:"10px",width:"100%",color:"white","font-family":"Roboto","font-size":"12px","line-height":"16px","text-align":"center"});c.appendChild(a.document.createTextNode(b));return c} 
function cK(a,b){const c=a.document.createElement("button");c.setAttribute("aria-label",b);$u(a,c);v(c,{position:"absolute",top:"10px",right:"10px",display:"block",cursor:"pointer",width:"24px",height:"24px","font-size":"24px","user-select":"none",color:"white"});b=a.document.createElement("gm-icon");b.className="google-material-icons";b.appendChild(a.document.createTextNode("close"));c.appendChild(b);const d=new ys;c.addEventListener("click",()=>void xs(d));return{fj:c,qf:vs(d)}} 
function aK(a,b){a=ry(a.document,b,null,null,{});return{ta:a.Fb,fk:a.ua}};function dK({target:a,threshold:b=0}){const c=new eK;c.init(a,b);return c}var eK=class extends Q{constructor(){super();this.i=new R(!1)}init(a,b){const c=new IntersectionObserver(d=>{for(const e of d)if(e.target===a){this.i.g(e.isIntersecting);break}},{threshold:b});c.observe(a);fs(this,()=>void c.disconnect())}};function fK(a){const b=gK(a.B,yg(y(a.g,2))??250,yg(y(a.g,3))??300);let c=1;return NJ(a.A).map(d=>({xa:c++,image:d,zb:b(d)}))}function hK(a,b){const c=dK({target:b.image.g,threshold:Sh(a.g,4)??.8});a.j.push(c);qs(ts(c.i,a.B,yg(y(a.g,5))??3E3,d=>d),!0,()=>{if(a.i<(yg(y(a.g,1))??1)){YJ({B:a.B,xa:b.xa,Yj:b.image.g,Va:a.Va,gb:a.gb,U:a.U});a.i++;if(!(a.i<(yg(y(a.g,1))??1)))for(;a.j.length;)a.j.pop()?.dispose();a.U?.Wk(b.xa)}})} 
var jK=class{constructor(a,b,c,d,e,f){this.B=a;this.Va=b;this.g=c;this.gb=d;this.A=e;this.U=f;this.j=[];this.i=0}run(){const a=fK(this);a.filter(iK).forEach(b=>void hK(this,b));this.U?.Yk(a.map(b=>({xa:b.xa,zb:b.zb})))}};function iK(a){return a.zb.rejectionReasons.length===0}function gK(a,b,c){const d=Cr(a);return e=>{e=e.g.getBoundingClientRect();const f=[];e.width<b&&f.push(1);e.height<c&&f.push(2);e.top<=d&&f.push(3);return{Xb:e.width,Mf:e.height,zj:e.top-d,rejectionReasons:f}}};function kK(a,b){a.xa=b;return a}var lK=class{constructor(a,b,c,d,e){this.l=a;this.Va=b;this.hostname=c;this.j=d;this.A=e;this.errorMessage=this.i=this.xa=this.g=null}};function mK(a,b){return new lK(b,a.Va,a.hostname,a.i,a.A)} 
function nK(a,b,c){var d=a.j++;a.g===null?(a.g=Um(),a=0):a=Um()-a.g;var e=b.l,f=b.Va,g=b.hostname,h=b.j,k=b.A.map(encodeURIComponent).join(",");if(b.g){var l={imcnt:b.g.length};var m=Math.min(b.g.length,10);for(let n=0;n<m;n++){const p=`im${n}`;l[`${p}_id`]=b.g[n].xa;l[`${p}_s_w`]=b.g[n].zb.Xb;l[`${p}_s_h`]=b.g[n].zb.Mf;l[`${p}_s_dbf`]=b.g[n].zb.zj;b.g[n].zb.rejectionReasons.length>0&&(l[`${p}_s_rej`]=b.g[n].zb.rejectionReasons.join(","))}}else l=null;kC("abg::imovad",{typ:e,wpc:f,hst:g,pvsid:h,peid:k, 
rate:c,num:d,tim:a,...(b.xa===null?{}:{imid:b.xa}),...(b.i===null?{}:{astat:b.i}),...(b.errorMessage===null?{}:{errm:b.errorMessage}),...l},c)} 
var oK=class{constructor(a,b,c,d){this.Va=a;this.hostname=b;this.i=c;this.A=d;this.j=0;this.g=null}Yk(a){var b=mK(this,"fndi");b.g=a;nK(this,b,a.length>0?1:.1)}Wk(a){a=kK(mK(this,"adpl"),a);nK(this,a,1)}Xk(a,b){a=kK(mK(this,"adst"),a);a.i=b;nK(this,a,1)}Vk(a){a=kK(mK(this,"adis"),a);nK(this,a,1)}reportError(a){var b=mK(this,"err");b.errorMessage=a;nK(this,b,.1)}};function pK(a,b,c){return(a=a.g())&&Bi(a,11)?c.map(d=>d.j()):c.map(d=>d.l(b))};const qK=new Set([7,1]);var rK=class{constructor(){this.j=new HJ;this.A=[]}g(a,b){qK.has(b)||xt(ut(Hz(a),c=>void this.j.add(c,b)),c=>void this.A.push(c))}i(a,b){for(const c of a)this.g(c,b)}};function sK(a){return new Nt(["pedestal_container"],{google_reactive_ad_format:30,google_ad_width:Math.floor(a),google_ad_format:"autorelaxed",google_full_width_responsive:!0,google_enable_content_recommendations:!0,google_content_recommendation_ui_type:"pedestal"})}var tK=class{g(a){return sK(Math.floor(a.i))}};var uK=class extends H{};function vK(a,b){var c=b.adClient;if(typeof c!=="string"||!c)return!1;a.Re=c;a.A=!!b.adTest;c=b.pubVars;ta(c)&&(a.F=c);if(Array.isArray(b.fillMessage)&&b.fillMessage.length>0){a.C={};for(const d of b.fillMessage)a.C[d.key]=d.value}a.i=b.adWidth;a.g=b.adHeight;typeof a.i==="number"&&a.i>0&&typeof a.g==="number"&&a.g>0||kC("rctnosize",b);return!0}var wK=class{constructor(){this.C=this.F=this.A=this.Re=null;this.g=this.i=0}H(){return!0}};function xK(a){try{a.setItem("__storage_test__","__storage_test__");const b=a.getItem("__storage_test__");a.removeItem("__storage_test__");return b==="__storage_test__"}catch(b){return!1}}function yK(a,b=[]){const c=Date.now();return Qa(b,d=>c-d<a*1E3)}function zK(a,b,c){try{const d=a.getItem(c);if(!d)return[];let e;try{e=JSON.parse(d)}catch(f){}if(!Array.isArray(e)||Xa(e,f=>!Number.isInteger(f)))return a.removeItem(c),[];e=yK(b,e);e.length||a?.removeItem(c);return e}catch(d){return null}} 
function AK(a,b,c){return b<=0||a==null||!xK(a)?null:zK(a,b,c)};function BK(a,b,c){let d=0;try{var e=d|=Ar(a);const h=Br(a),k=a.innerWidth;var f=h&&k?h/k:0;d=e|(f?f>1.05?262144:f<.95?524288:0:131072);d|=Dr(a);d|=a.innerHeight>=a.innerWidth?0:8;d|=a.navigator&&/Android 2/.test(a.navigator.userAgent)?1048576:0;var g;if(g=b)g=AK(c,3600,"__lsv__")?.length!==0;g&&(d|=134217728)}catch(h){d|=32}return d};var CK=class extends wK{constructor(){super(...arguments);this.l=!1;this.j=null}H(a){this.l=!!a.enableAma;if(a=a.amaConfig)try{var b=Iu(a)}catch(c){b=null}else b=null;this.j=b;return!0}};var DK={};function EK(a,b,c){let d=FK(a,c,b);if(!d)return!0;const e=c.H.i;for(;d.Cc&&d.Cc.length;){const f=d.Cc.shift(),g=iz(f.ga);if(g&&!(typeof d.dc==="number"&&g<=d.dc))c.C?.g(f,18);else if(GK(c,f,{he:d.dc})){if(d.Bd.g.length+1>=e)return c.C?.i(d.Cc,19),!0;d=FK(a,c,b);if(!d)return!0}}return c.j} 
const FK=(a,b,c)=>{var d=b.H.i,e=b.H.A,f=b.H;f=zC(b.da(),f.g?f.g.Kc:void 0,d);if(f.g.length>=d)return b.C?.i(HK(b,f,{types:a},c),19),null;e?(d=f.i||(f.i=Gr(f.j).scrollHeight||null),e=!d||d<0?-1:d*e-FC(f)):e=void 0;const g=(d=e==null||e>=50)?HK(b,f,{types:a},c):null;d||b.C?.i(HK(b,f,{types:a},c),18);return{Bd:f,dc:e,Cc:g}};DK[2]=Ea(function(a,b){a=HK(b,zC(b.da()),{types:a,Ab:LB(b.da())},2);if(a.length==0)return!0;for(let c=0;c<a.length;c++)if(GK(b,a[c]))return!0;return b.j?(b.A.push(11),!0):!1},[0]); 
DK[5]=Ea(EK,[0],5);DK[10]=Ea(function(a,b){a=[];const c=b.zd;c.includes(3)&&a.push(2);c.includes(1)&&a.push(0);c.includes(2)&&a.push(1);return EK(a,10,b)},10);DK[3]=function(a){if(!a.j)return!1;const b=HK(a,zC(a.da()),{types:[0],Ab:LB(a.da())},3);if(b.length==0)return!0;for(let c=b.length-1;c>=0;c--)if(GK(a,b[c]))return!0;a.A.push(11);return!0}; 
const JK=a=>{const b=a.da().document.body.getBoundingClientRect().width;IK(a,sK(b))},LK=(a,b)=>{var c={types:[0],Ab:MB(),al:[5]};c=HK(a,zC(a.da()),c,8);KK(a,c.reverse(),b)},KK=(a,b,c)=>{for(const d of b)if(b=c.g(d.na),GK(a,d,{Se:b}))return!0;return!1}; 
DK[8]=function(a){var b=a.da().document;if(b.readyState!="complete")return b.addEventListener("readystatechange",()=>DK[8](a),{once:!0}),!0;if(!a.j)return!1;if(!a.be())return!0;b={types:[0],Ab:MB(),tg:[2,4,5]};b=HK(a,zC(a.da()),b,8);const c=new tK;if(KK(a,b,c))return!0;if(a.l.kh)switch(a.l.Xh||0){case 1:LK(a,c);break;default:JK(a)}return!0};DK[6]=Ea(EK,[2],6);DK[7]=Ea(EK,[1],7); 
DK[9]=function(a){const b=FK([0,2],a,9);if(!b||!b.Cc)return a.A.push(17),a.j;for(var c of b.Cc){var d=a.l.If||null;d==null?d=null:(d=jz(c.ga,new MK,new NK(d,a.da())),d=new Jz(d,c.ia(),c.na));if(!d)continue;const e=iz(d.ga);if(e!==null&&!(typeof b.dc==="number"&&e>b.dc)&&GK(a,d,{he:b.dc,mf:!0}))return a=d.ga.J,c=c.ga,a=a.length>0?a[0]:null,c.A=!0,a!=null&&c.J.push(a),!0}a.A.push(17);return a.j};var MK=class{i(a,b,c,d){return uy(d.document,a,b)}j(a){return Cr(a)||0}};var OK=class{constructor(a,b,c){this.i=a;this.g=b;this.Bd=c}Ba(a){return this.g?bD(this.i,this.g,a,this.Bd):aD(this.i,a,this.Bd)}wa(){return this.g?16:9}};var PK=class{constructor(a){this.Te=a}Ba(a){return iD(a.document,this.Te)}wa(){return 11}};var QK=class{constructor(a){this.Mb=a}Ba(a){return fD(this.Mb,a)}wa(){return 13}};var RK=class{Ba(a){return ZC(a)}wa(){return 12}};var SK=class{constructor(a){this.Vc=a}Ba(){return dD(this.Vc)}wa(){return 2}};var TK=class{constructor(a){this.g=a}Ba(){return gD(this.g)}wa(){return 3}};var UK=class{Ba(){return jD()}wa(){return 17}};var VK=class{constructor(a){this.g=a}Ba(){return cD(this.g)}wa(){return 1}};var WK=class{Ba(){return rc(ez)}wa(){return 7}};var XK=class{constructor(a){this.tg=a}Ba(){return eD(this.tg)}wa(){return 6}};var YK=class{constructor(a){this.g=a}Ba(){return hD(this.g)}wa(){return 5}};var ZK=class{constructor(a,b){this.minWidth=a;this.maxWidth=b}Ba(){return Ea(kD,this.minWidth,this.maxWidth)}wa(){return 10}};var $K=class{constructor(a){this.A=a.i.slice(0);this.i=a.g.slice(0);this.j=a.j;this.l=a.A;this.g=a.l}};function aL(a){var b=new bL;b.l=a;b.i.push(new VK(a));return b}function cL(a,b){a.i.push(new XK(b));return a}function dL(a,b){a.i.push(new SK(b));return a}function eL(a,b){a.i.push(new YK(b));return a}function fL(a,b){a.i.push(new TK(b));return a}function gL(a){a.i.push(new WK);return a}function hL(a){a.g.push(new RK);return a}function iL(a,b=0,c,d){a.g.push(new OK(b,c,d));return a} 
function jL(a,b=0,c=Infinity){a.g.push(new ZK(b,c));return a}function kL(a){a.g.push(new UK);return a}function lL(a,b=0){a.g.push(new QK(b));return a}function mL(a,b){a.j=b;return a}var bL=class{constructor(){this.j=0;this.A=!1;this.i=[].slice(0);this.g=[].slice(0)}build(){return new $K(this)}};var NK=class{constructor(a,b){this.i=a;this.j=b}g(){var a=this.i,b=this.j;const c=a.F||{};c.google_ad_client=a.Re;c.google_ad_height=Cr(b)||0;c.google_ad_width=Br(b)||0;c.google_reactive_ad_format=9;b=new uK;b=Li(b,1,a.l);a.j&&A(b,2,a.j);c.google_rasc=Ui(b);a.A&&(c.google_adtest="on");return new Nt(["fsi_container"],c)}};var nL=Gt(new Dt(0,{})),oL=Gt(new Dt(1,{})),pL=a=>a===nL||a===oL;function qL(a,b,c){Sr(a.g,b)||a.g.set(b,[]);a.g.get(b).push(c)}var rL=class{constructor(){this.g=new Wr}};function sL(a,b){a.H.wpc=b;return a}function tL(a,b){for(let c=0;c<a.l.length;c++)if(a.l[c]==b)return a;a.l.push(b);return a}function uL(a,b){for(let c=0;c<b.length;c++)tL(a,b[c]);return a}function vL(a,b){a.j=a.j?a.j:b;return a} 
var wL=class{constructor(a){this.H={};this.H.c=a;this.l=[];this.j=null;this.C=[];this.D=0}A(a){const b=Dc(this.H);this.D>0&&(b.t=this.D);b.err=this.l.join();b.warn=this.C.join();this.j&&(b.excp_n=this.j.name,b.excp_m=this.j.message&&this.j.message.substring(0,512),b.excp_s=this.j.stack&&Om(this.j.stack,""));b.w=0<a.innerWidth?a.innerWidth:null;b.h=0<a.innerHeight?a.innerHeight:null;return b}};function xL(a,b){b&&(a.g.apv=Ji(b,4),Oh(b,ju,23)&&(a.g.sat=""+z(b,ju,23).g()));return a}function yL(a,b){a.g.afm=b.join(",");return a}var zL=class extends wL{constructor(a){super(a);this.g={}}A(a){try{this.g.su=a.location.hostname}catch(b){this.g.su="_ex"}a=super.A(a);Fc(a,this.g);return a}};function AL(a){return a==null?null:Number.isInteger(a)?a.toString():a.toFixed(3)};function BL(a,b,c,d=30){c.length<=d?a[b]=CL(c):(a[b]=CL(c.slice(0,d)),a[b+"_c"]=c.length.toString())}function CL(a){const b=a.length>0&&typeof a[0]==="string";a=a.map(c=>c?.toString()??"null");b&&(a=a.map(c=>ja(c,"replaceAll").call(c,"~","")));return a.join("~")}function DL(a){return a==null?"null":typeof a==="string"?a:typeof a==="boolean"?a?"1":"0":Number.isInteger(a)?a.toString():a.toFixed(3)};function EL(a,b){a.i.op=DL(b)}function FL(a,b,c){BL(a.i,"fap",b);a.i.fad=DL(c)}function GL(a,b,c){BL(a.i,"fmp",b);a.i.fmd=DL(c)}function HL(a,b,c){BL(a.i,"vap",b);a.i.vad=DL(c)}function IL(a,b,c){BL(a.i,"vmp",b);a.i.vmd=DL(c)}function JL(a,b,c){BL(a.i,"pap",b);a.i.pad=DL(c)}function KL(a,b,c){BL(a.i,"pmp",b);a.i.pmd=DL(c)}function LL(a,b){BL(a.i,"psq",b)} 
var ML=class extends zL{constructor(a){super(0);Object.assign(this,a);this.i={};this.errors=[]}A(a){a=super.A(a);Object.assign(a,this.i);this.errors.length>0&&(a.e=CL(this.errors));return a}};function NL(a,b,c){const d=b.ga;Sr(a.g,d)||a.g.set(d,new OL(tt(Hz(b))??""));c(a.g.get(d))}function PL(a,b){NL(a,b,c=>{c.g=!0})}function QL(a,b){NL(a,b,c=>{c.i=!0})}function RL(a,b){NL(a,b,c=>{c.j=!0});a.M.push(b.ga)}function SL(a,b,c){NL(a,b,d=>{d.wc=c})}function TL(a,b,c){const d=[];let e=0;for(const f of c.filter(b))pL(f.wc??"")?++e:(b=a.i.get(f.wc??"",null),d.push(b));return{list:d.sort((f,g)=>(f??-1)-(g??-1)),xc:e}} 
function UL(a,b){EL(b,a.i.Zc());var c=Vr(a.g).filter(f=>(f.Sb.startsWith(nL)?0:1)===0),d=Vr(a.g).filter(f=>(f.Sb.startsWith(nL)?0:1)===1),e=TL(a,f=>f.g,c);FL(b,e.list,e.xc);e=TL(a,f=>f.g,d);GL(b,e.list,e.xc);e=TL(a,f=>f.i,c);HL(b,e.list,e.xc);e=TL(a,f=>f.i,d);IL(b,e.list,e.xc);c=TL(a,f=>f.j,c);JL(b,c.list,c.xc);d=TL(a,f=>f.j,d);KL(b,d.list,d.xc);LL(b,a.M.map(f=>a.g.get(f)?.wc).map(f=>a.i.get(f)??null))} 
function Bo(){var a=N(VL);if(!a.l)return qo();const b=zo(yo(xo(wo(vo(uo(to(so(po(oo(new ro,a.l??[]),a.J??[]),a.C),a.D),a.G),a.V),a.X),a.H??0),Vr(a.g).map(c=>{var d=new no;d=Ri(d,1,c.Sb);var e=a.i.get(c.wc??"",-1);d=Pi(d,2,e);d=Mi(d,3,c.g);return Mi(d,4,c.i)})),a.M.map(c=>a.g.get(c)?.wc).map(c=>a.i.get(c)??-1));a.j!=null&&Mi(b,6,a.j);a.A!=null&&di(b,13,Wg(a.A),"0");return b} 
var VL=class{constructor(){this.A=this.J=this.l=null;this.G=this.D=!1;this.j=null;this.X=this.C=this.V=!1;this.H=null;this.i=new Wr;this.g=new Wr;this.M=[]}};class OL{constructor(a){this.j=this.i=this.g=!1;this.wc=null;this.Sb=a}};var WL=class{constructor(a){this.i=a;this.g=-1}};function XL(a){let b=0;for(;a;)(!b||a.previousElementSibling||a.nextElementSibling)&&b++,a=a.parentElement;return b};function YL(a,b){const c=a.J.filter(d=>Ur(d.Nd).every(e=>d.Nd.get(e)===b.get(e)));return c.length===0?(a.i.push(19),null):c.reduce((d,e)=>d.Nd.Zc()>e.Nd.Zc()?d:e,c[0])}function ZL(a,b){b=Hz(b);if(!st(b))return a.i.push(18),null;b=b.getValue();if(Sr(a.j,b))return a.j.get(b);var c=Et(b);c=YL(a,c);a.j.set(b,c);return c} 
var $L=class{constructor(a){this.g=a;this.j=new Wr;this.J=(z(a,Gu,2)?.g()||[]).map(b=>{const c=Et(E(b,1)),d=Uu(vi(b,2));return{Nd:c,ai:d,Sb:E(b,1)}});this.i=[]}G(){const a=N(VL);var b=this.A();a.l=b;b=this.C();a.J=b;b=this.l();b!=null&&(a.A=b);b=!!this.g.i()?.g()?.g();a.G=b;b=new Wr;for(const c of z(this.g,Gu,2)?.g()??[])b.set(E(c,1),Uu(vi(c,2)));a.i=b}H(){return[...this.i]}A(){return[...this.g.g()]}C(){return[...Uh(this.g,4,Ug,1,void 0,1024).map(Uu)]}l(){return Tu(z(this.g,Au,5)?.g())??null}D(a){const b= 
ZL(this,a);b?.Sb!=null&&SL(N(VL),a,b.Sb)}M(a){return a.length==0?!0:.75<=lt((new jt(a)).filter(b=>{b=ZL(this,b)?.Sb||"";return b!=""&&!(b===nL||b===oL)}))/a.length}};function aM(a,b){return lt(b)==0?b:b.sort((c,d)=>(ZL(a.g,c)?.ai??Number.MAX_VALUE)-(ZL(a.g,d)?.ai??Number.MAX_VALUE))}function bM(a,b){var c=b.na.g,d=Math,e=d.min;const f=b.ia(),g=b.ga.i();c+=200*e.call(d,20,g==0||g==3?XL(f.parentElement):XL(f));a=a.i;a.g<0&&(a.g=Gr(a.i).scrollHeight||0);a=a.g-b.na.g;a=c+(a>1E3?0:2*(1E3-a));b.ia();return a}function cM(a,b){return lt(b)==0?b:b.sort((c,d)=>bM(a,c)-bM(a,d))} 
function dM(a,b){return b.sort((c,d)=>{const e=c.ga.H,f=d.ga.H;var g;e==null||f==null?g=e==null&&f==null?bM(a,c)-bM(a,d):e==null?1:-1:g=e-f;return g})}var eM=class{constructor(a,b=null){this.i=new WL(a);this.g=b&&new $L(b)}};function fM(a,b,c=0,d){var e=a.i;for(var f of b.A)e=it(e,f.Ba(a.j),gM(f.wa(),c));f=e=e.apply(YC(a.j));for(const g of b.i)f=it(f,g.Ba(a.j),At([hM(g.wa(),c),h=>{d?.g(h,g.wa())}]));switch(b.j){case 1:f=cM(a.g,f);break;case 2:f=dM(a.g,f);break;case 3:const g=N(VL);f=aM(a.g,f);e.forEach(h=>{PL(g,h);a.g.g?.D(h)});f.forEach(h=>QL(g,h))}b.l&&(f=mt(f,ud(a.j.location.href+a.j.localStorage.google_experiment_mod)));b.g?.length===1&&qL(a.A,b.g[0],{Cb:lt(e),ui:lt(f)});return kt(f)} 
var iM=class{constructor(a,b,c=null){this.i=new jt(a);this.g=new eM(b,c);this.j=b;this.A=new rL}};const gM=(a,b)=>c=>hz(c,b,a),hM=(a,b)=>c=>hz(c.ga,b,a);function jM(a,b,c,d){a:{switch(b){case 0:a=kM(lM(c),a);break a;case 3:a=kM(c,a);break a;case 2:const e=c.lastChild;a=kM(e?e.nodeType==1?e:lM(e):null,a);break a}a=!1}if(d=!a&&!(!d&&b==2&&!mM(c)))b=b==1||b==2?c:c.parentNode,d=!(b&&!dv(b)&&b.offsetWidth<=0);return d}function kM(a,b){if(!a)return!1;a=$d(a,b);if(!a)return!1;a=a.cssFloat||a.styleFloat;return a=="left"||a=="right"}function lM(a){for(a=a.previousSibling;a&&a.nodeType!=1;)a=a.previousSibling;return a?a:null} 
function mM(a){return!!a.nextSibling||!!a.parentNode&&mM(a.parentNode)};const nM=!ec&&!ac();function oM(a){if(/-[a-z]/.test("adFormat"))return null;if(nM&&a.dataset){if(!(!Xb("Android")||bc()||$b()||Zb()||Xb("Silk")||"adFormat"in a.dataset))return null;a=a.dataset.adFormat;return a===void 0?null:a}return a.getAttribute("data-"+"adFormat".replace(/([A-Z])/g,"-$1").toLowerCase())};function pM(a,b,c){if(!b)return null;const d=jm(document,"INS");d.id="google_pedestal_container";d.style.width="100%";d.style.zIndex="-1";if(c){var e=a.getComputedStyle(c),f="";if(e&&e.position!=="static"){var g=c.parentNode.lastElementChild;for(f=e.position;g&&g!==c;){if(a.getComputedStyle(g).display!=="none"){f=a.getComputedStyle(g).position;break}g=g.previousElementSibling}}if(c=f)d.style.position=c}b.appendChild(d);if(d){var h=a.document;f=h.createElement("div");f.style.width="100%";f.style.height= 
"2000px";c=Cr(a);e=h.body.scrollHeight;a=a.innerHeight;g=h.body.getBoundingClientRect().bottom;d.appendChild(f);var k=f.getBoundingClientRect().top;h=h.body.getBoundingClientRect().top;d.removeChild(f);f=e;e<=a&&c>0&&g>0&&(f=g-h);a=k-h>=.8*f}else a=!1;return a?d:(b.removeChild(d),null)} 
function qM(a){const b=a.document.body;var c=pM(a,b,null);if(c)return c;if(a.document.body){c=Math.floor(a.document.body.getBoundingClientRect().width);for(var d=[{element:a.document.body,depth:0,height:0}],e=-1,f=null;d.length>0;){const h=d.pop(),k=h.element;var g=h.height;h.depth>0&&g>e&&(e=g,f=k);if(h.depth<5)for(g=0;g<k.children.length;g++){const l=k.children[g],m=l.getBoundingClientRect().width;(m==null||c==null?0:m>=c*.9&&m<=c*1.01)&&d.push({element:l,depth:h.depth+1,height:l.getBoundingClientRect().height})}}c= 
f}else c=null;return c?pM(a,c.parentNode||b,c):null}function rM(a){let b=0;try{b|=Ar(a),wc()||(b|=1048576),Math.floor(a.document.body.getBoundingClientRect().width)<=1200||(b|=32768),sM(a)&&(b|=33554432)}catch(c){b|=32}return b}function sM(a){a=a.document.getElementsByClassName("adsbygoogle");for(let b=0;b<a.length;b++)if(oM(a[b])==="autorelaxed")return!0;return!1};function tM(a){const b=Fr(a,!0),c=Gr(a).scrollWidth,d=Gr(a).scrollHeight;let e="unknown";a&&a.document&&a.document.readyState&&(e=a.document.readyState);var f=Lr(a);const g=[];var h=[];const k=[],l=[];var m=[],n=[],p=[];let w=0,u=0,t=Infinity,B=Infinity,I=null;var U=nC({qc:!1},a);for(var M of U){U=M.getBoundingClientRect();const Pa=b-(U.bottom+f);var P=void 0,V=void 0;if(M.className&&M.className.indexOf("adsbygoogle-ablated-ad-slot")!=-1){P=M.getAttribute("google_element_uid");V=a.google_sv_map;if(!P|| 
!V||!V[P])continue;P=(V=Jm(V[P]))?V.height:0;V=V?V.width:0}else if(P=U.bottom-U.top,V=U.right-U.left,P<=1||V<=1)continue;g.push(P);k.push(V);l.push(P*V);vC(M)?(u+=1,M.className&&M.className.indexOf("pedestal_container")!=-1&&(I=P)):(t=Math.min(t,Pa),n.push(U),w+=1,h.push(P),m.push(P*V));B=Math.min(B,Pa);p.push(U)}t=t===Infinity?null:t;B=B===Infinity?null:B;f=uM(n);p=uM(p);h=vM(b,h);n=vM(b,g);m=vM(b*c,m);M=vM(b*c,l);return new wM(a,{Aj:e,ng:b,Jk:c,Hk:d,rk:w,Ui:u,Wi:xM(g),Xi:xM(k),Vi:xM(l),zk:f,yk:p, 
xk:t,wk:B,vf:h,uf:n,Si:m,Ri:M,Lk:I})} 
function yM(a,b,c,d){const e=wc()&&!(Br(a.B)>=900);d=Qa(d,f=>Za(a.i,f)).join(",");b={wpc:b,su:c,eid:d,doc:a.g.Aj??null,pg_h:zM(a.g.ng),pg_w:zM(a.g.Jk),pg_hs:zM(a.g.Hk),c:zM(a.g.rk),aa_c:zM(a.g.Ui),av_h:zM(a.g.Wi),av_w:zM(a.g.Xi),av_a:zM(a.g.Vi),s:zM(a.g.zk),all_s:zM(a.g.yk),b:zM(a.g.xk),all_b:zM(a.g.wk),d:zM(a.g.vf),all_d:zM(a.g.uf),ard:zM(a.g.Si),all_ard:zM(a.g.Ri),pd_h:zM(a.g.Lk),dt:e?"m":"d"};c={};for(const f of Object.keys(b))b[f]!==null&&(c[f]=b[f]);return c} 
var wM=class{constructor(a,b){this.i="633794002 633794005 21066126 21066127 21065713 21065714 21065715 21065716 42530887 42530888 42530889 42530890 42530891 42530892 42530893".split(" ");this.B=a;this.g=b}};function xM(a){return Nl.apply(null,Qa(a,b=>b>0))||null}function vM(a,b){return a<=0?null:Ml.apply(null,b)/a} 
function uM(a){let b=Infinity;for(let e=0;e<a.length-1;e++)for(let f=e+1;f<a.length;f++){var c=a[e],d=a[f];c=Math.max(Math.max(0,c.left-d.right,d.left-c.right),Math.max(0,c.top-d.bottom,d.top-c.bottom));c>0&&(b=Math.min(c,b))}return b!==Infinity?b:null}function zM(a){return a==null?null:Number.isInteger(a)?a.toString():a.toFixed(3)};function AM(a){var b=xC({qc:!1,Yd:!1},a);a=(Cr(a)||0)-Lr(a);let c=0;for(let d=0;d<b.length;d++){const e=b[d].getBoundingClientRect();DC(e)&&e.top<=a&&(c+=1)}return c>0} 
function BM(a){const b={};var c=xC({qc:!1,Yd:!1,Sf:!1,Tf:!1},a).map(d=>d.getBoundingClientRect()).filter(DC);b.Mg=c.length;c=yC({Sf:!0},a).map(d=>d.getBoundingClientRect()).filter(DC);b.ih=c.length;c=yC({Tf:!0},a).map(d=>d.getBoundingClientRect()).filter(DC);b.Qh=c.length;c=yC({Yd:!0},a).map(d=>d.getBoundingClientRect()).filter(DC);b.Qg=c.length;c=(Cr(a)||0)-Lr(a);c=xC({qc:!1},a).map(d=>d.getBoundingClientRect()).filter(DC).filter(Da(CM,null,c));b.Ng=c.length;a=tM(a);c=a.g.vf!=null?a.g.vf:null;c!= 
null&&(b.Kh=c);a=a.g.uf!=null?a.g.uf:null;a!=null&&(b.Og=a);return b}function GK(a,b,{he:c,Se:d,mf:e}={}){return My(997,()=>DM(a,b,{he:c,Se:d,mf:e}),a.g)} 
function HK(a,b,c,d){var e=c.Ab?c.Ab:a.H;const f=NB(e,b.g.length);e=a.l.Pg?e.g:void 0;const g=kL(lL(hL(jL(iL(gL(eL(fL(cL(dL(aL(c.types),a.Ka),c.tg||[]),a.pa),c.al||[])),f.ld||void 0,e,b),c.minWidth,c.maxWidth)),f.Mb||void 0));a.X&&g.g.push(new PK(a.X));b=1;a.Ib()&&(b=3);mL(g,b);a.l.li&&(g.A=!0);return My(995,()=>fM(a.i,g.build(),d,a.C||void 0),a.g)}function IK(a,b){const c=qM(a.g);if(c){const d=Mt(a.V,b),e=ry(a.g.document,a.G,null,null,{},d);e&&(Qx(e.Fb,c,2,256),My(996,()=>EM(a,e,d),a.g))}} 
function FM(a){return a.J?a.J:a.J=a.g.google_ama_state} 
function DM(a,b,{he:c,Se:d,mf:e}={}){const f=b.ga;if(f.A)return!1;var g=b.ia(),h=f.i();if(!jM(a.g,h,g,a.j))return!1;h=null;f.ed?.includes(6)?(h=Math.round(g.getBoundingClientRect().height),h=new Nt(null,{google_max_responsive_height:c==null?h:Math.min(c,h),google_full_width_responsive:"false"})):h=c==null?null:new Nt(null,{google_max_responsive_height:c});c=Ot(Ki(f.Je,2)||0);g=Pt(f.H);const k=GM(a,f),l=HM(a),m=Mt(a.V,f.V?f.V.g(b.na):null,h,d||null,c,g,k,l),n=b.fill(a.G,m);if(e&&!IM(a,n,m)||!My(996, 
()=>EM(a,n,m),a.g))return!1;Kl(9,[f.H,f.uc]);a.Ib()&&RL(N(VL),b);return!0}function GM(a,b){return tt(xt(Fz(b).map(Qt),()=>{a.A.push(18)}))}function HM(a){if(!a.Ib())return null;var b=a.i.g.g?.C();if(b==null)return null;b=b.join("~");a=a.i.g.g?.l()??null;return Rt({pj:b,Ij:a})}function IM(a,b,c){if(!b)return!1;var d=b.ua;const e=d.style.width;d.style.width="100%";const f=d.offsetWidth;d.style.width=e;if(ay(f,a.g,b.ua,c&&c.bd()||{}))return gy(a.g,b.ua),!0;hv(b.Fb);return!1} 
function EM(a,b,c){if(!b)return!1;try{vy(a.g,b.ua,c)}catch(d){return hv(b.Fb),a.A.push(6),!1}return!0} 
var JM=class{constructor(a,b,c,d,e={},f=[],g=!1){this.i=a;this.G=b;this.g=c;this.H=d.Ab;this.Ka=d.Vc||[];this.V=d.Kj||null;this.pa=d.uj||[];this.X=d.Te||[];this.l=e;this.j=!1;this.D=[];this.A=[];this.M=this.J=void 0;this.zd=f;this.C=g?new rK:null}da(){return this.g}Ib(){if((this.i.g.g?.A().length??0)==0)return!1;if(this.M===void 0){const a=mL(hL(gL(aL([0,1,2]))),1).build(),b=My(995,()=>fM(this.i,a),this.g);this.M=this.i.g.g?.M(b)||!1}return this.M}Xf(){return!!this.l.fi}be(){return!sM(this.g)}nb(){return this.C}}; 
const CM=(a,b)=>b.top<=a;function KM(a,b,c,d,e,f=0,g=0){this.ob=a;this.Ce=f;this.Be=g;this.errors=b;this.Wb=c;this.g=d;this.i=e};var LM=(a,{be:b=!1,Xf:c=!1,dl:d=!1,Ib:e=!1}={})=>{const f=[];d&&f.push(9);if(e){a.includes(4)&&!c&&b&&f.push(8);a.includes(1)&&f.push(1);d=a.includes(3);e=a.includes(2);const g=a.includes(1);(d||e||g)&&f.push(10)}else a.includes(3)&&f.push(6),a.includes(4)&&!c&&b&&f.push(8),a.includes(1)&&f.push(1,5),a.includes(2)&&f.push(7);a.includes(4)&&c&&b&&f.push(8);return f};function MM(a,b,c){a=LM(a,{be:b.be(),Xf:b.Xf(),dl:!!b.l.If,Ib:b.Ib()});return new NM(a,b,c)}function OM(a,b){const c=DK[b];return c?My(998,()=>c(a.g),a.l):(a.g.D.push(12),!0)}function PM(a,b){return new Promise(c=>{setTimeout(()=>{c(OM(a,b))})})}function QM(a){a.g.j=!0;return Promise.all(a.i.map(b=>PM(a,b))).then(b=>{b.includes(!1)&&a.g.D.push(5);a.i.splice(0,a.i.length)})}var NM=class{constructor(a,b,c){this.A=a.slice(0);this.i=a.slice(0);this.j=$a(this.i,1);this.g=b;this.l=c}};var RM=class{constructor(a){this.g=a;this.exception=void 0}};function SM(a){return QM(a).then(()=>{var b=lt(a.g.i.i.filter(ez));var c=a.g.D.slice(0);var d=a.g;d=[...d.A,...(d.i.g.g?.H()||[])];b=new KM(b,c,d,lt(a.g.i.i),a.g.i.A.g,lt(a.g.i.i.filter(ez).filter(fz)),lt(a.g.i.i.filter(fz)));return new RM(b)})};var TM=class{g(){return new Nt([],{google_reactive_ad_format:40,google_tag_origin:"qs"})}};var UM=class{g(){return new Nt(["adsbygoogle-resurrected-ad-slot"],{})}};function VM(a){return ev(a.g.document).map(b=>{const c=new Yy(b,3);b=new $y(xy(a.g,b));return new dz(c,b,a.i,!1,0,[],null,a.g,null)})}var WM=class{constructor(a){var b=new UM;this.g=a;this.i=b||null}};const XM={Ig:"10px",jf:"10px"};function YM(a){return Rr(a.g.document.querySelectorAll("INS.adsbygoogle-placeholder")).map(b=>new dz(new Yy(b,1),new Wy(XM),a.i,!1,0,[],null,a.g,null))}var ZM=class{constructor(a,b){this.g=a;this.i=b||null}};function $M(a,b){const c=[];b.forEach((d,e)=>{c.push(ja(e,"replaceAll").call(e,"~","_")+"--"+d.map(f=>Number(f)).join("_"))});BL(a.g,"cnstr",c,80)}var aN=class extends wL{constructor(){super(-1);this.g={}}A(a){a=super.A(a);Object.assign(a,this.g);return a}};var bN=class extends Error{constructor(a,b,c){super(a);this.g=b;this.i=c}};function cN(a,b,c){return a==null?new bN(b+"ShouldNotBeNull",2,c):a==0?new bN(b+"ShouldNotBeZero",3,c):a<-1?new bN(b+"ShouldNotBeLessMinusOne",4,c):null}function dN(a,b,c){const d=cN(c.Xc,"gapsMeasurementWindow",1)||cN(c.mc,"gapsPerMeasurementWindow",2)||cN(c.zc,"maxGapsToReport",3);return d!=null?qt(d):c.Ve||c.mc!=-1||c.zc!=-1?ot(new eN(a,b,c)):qt(new bN("ShouldHaveLimits",1,0))} 
function fN(a){return FM(a.j)&&FM(a.j).placed||[]}function gN(a){return fN(a).map(b=>at(Zs(b.element,a.g)))}function hN(a){return fN(a).map(b=>b.index)}function iN(a,b){const c=b.ga;return!a.C&&c.j&&wg(y(c.j,8))!=null&&Ki(c.j,8)==1?[]:c.A?(c.J||[]).map(d=>at(Zs(d,a.g))):[at(new $s(b.na.g,0))]}function jN(a){a.sort((e,f)=>e.g-f.g);const b=[];let c=0;for(let e=0;e<a.length;++e){var d=a[e];let f=d.g;d=d.g+d.i;f<=c?c=Math.max(c,d):(b.push(new $s(c,f-c)),c=d)}return b} 
function kN(a,b){b=b.map(c=>{var d=new Tn;d=Ni(d,1,c.g);c=c.getHeight();return Ni(d,2,c)});return Vn(Un(new Wn,a),b)}function lN(a){const b=mi(a,Tn,2,Th()).map(c=>`G${ui(c,1)}~${c.getHeight()}`);return`W${ui(a,1)}${b.join("")}`}function mN(a,b){const c=[];let d=0;for(const e of Ur(b)){const f=b.get(e);f.sort((g,h)=>h.getHeight()-g.getHeight());a.G||f.splice(a.l,f.length);!a.H&&d+f.length>a.i&&f.splice(a.i-d,f.length);c.push(kN(e,f));d+=f.length;if(!a.H&&d>=a.i)break}return c} 
function nN(a){const b=mi(a,Wn,5,Th()).map(c=>lN(c));return`M${ui(a,1)}H${ui(a,2)}C${ui(a,3)}B${Number(!!C(a,4))}${b.join("")}`} 
function oN(a){var b=Kz(kt(a.j.i.i),a.g),c=gN(a),d=new Xr(hN(a));for(var e=0;e<b.length;++e)if(!d.contains(e)){var f=iN(a,b[e]);c.push(...f)}c.push(new $s(0,0));c.push(at(new $s(Gr(a.g).scrollHeight,0)));b=jN(c);c=new Wr;for(d=0;d<b.length;++d)e=b[d],f=a.D?0:Math.floor(e.g/a.A),Sr(c,f)||c.set(f,[]),c.get(f).push(e);b=mN(a,c);c=new Xn;c=Ni(c,1,a.i);c=Ni(c,2,a.A);c=Ni(c,3,a.l);a=Li(c,4,a.C);return pi(a,5,b)}function pN(a){a=oN(a);return nN(a)} 
var eN=class{constructor(a,b,c){this.D=c.Xc==-1;this.A=c.Xc;this.G=c.mc==-1;this.l=c.mc;this.H=c.zc==-1;this.i=c.zc;this.C=c.Rf;this.j=b;this.g=a}};function Wu(a,b,c){let d=b.ub;b.sc&&O(aw)&&(d=1,"r"in c&&(c.r+="F"));d<=0||(!b.Hb||"pvc"in c||(c.pvc=Ge(a.g)),kC(b.nc,c,d))}function qN(a,b,c){c=c.A(a.g);b.Hb&&(c.pvc=Ge(a.g));0<=b.ub&&(c.r=b.ub,Wu(a,b,c))}var rN=class{constructor(a){this.g=a}};const sN={google_ad_channel:!0,google_ad_host:!0};function tN(a,b){a.location.href&&a.location.href.substring&&(b.url=a.location.href.substring(0,200));kC("ama",b,.01)}function uN(a){const b={};be(sN,(c,d)=>{d in a&&(b[d]=a[d])});return b};function vN(a){return a.replace(/(^\/)|(\/$)/g,"")}function wN(a){const b=/[a-zA-Z0-9._~-]/,c=/%[89a-zA-Z]./;return a.replace(/(%[a-zA-Z0-9]{2})/g,d=>{if(!d.match(c)){const e=decodeURIComponent(d);if(e.match(b))return e}return d.toUpperCase()})}function xN(a){let b="";const c=/[/%?&=]/;for(let d=0;d<a.length;++d){const e=a[d];b=e.match(c)?b+e:b+encodeURIComponent(e)}return b};function yN(a,b){a=zi(a,2);if(!a)return!1;for(let c=0;c<a.length;c++)if(a[c]==b)return!0;return!1}function zN(a,b){a=vN(xN(wN(a.location.pathname)));const c=de(a),d=AN(a);return b.find(e=>{if(Oh(e,$t,7)){var f=z(e,$t,7);f=Ag(y(f,1,void 0,Kh))}else f=Ag(y(e,1,void 0,Kh));Oh(e,$t,7)?(e=z(e,$t,7),e=Ki(e,2)):e=2;if(typeof f!=="number")return!1;switch(e){case 1:return f==c;case 2:return d[f]||!1}return!1})||null} 
function AN(a){const b={};for(;;){b[de(a)]=!0;if(!a)return b;a=a.substring(0,a.lastIndexOf("/"))}};function BN(a,b){try{b.removeItem("google_ama_config")}catch(c){tN(a,{lserr:1})}};var DN=(a,b,c,d,e=null,f=null)=>{CN(a,new rN(a),b,c,d,e,f)},CN=(a,b,c,d,e,f=null,g=null)=>{if(c)if(d){var h=gG(d,e);try{const k=new EN(a,b,c,d,e,h,f,g);My(990,()=>FN(k),a)}catch(k){Jl()&&Kl(15,[k]),qN(b,Ou,vL(tL(sL(yL(xL(new zL(0),d),h),c),1),k)),HI(N(EI),Fo(new Oo,Qn(1)))}}else qN(b,Ou,tL(sL(new zL(0),c),8)),HI(N(EI),Fo(new Oo,Qn(8)));else qN(b,Ou,tL(new zL(0),9)),HI(N(EI),Fo(new Oo,Qn(9)))}; 
function FN(a){a.J.forEach(b=>{switch(b){case 0:My(991,()=>GN(a),a.g);break;case 1:My(1073,()=>{$F(new fG(a.g,a.C,a.A,a.l,a.i.ca))},a.g);break;case 2:HN(a);break;case 7:My(1203,()=>{var c=z(a.A,zu,34);if(c){var d=a.g,e=a.l,f=Gh(c);c=d.location.hostname;var g=z(f,yu,1)?.g()??[];c=new oK(e,c,Ge(r),g);if(g=z(f,yu,1))if(f=z(f,xu,2)){ct(d,XJ);const l=new bs;var h=d.innerWidth;var k=.375*h;h=new uG(k,h-k);k=d.innerWidth;k=Br(d)>=900?.2*k:.5*k;(new jK(d,e,g,f,new QJ(d,h,k,l,new zJ(l)),c)).run()}else c.reportError("No messages"); 
else c.reportError("No settings")}},a.g)}})} 
function GN(a){var b=O(Nv)?void 0:a.i.Ok;let c=null;c=O(Nv)?LB(a.g):JB(a.g,b);if(a.i.ca&&Oh(a.i.ca,Zt,10)){var d=Yt(a.i.ca.g());d!==null&&d!==void 0&&(c=AB(a.g,d,b));O(ew)&&a.i.ca.g()?.g()===2&&(c=IB(a.i.ca.g(),c))}Oh(a.A,Vt,26)&&(c=OB(c,z(a.A,Vt,26),a.g));c=QB(c,a.g);b=a.i.ca?zi(a.i.ca,6):[];d=a.i.ca?mi(a.i.ca,eu,5,Th()):[];const e=a.i.ca?zi(a.i.ca,2):[],f=My(993,()=>{var g=a.A,h=mi(g,vu,1,Th()),k=a.i.ca&&yN(a.i.ca,1)?"text_image":"text",l=new TM,m=cz(h,a.g,{Yi:l,ek:new az(k)});h.length!=m.length&& 
a.G.push(13);m=m.concat(YM(new ZM(a.g,l)));h=O(bw);l=z(g,Hu,24)?.i()?.g()?.g()||!1;if(h||l)h=VM(new WM(a.g)),l=N(VL),m=m.concat(h),l.V=!0,l.H=h.length,a.D==="n"&&(a.D=z(g,Hu,24)?.g()?.length?"o":"p");h=O(ew)&&a.i.ca.g()?.g()===2&&a.i.ca.g()?.i();h=O(Lv)||h;a:{if(l=z(g,ru,6))for(n of l.g())if(Oh(n,Ct,4)){var n=!0;break a}n=!1}h&&n?(n=m.concat,h=a.g,(l=z(g,ru,6))?(h=Cz(l.g(),h),k=pK(g,k,h)):k=[],k=n.call(m,k)):(n=m.concat,h=a.g,(l=z(g,ru,6))?(h=Bz(l.g(),h),k=pK(g,k,h)):k=[],k=n.call(m,k));m=k;g=z(g, 
Hu,24);return new iM(m,a.g,g)},a.g);a.j=new JM(f,a.l,a.g,{Ab:c,Kj:a.V,Vc:a.i.Vc,uj:b,Te:d},IN(a),e,O(aw));FM(a.j)?.optimization?.ablatingThisPageview&&!a.j.Ib()&&(wy(a.g),N(VL).C=!0,a.D="f");a.H=MM(e,a.j,a.g);My(992,()=>SM(a.H),a.g).then(My(994,()=>a.pa.bind(a),a.g),a.X.bind(a))}function HN(a){const b=z(a.A,wu,18);b&&(new OI(a.g,new xJ(a.g,a.l),b,new sE(a.g),mi(a.A,vu,1,Th()))).run()} 
function IN(a){const b=O(dw);if(!a.A.g())return{li:b,kh:!1,fi:!1,Mk:0,Xh:0,Pg:JN(a),If:a.M};const c=a.A.g();return{li:b||C(c,14),kh:C(c,5),fi:C(c,6),Mk:wi(c,8),Xh:Ki(c,10),Pg:JN(a),If:a.M}}function JN(a){return O(Vv)||O(ew)&&a.i.ca?.g()?.g()===2?!1:a.i.ca&&Oh(a.i.ca,Zt,10)?(Yt(a.i.ca.g())||0)>=.5:!0} 
function KN(a,b){var c=new zL(b.ob);c.g.pp=b.Be;c.g.ppp=b.Ce;c.g.ppos=b.placementPositionDiffs;c.g.eatf=b.Pc;c.g.eatfAbg=b.Qc;c.g.reatf=b.oc;c.g.a=a.H.A.slice(0).join(",");c=yL(xL(c,a.A),a.J);var d=b.La;d&&(c.g.as_count=d.Mg,c.g.d_count=d.ih,c.g.ng_count=d.Qh,c.g.am_count=d.Qg,c.g.atf_count=d.Ng,c.g.mdns=AL(d.Kh),c.g.alldns=AL(d.Og));d=b.Bc;d!=null&&(c.g.allp=d);if(d=b.Ud){var e=[];for(var f of Ur(d))if(d.get(f).length>0){var g=d.get(f)[0];e.push("("+[f,g.Cb,g.ui].join()+")")}c.g.fd=e.join(",")}f= 
b.ng;f!=null&&(c.g.pgh=f);c.g.abl=b.wh;c.g.rr=a.D;a=uL(uL(sL(c,a.l),b.errors),a.G);c=b.Wb;for(e=0;e<c.length;e++)a:{f=a;d=c[e];for(g=0;g<f.C.length;g++)if(f.C[g]==d)break a;f.C.push(d)}b.exception!==void 0&&tL(vL(a,b.exception),1);return a} 
function LN(a,b){var c=KN(a,b);qN(a.C,b.errors.length>0||a.G.length>0||b.exception!==void 0?Ou:Nu,c);if(z(a.A,Hu,24)){a.j.i.g.g?.G();b=FM(a.j);const d=N(VL);d.j=!!b?.optimization?.ablationFromStorage;b?.optimization?.ablatingThisPageview&&(d.D=!0);d.X=!!b?.optimization?.availableAbg;b=N(VL);c=new ML(c);b.l?(c.i.sl=CL(b.l??[]),c.i.daaos=CL(b.J??[]),c.i.ab=DL(b.D),c.i.rr=DL(b.V),c.i.oab=DL(b.G),b.j!=null&&(c.i.sab=DL(b.j)),b.C&&(c.i.fb=DL(b.C)),c.i.ls=DL(b.X),EL(c,b.i.Zc()),b.H!=null&&(c.i.rp=DL(b.H)), 
b.A!=null&&(c.i.expl=DL(b.A)),UL(b,c)):c.errors.push("irr");qN(a.C,Qu,c)}c=a.j?.nb();O(aw)&&c!=null&&(c=new Map([...c.j.map.entries()].map(IJ)),b=new aN,$M(b,c),qN(a.C,Su,b))}function MN(a,b){if(O(Pv)&&a.j!=null){var c=dN(a.g,a.j,{Xc:W($v),mc:W(Zv),zc:W(Rv),Rf:!0,Ve:!1});if(st(c))a=new $n,c=oN(c.getValue()),a=oi(a,2,Zn,c),A(b,16,a);else{var d=c.g;a=new $n;c=a.setError;var e=new Yn;e=Si(e,2,d.i);d=Si(e,1,d.g);a=c.call(a,d);A(b,16,a)}}} 
function NN(a,b){const c=N(EI);if(c.i){var d=new Oo,e=b.Wb.filter(g=>g!==null),f=a.G.concat(b.errors,b.exception?[1]:[]).filter(g=>g!==null);Ko(Ho(No(Mo(Lo(Jo(Io(Co(Eo(Go(Do(d,a.H.A.slice(0).map(g=>{var h=new Pn;return Si(h,1,g)})),e.map(g=>{var h=new Sn;return Si(h,1,g)})),f.map(g=>Qn(g))),z(a.A,ju,23)?.g()),b.ob),b.Bc),b.oc),b.Pc),b.Qc),a.J.map(g=>g.toString())),ho(go(fo(eo(co(bo(ao(new io,b.La?.Mg),b.La?.ih),b.La?.Qh),b.La?.Qg),b.La?.Ng),b.La?.Kh),b.La?.Og));if(b.Ud)for(let g of Ur(b.Ud)){e=new bi; 
for(let h of b.Ud.get(g))mo(e,ko(jo(new lo,h.Cb),h.ui));ai(d).set(g.toString(),e)}z(a.A,Hu,24)&&Ao(d);MN(a,d);HI(c,d)}} 
function ON(a,b,c){{var d=FM(a.j),e=b.g;const f=e.g,g=e.Be;let h=e.ob,k=e.Ce,l=e.errors.slice(),m=e.Wb.slice(),n=b.exception;const p=pI(a.g).had_ads_ablation??!1;d?(d.numAutoAdsPlaced?h+=d.numAutoAdsPlaced:a.H.j&&m.push(13),d.exception!==void 0&&(n=d.exception),d.numPostPlacementsPlaced&&(k+=d.numPostPlacementsPlaced),c={ob:h,Be:g,Ce:k,Bc:f,errors:e.errors.slice(),Wb:m,exception:n,oc:c,Pc:!!d.eatf,Qc:!!d.eatfAbg,wh:p}):(m.push(12),a.H.j&&m.push(13),c={ob:h,Be:g,Ce:k,Bc:f,errors:l,Wb:m,exception:n, 
oc:c,Pc:!1,Qc:!1,wh:p})}c.La=BM(a.j.g);if(b=b.g.i)c.Ud=b;c.ng=Gr(a.g).scrollHeight;if(Jl()||z(a.A,iu,25)?.i()){d=kt(a.j.i.i);b=[];for(const f of d){d={};e=f.M;for(const g of Ur(e))d[g]=e.get(g);d={anchorElement:f.D.g(f.g),position:f.i(),clearBoth:f.G,locationType:f.uc,placed:f.A,placementProto:f.j?th(f.j):null,articleStructure:f.l?th(f.l):null,rejectionReasons:d};b.push(d)}Kl(14,[{placementIdentifiers:b},a.j.G,c.La])}return c} 
function PN(a,b){var c=a.j.g;c=c.googleSimulationState=c.googleSimulationState||{};c.amaConfigPlacementCount=b.Bc;c.numAutoAdsPlaced=b.ob;c.hasAtfAd=b.oc;b.exception!==void 0&&(c.exception=b.exception);if(a.j!=null)if(a=dN(a.g,a.j,{Xc:-1,mc:-1,zc:-1,Rf:!0,Ve:!0}),st(a))c.placementPositionDiffs=pN(a.getValue()),b=oN(a.getValue()),a=new $n,a=oi(a,2,Zn,b),c.placementPositionDiffsReport=Ui(a);else{c.placementPositionDiffs="E"+a.g.message;var d=a.g;a=new $n;b=a.setError;var e=new Yn;e=Si(e,2,d.i);d=Si(e, 
1,d.g);a=b.call(a,d);c.placementPositionDiffsReport=Ui(a)}}function QN(a,b){LN(a,{ob:0,Bc:void 0,errors:[],Wb:[],exception:b,oc:void 0,Pc:void 0,Qc:void 0,La:void 0});NN(a,{ob:0,Bc:void 0,errors:[],Wb:[],exception:b,oc:void 0,Pc:void 0,Qc:void 0,La:void 0})} 
var EN=class{constructor(a,b,c,d,e,f,g,h){this.g=a;this.C=b;this.l=c;this.A=d;this.i=e;this.J=f;this.V=g||null;this.G=[];this.M=h;this.D="n"}pa(a){try{const b=AM(this.j.g)||void 0;Mu({zf:b},this.g);const c=ON(this,a,AM(this.j.g));Oh(this.A,iu,25)&&hu(z(this.A,iu,25))&&PN(this,c);LN(this,c);NN(this,c);iC(753,()=>{if(O(Ov)&&this.j!=null){var d=dN(this.g,this.j,{Xc:W($v),mc:W(Zv),zc:W(Rv),Rf:!0,Ve:!1}),e=Dc(c);st(d)?(d=pN(d.getValue()),e.placementPositionDiffs=d):e.placementPositionDiffs="E"+d.g.message; 
e=KN(this,e);qN(this.C,Pu,e)}})()}catch(b){QN(this,b)}}X(a){QN(this,a)}};var RN=class extends H{},SN=Hk(RN);function TN(a){try{var b=a.localStorage.getItem("google_auto_fc_cmp_setting")||null}catch(d){b=null}const c=b;return c?rt(()=>SN(c)):ot(null)};function UN(a){this.g=a||{cookie:""}}ba=UN.prototype; 
ba.set=function(a,b,c){let d,e,f,g=!1,h;typeof c==="object"&&(h=c.gi,g=c.Ie||!1,f=c.domain||void 0,e=c.path||void 0,d=c.ie);if(/[;=\s]/.test(a))throw Error('Invalid cookie name "'+a+'"');if(/[;\r\n]/.test(b))throw Error('Invalid cookie value "'+b+'"');d===void 0&&(d=-1);this.g.cookie=a+"="+b+(f?";domain="+f:"")+(e?";path="+e:"")+(d<0?"":d==0?";expires="+(new Date(1970,1,1)).toUTCString():";expires="+(new Date(Date.now()+d*1E3)).toUTCString())+(g?";secure":"")+(h!=null?";samesite="+h:"")}; 
ba.get=function(a,b){const c=a+"=",d=(this.g.cookie||"").split(";");for(let e=0,f;e<d.length;e++){f=Qb(d[e]);if(f.lastIndexOf(c,0)==0)return f.slice(c.length);if(f==a)return""}return b};function VN(a,b,c,d){a.get(b);a.set(b,"",{ie:0,path:c,domain:d})}ba.isEmpty=function(){return!this.g.cookie};ba.Zc=function(){return this.g.cookie?(this.g.cookie||"").split(";").length:0}; 
ba.clear=function(){var a=(this.g.cookie||"").split(";");const b=[],c=[];let d,e;for(let f=0;f<a.length;f++)e=Qb(a[f]),d=e.indexOf("="),d==-1?(b.push(""),c.push(e)):(b.push(e.substring(0,d)),c.push(e.substring(d+1)));for(a=b.length-1;a>=0;a--)VN(this,b[a])};function WN(a,b=window){if(a.g())try{return b.localStorage}catch{}return null}let XN;function YN(a){return XN?XN:a.origin==="null"?XN=!1:XN=ZN(a)}function ZN(a){if(!a.navigator.cookieEnabled)return!1;const b=new UN(a.document);if(!b.isEmpty())return!0;b.set("TESTCOOKIESENABLED","1",{ie:60,gi:a.isSecureContext?"none":void 0,Ie:a.isSecureContext||void 0});if(b.get("TESTCOOKIESENABLED")!=="1")return!1;VN(b,"TESTCOOKIESENABLED");return!0} 
function $N(a,b){b=b.origin!=="null"?b.document.cookie:null;return b===null?null:(new UN({cookie:b})).get(a)||""}function aO(a,b,c,d){d.origin!=="null"&&(d.isSecureContext&&(c={...c,gi:"none",Ie:!0}),(new UN(d.document)).set(a,b,c))};function bO(a,b){return Li(a,5,b)}function cO(a,b){return Li(a,8,b)}function dO(a,b){return Li(a,12,b)}function eO(a,b){return Li(a,16,b)}var fO=class extends H{A(){return ti(this,1)!=null}i(){return ti(this,2)!=null}l(){return C(this,3)}g(){return C(this,5)}};var iO=({B:a,Ia:b,Ah:c=!1,Bh:d=!1})=>{if(!gO({B:a,Ia:b,Ah:c,Bh:d}))return hO(a,bO(new fO,!0));b=dI();return(b=iI(b,24))?hO(a,bO(new fO,lJ(b))):qt(Error("tcunav"))};function gO({B:a,Ia:b,Ah:c,Bh:d}){if(!(d=!d&&qJ(new uJ(a)))){if(c=!c){if(b){a=TN(a);if(st(a))if((a=a.getValue())&&wg(y(a,1))!=null)b:switch(a=F(a,1),a){case 1:a=!0;break b;default:throw Error("Unhandled AutoGdprFeatureStatus: "+a);}else a=!1;else lC(806,a.g),a=!1;b=!a}c=b}d=c}return d?!0:!1} 
function hO(a,b){return(a=WN(b,a))?ot(a):qt(Error("unav"))};var jO=class{constructor(a,b,c,d,e){this.g=a;this.l=b;this.A=c;this.i=!1;this.j=d;this.C=e}run(){const a=this.A;if(this.i){var b=this.g;if(this.j&&!YH(a)){var c=new RN;c=Si(c,1,1)}else c=null;if(c){c=Ui(c);try{b.localStorage.setItem("google_auto_fc_cmp_setting",c)}catch(d){}}}b=YH(a)&&(this.j||this.C);a&&b&&(new OI(this.g,new xJ(this.g,this.l),a,new sE(this.g))).run()}};var kO=class extends H{getName(){return F(this,1)}getVersion(){return E(this,3)}};var lO=[0,Ck,-1,wk];var mO=class extends H{Sj(){return F(this,3)}};const nO={"-":0,Y:2,N:1};var oO=class extends H{getVersion(){return ui(this,2)}};function pO(a){return a.includes("~")?a.split("~").slice(1):[]};function qO(a){return Ze(a.length%4!==0?a+"A":a).map(b=>b.toString(2).padStart(8,"0")).join("")}function rO(a){if(!/^[0-1]+$/.test(a))throw Error(`Invalid input [${a}] not a bit string.`);return parseInt(a,2)}function sO(a){if(!/^[0-1]+$/.test(a))throw Error(`Invalid input [${a}] not a bit string.`);const b=[1,2,3,5];let c=0;for(let d=0;d<a.length-1;d++)b.length<=d&&b.push(b[d-1]+b[d-2]),c+=parseInt(a[d],2)*b[d];return c}function tO(a,b){a=qO(a);return a.length<b?a.padEnd(b,"0"):a};function uO(a){var b=qO(a),c=rO(b.slice(0,6));a=rO(b.slice(6,12));var d=new oO;c=Oi(d,1,c);a=Oi(c,2,a);b=b.slice(12);c=rO(b.slice(0,12));d=[];let e=b.slice(12).replace(/0+$/,"");for(let k=0;k<c;k++){if(e.length===0)throw Error(`Found ${k} of ${c} sections [${d}] but reached end of input [${b}]`);var f=rO(e[0])===0;e=e.slice(1);var g=vO(e,b),h=d.length===0?0:d[d.length-1];h=sO(g)+h;e=e.slice(g.length);if(f)d.push(h);else{f=vO(e,b);g=sO(f);for(let l=0;l<=g;l++)d.push(h+l);e=e.slice(f.length)}}if(e.length> 
0)throw Error(`Found ${c} sections [${d}] but has remaining input [${e}], entire input [${b}]`);return ci(a,3,d,xg)}function vO(a,b){const c=a.indexOf("11");if(c===-1)throw Error(`Expected section bitstring but not found in [${a}] part of [${b}]`);return a.slice(0,c+2)};var wO=class extends H{g(){return F(this,1)}i(){return F(this,2)}};var xO=class extends H{};var yO=class extends H{getVersion(){return ui(this,1)}};var zO=class extends H{};function AO(a){var b=new BO;return A(b,1,a)}var BO=class extends H{};const CO=[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2],DO=6+CO.reduce((a,b)=>a+b);var EO=class extends H{};var FO=class extends H{getVersion(){return ui(this,1)}};var GO=class extends H{};function HO(a){var b=new IO;return A(b,1,a)}var IO=class extends H{};const JO=[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2],KO=6+JO.reduce((a,b)=>a+b);var LO=class extends H{g(){return F(this,1)}i(){return F(this,2)}A(){return F(this,3)}};var MO=class extends H{};var NO=class extends H{getVersion(){return ui(this,1)}};var OO=class extends H{};function PO(a){var b=new QO;return A(b,1,a)}var QO=class extends H{};const RO=[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2],SO=6+RO.reduce((a,b)=>a+b);var TO=class extends H{g(){return F(this,1)}i(){return F(this,2)}A(){return F(this,3)}};var UO=class extends H{};var VO=class extends H{getVersion(){return ui(this,1)}};const WO=[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2],XO=6+WO.reduce((a,b)=>a+b);var YO=class extends H{g(){return F(this,1)}i(){return F(this,2)}};var ZO=class extends H{};var $O=class extends H{getVersion(){return ui(this,1)}};var aP=class extends H{};function bP(a){var b=new cP;return A(b,1,a)}var cP=class extends H{};const dP=[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2],eP=6+dP.reduce((a,b)=>a+b);var fP=class extends H{};var gP=class extends H{getVersion(){return ui(this,1)}};const hP=[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2],iP=6+hP.reduce((a,b)=>a+b);var jP=class extends H{};function kP(a,b){return ci(a,1,b,vg)}function lP(a,b){return ci(a,2,b,vg)}function mP(a,b){return ci(a,3,b,xg)}function nP(a,b){ci(a,4,b,xg)}var oP=class extends H{};function pP(a,b){return Pi(a,1,b)}function qP(a){var b=Number;{var c=y(a,1);const d=typeof c;c=c==null?c:d==="bigint"?String(kg(64,c)):ug(c)?d==="string"?Cg(c):Dg(c):void 0}b=b(c??"0");a=ui(a,2);return new Date(b*1E3+a/1E6)}var rP=class extends H{};function sP(a,b){return Oi(a,1,b)}function tP(a,b){return A(a,2,b)}function uP(a,b){return A(a,3,b)}function vP(a,b){return Oi(a,4,b)}function wP(a,b){return Oi(a,5,b)}function xP(a,b){return Oi(a,6,b)}function yP(a,b){return Ri(a,7,b)}function zP(a,b){return Oi(a,8,b)}function AP(a,b){return Oi(a,9,b)}function BP(a,b){return Mi(a,10,b)}function CP(a,b){return Mi(a,11,b)}function DP(a,b){return ci(a,12,b,vg)}function EP(a,b){return ci(a,13,b,vg)}function FP(a,b){return ci(a,14,b,vg)} 
function GP(a,b){return Mi(a,15,b)}function HP(a,b){return Ri(a,16,b)}function IP(a,b){return ci(a,17,b,xg)}function JP(a,b){return ci(a,18,b,xg)}function KP(a,b){return pi(a,19,b)}var LP=class extends H{getVersion(){return ui(this,1)}};var MP=class extends H{};var NP="a".charCodeAt(),OP=Cc(sr),PP=Cc(tr);function QP(a,b){if(a.g+b>a.i.length)throw Error("Requested length "+b+" is past end of string.");const c=a.i.substring(a.g,a.g+b);a.g+=b;return parseInt(c,2)}function RP(a){a=QP(a,36);var b=pP(new rP,Math.floor(a/10));return Oi(b,2,a%10*1E8)}function SP(a){return String.fromCharCode(NP+QP(a,6))+String.fromCharCode(NP+QP(a,6))}function TP(a){let b=QP(a,12);const c=[];for(;b--;){var d=!!QP(a,1)===!0,e=QP(a,16);if(d)for(d=QP(a,16);e<=d;e++)c.push(e);else c.push(e)}c.sort((f,g)=>f-g);return c} 
function UP(a,b,c){const d=[];for(let e=0;e<b;e++)if(QP(a,1)){const f=e+1;if(c&&c.indexOf(f)===-1)throw Error(`ID: ${f} is outside of allowed values!`);d.push(f)}return d}function VP(a){const b=QP(a,16);return!!QP(a,1)===!0?(a=TP(a),a.forEach(c=>{if(c>b)throw Error(`ID ${c} is past MaxVendorId ${b}!`);}),a):UP(a,b)}function WP(a){const b=[];let c=QP(a,12);for(;c--;){const k=QP(a,6);var d=QP(a,2),e=TP(a),f=b,g=f.push;var h=new jP;h=G(h,1,k);d=G(h,2,d);e=ci(d,3,e,xg);g.call(f,e)}return b} 
var XP=class{constructor(a){if(/[^01]/.test(a))throw Error(`Input bitstring ${a} is malformed!`);this.i=a;this.g=0}skip(a){this.g+=a}};var YP=a=>{try{const b=Ze(a).map(f=>f.toString(2).padStart(8,"0")).join(""),c=new XP(b);if(QP(c,3)!==3)return null;const d=lP(kP(new oP,UP(c,24,OP)),UP(c,24,OP)),e=QP(c,6);e!==0&&nP(mP(d,UP(c,e)),UP(c,e));return d}catch(b){return null}};var ZP=a=>{try{const b=Ze(a).map(d=>d.toString(2).padStart(8,"0")).join(""),c=new XP(b);return KP(JP(IP(HP(GP(FP(EP(DP(CP(BP(AP(zP(yP(xP(wP(vP(uP(tP(sP(new LP,QP(c,6)),RP(c)),RP(c)),QP(c,12)),QP(c,12)),QP(c,6)),SP(c)),QP(c,12)),QP(c,6)),!!QP(c,1)),!!QP(c,1)),UP(c,12,PP)),UP(c,24,OP)),UP(c,24,OP)),!!QP(c,1)),SP(c)),VP(c)),VP(c)),WP(c))}catch(b){return null}};var aQ=a=>{if(!a)return null;a=a.split(".");if(a.length>4)return null;var b=ZP(a[0]);if(!b)return null;var c=new MP;b=A(c,1,b);a.shift();for(const d of a)switch($P(d)){case 1:case 2:break;case 3:a=YP(d);if(!a)return null;A(b,2,a);break;default:return null}return b};const $P=a=>{try{const b=Ze(a).map(c=>c.toString(2).padStart(8,"0")).join("");return QP(new XP(b),3)}catch(b){return-1}};const bQ=(a,b)=>{const c={};if(Array.isArray(b)&&b.length!==0)for(const d of b)c[d]=a.indexOf(d)!==-1;else for(const d of a)c[d]=!0;delete c[0];return c};var dQ=(a,b)=>{try{var c=Ze(a.split(".")[0]).map(e=>e.toString(2).padStart(8,"0")).join("");const d=new XP(c);c={};c.tcString=a;c.gdprApplies=b;d.skip(78);c.cmpId=QP(d,12);c.cmpVersion=QP(d,12);d.skip(30);c.tcfPolicyVersion=QP(d,6);c.isServiceSpecific=!!QP(d,1);c.useNonStandardStacks=!!QP(d,1);c.specialFeatureOptins=cQ(UP(d,12,PP),PP);c.purpose={consents:cQ(UP(d,24,OP),OP),legitimateInterests:cQ(UP(d,24,OP),OP)};c.purposeOneTreatment=!!QP(d,1);c.publisherCC=SP(d);c.vendor={consents:cQ(VP(d),null), 
legitimateInterests:cQ(VP(d),null)};return c}catch(d){return null}};const cQ=(a,b)=>{const c={};if(Array.isArray(b)&&b.length!==0)for(const d of b)c[d]=a.indexOf(d)!==-1;else for(const d of a)c[d]=!0;delete c[0];return c};function $q(a,...b){try{const c=encodeURIComponent(We(rn(b,a.i)));a.j(`${"https://pagead2.googlesyndication.com/pagead/ping"}?e=${4}&d=${c}`)}catch(c){qn(c,a.i)}}var eQ=class extends ar{constructor(a){super(7,vq());this.j=a}};var fQ=class extends H{g(){return ti(this,2)!=null}};var gQ=class extends H{A(){return ti(this,2)!=null}};var hQ=class extends H{i(){return ti(this,1)!=null}};var iQ=Hk(class extends H{});function jQ(a){a=kQ(a);try{var b=a?iQ(a):null}catch(c){b=null}return b?z(b,hQ,4)||null:null}function kQ(a){a=(new UN(a)).get("FCCDCF","");if(a)if(a.startsWith("%"))try{var b=decodeURIComponent(a)}catch(c){b=null}else b=a;else b=null;return b};function lQ(a){a.__tcfapiPostMessageReady||mQ(new nQ(a))} 
function mQ(a){a.g=b=>{const c=typeof b.data==="string";let d;try{d=c?JSON.parse(b.data):b.data}catch(f){return}const e=d.__tcfapiCall;e&&(e.command==="ping"||e.command==="addEventListener"||e.command==="removeEventListener")&&(0,a.B.__tcfapi)(e.command,e.version,(f,g)=>{const h={};h.__tcfapiReturn=e.command==="removeEventListener"?{success:f,callId:e.callId}:{returnValue:f,success:g,callId:e.callId};f=c?JSON.stringify(h):h;b.source&&typeof b.source.postMessage==="function"&&b.source.postMessage(f, 
b.origin);return f},e.parameter)};a.B.addEventListener("message",a.g);a.B.__tcfapiPostMessageReady=!0}var nQ=class{constructor(a){this.B=a}};function oQ(a){a.__uspapiPostMessageReady||pQ(new qQ(a))} 
function pQ(a){a.g=b=>{const c=typeof b.data==="string";let d;try{d=c?JSON.parse(b.data):b.data}catch(f){return}const e=d.__uspapiCall;e&&e.command==="getUSPData"&&a.B.__uspapi(e.command,e.version,(f,g)=>{const h={};h.__uspapiReturn={returnValue:f,success:g,callId:e.callId};f=c?JSON.stringify(h):h;b.source&&typeof b.source.postMessage==="function"&&b.source.postMessage(f,b.origin);return f})};a.B.addEventListener("message",a.g);a.B.__uspapiPostMessageReady=!0} 
var qQ=class{constructor(a){this.B=a;this.g=null}};var rQ=class extends H{};var sQ=Hk(class extends H{g(){return ti(this,1)!=null}});function tQ(a,b){function c(n){if(n.length<10)return null;var p=h(n.slice(0,4));p=k(p);n=h(n.slice(6,10));n=l(n);return"1"+p+n+"N"}function d(n){if(n.length<10)return null;var p=h(n.slice(0,6));p=k(p);n=h(n.slice(6,10));n=l(n);return"1"+p+n+"N"}function e(n){if(n.length<12)return null;var p=h(n.slice(0,6));p=k(p);n=h(n.slice(8,12));n=l(n);return"1"+p+n+"N"}function f(n){if(n.length<18)return null;var p=h(n.slice(0,8));p=k(p);n=h(n.slice(12,18));n=l(n);return"1"+p+n+"N"}function g(n){if(n.length<10)return null; 
var p=h(n.slice(0,6));p=k(p);n=h(n.slice(6,10));n=l(n);return"1"+p+n+"N"}function h(n){const p=[];let w=0;for(let u=0;u<n.length/2;u++)p.push(rO(n.slice(w,w+2))),w+=2;return p}function k(n){return n.every(p=>p===1)?"Y":"N"}function l(n){return n.some(p=>p===1)?"Y":"N"}if(a.length===0)return null;a=a.split(".");if(a.length>2)return null;a=qO(a[0]);const m=rO(a.slice(0,6));a=a.slice(6);if(m!==1)return null;switch(b){case 8:return c(a);case 10:case 12:case 9:return d(a);case 11:return e(a);case 7:return f(a); 
case 13:return g(a);default:return null}};function uQ(a){!a.A||a.B.__uspapi||a.B.frames.__uspapiLocator||(a.B.__uspapiManager="fc",XI(a.B,"__uspapiLocator"),Ha("__uspapi",(b,c,d)=>{typeof d==="function"&&b==="getUSPData"&&(b=a.i&&!C(a.j,3),d({version:1,uspString:b?"1---":a.A},!0))},a.B),oQ(a.B))} 
function vQ(a){!a.tcString||a.B.__tcfapi||a.B.frames.__tcfapiLocator||(a.B.__tcfapiManager="fc",XI(a.B,"__tcfapiLocator"),a.B.__tcfapiEventListeners=a.B.__tcfapiEventListeners||[],Ha("__tcfapi",(b,c,d,e)=>{if(typeof d==="function")if(c&&(c>2.2||c<=1))d(null,!1);else{var f=a.B.__tcfapiEventListeners;c=a.i&&!a.j.g();switch(b){case "ping":d({gdprApplies:!c,cmpLoaded:!0,cmpStatus:"loaded",displayStatus:"disabled",apiVersion:"2.2",cmpVersion:2,cmpId:300});break;case "addEventListener":e=f.push(d);b=!c; 
--e;a.tcString?(b=dQ(a.tcString,b),b.addtlConsent=a.g!=null?a.g:void 0,b.cmpStatus="loaded",b.eventStatus="tcloaded",e!=null&&(b.listenerId=e)):b=null;d(b,!0);break;case "removeEventListener":e!==void 0&&f[e]?(f[e]=null,d(!0)):d(!1);break;case "getInAppTCData":case "getVendorList":d(null,!1);break;case "getTCData":d(null,!1)}}},a.B),lQ(a.B))} 
function wQ(a){if(!a?.g()||E(a,1).length===0||mi(a,rQ,2,Th()).length===0)return null;const b=E(a,1);let c;try{var d=uO(b.split("~")[0]);c=pO(b)}catch(e){return null}a=mi(a,rQ,2,Th()).reduce((e,f)=>{var g=xQ(e);g=vi(g,1);g=Uu(g);var h=xQ(f);h=vi(h,1);return g>Uu(h)?e:f});d=xi(d,3).indexOf(ui(a,1));return d===-1||d>=c.length?null:{uspString:tQ(c[d],ui(a,1)),tf:qP(xQ(a))}}function yQ(a){a=a.find(b=>b&&F(b,1)===13);if(a?.g())try{return sQ(E(a,2))}catch(b){}return null} 
function xQ(a){return Oh(a,rP,2)?z(a,rP,2):pP(new rP,0)} 
var zQ=class{constructor(a,b,c){this.B=a;this.j=b;this.i=c;b=kQ(this.B.document);try{var d=b?iQ(b):null}catch(e){d=null}(b=d)?(d=z(b,gQ,5)||null,b=mi(b,fQ,7,Th()),b=yQ(b??[]),d={Wg:d,uh:b}):d={Wg:null,uh:null};b=d;d=wQ(b.uh);b=b.Wg;b?.A()&&E(b,2).length!==0?(c=Oh(b,rP,1)?z(b,rP,1):pP(new rP,0),b={uspString:E(b,2),tf:qP(c)}):b=null;this.A=b&&d?d.tf>b.tf?d.uspString:b.uspString:b?b.uspString:d?d.uspString:null;this.tcString=(d=jQ(a.document))&&d.i()?E(d,1):null;this.g=(a=jQ(a.document))&&ti(a,2)!=null? 
E(a,2):null}};function AQ(){const a=Tb();return a?Xa("AmazonWebAppPlatform;Android TV;Apple TV;AppleTV;BRAVIA;BeyondTV;Freebox;GoogleTV;HbbTV;LongTV;MiBOX;MiTV;NetCast.TV;Netcast;Opera TV;PANASONIC;POV_TV;SMART-TV;SMART_TV;SWTV;Smart TV;SmartTV;TV Store;UnionTV;WebOS".split(";"),b=>Rb(a,b))||Rb(a,"OMI/")&&!Rb(a,"XiaoMi/")?!0:Rb(a,"Presto")&&Rb(a,"Linux")&&!Rb(a,"X11")&&!Rb(a,"Android")&&!Rb(a,"Mobi"):!1};function BQ(a){const b=a[0]/255,c=a[1]/255;a=a[2]/255;return(b<=.03928?b/12.92:Math.pow((b+.055)/1.055,2.4))*.2126+(c<=.03928?c/12.92:Math.pow((c+.055)/1.055,2.4))*.7152+(a<=.03928?a/12.92:Math.pow((a+.055)/1.055,2.4))*.0722}var CQ=(a,b)=>{a=BQ(a);b=BQ(b);return(Math.max(a,b)+.05)/(Math.min(a,b)+.05)};function DQ(a,b,c,d=null){const e=g=>{let h;try{h=JSON.parse(g.data)}catch(k){return}!h||h.googMsgType!==b||d&&/[:|%3A]javascript\(/i.test(g.data)&&!d(h,g)||c(h,g)};Lk(a,"message",e);let f=!1;return()=>{let g=!1;f||(f=!0,g=Mk(a,"message",e));return g}}function EQ(a,b,c,d=null){const e=DQ(a,b,qc(c,()=>e()),d);return e}function FQ(a,b,c,d){c.googMsgType=b;a.postMessage(JSON.stringify(c),d)} 
function GQ(a,b,c,d,e){if(!(e<=0)&&(FQ(a,b,c,d),a=a.frames))for(let f=0;f<a.length;++f)e>1&&GQ(a[f],b,c,d,--e)};function HQ(a,b,c,d){return DQ(a,"fullscreen",d.tb(952,(e,f)=>{if(f.source===b){if(!("eventType"in e))throw Error(`bad message ${JSON.stringify(e)}`);delete e.googMsgType;c(e)}}))};class IQ{constructor(){this.promise=new Promise((a,b)=>{this.resolve=a;this.reject=b})}};async function JQ(a){return a.C.promise}async function KQ(a){return a.j.promise}async function LQ(a){return a.l.promise}function MQ(a,b){b.type="err_st";b.slot=a.slotType;b.freq=.25;a.qem&&(b.qem=a.qem);b.tag_type=a.D.ml;b.version=a.D.version;ln(a.I,"fullscreen_tag",b,!1,.25)} 
class NQ extends Q{constructor(a,b,c){var d=eC,e=cC,f={ml:2,version:vq()};super();this.slotType=a;this.pubWin=b;this.sf=c;this.Aa=d;this.I=e;this.D=f;this.i=1;this.qem=null;this.C=new IQ;this.j=new IQ;this.l=new IQ}init(){const a=HQ(this.pubWin,this.sf,b=>{if(b.eventType==="adError")this.l.resolve(),this.i=4;else if(b.eventType==="adReady"&&this.i===1)this.qem=b.qem,b.slotType!==this.slotType&&(MQ(this,{cur_st:this.i,evt:b.eventType,adp_tp:b.slotType}),this.i=4),this.C.resolve(),this.i=2;else if(b.eventType=== 
"adClosed"&&this.i===2)this.j.resolve(b.result),this.i=3;else if(b.eventType!=="adClosed"||this.i!==3)b.eventType==="adClosed"&&b.closeAfterError&&(this.j.resolve(b.result),this.i=3),MQ(this,{cur_st:this.i,evt:b.eventType}),this.i=4},this.Aa);fs(this,a)}};var OQ=Promise;class PQ{constructor(a){this.j=a}i(a,b,c){this.j.then(d=>{d.i(a,b,c)})}g(a,b){return this.j.then(c=>c.g(a,b))}};class QQ{constructor(a){this.data=a}};function RQ(a,b){SQ(a,b);return new TQ(a)}class TQ{constructor(a){this.j=a}i(a,b,c=[]){const d=new MessageChannel;SQ(d.port1,b);this.j.postMessage(a,[d.port2].concat(c))}g(a,b){return new OQ(c=>{this.i(a,c,b)})}}function SQ(a,b){b&&(a.onmessage=c=>{b(new QQ(c.data,RQ(c.ports[0])))})};var UQ=class{constructor(a){this.g=a}};const VQ=a=>{const b=Object.create(null);(typeof a==="string"?[a]:a).forEach(c=>{if(c==="null")throw Error("Receiving from null origin not allowed without token verification. Please use NullOriginConnector.");b[c]=!0});return c=>b[c]===!0};var XQ=({destination:a,Ca:b,origin:c,lf:d="ZNWN1d",onMessage:e,Th:f})=>WQ({destination:a,Tj:()=>b.contentWindow,Dk:c instanceof UQ?c:typeof c==="function"?new UQ(c):new UQ(VQ(c)),lf:d,onMessage:e,Th:f}); 
const WQ=({destination:a,Tj:b,Dk:c,xp:d,lf:e,onMessage:f,Th:g})=>new PQ(new OQ((h,k)=>{const l=m=>{m.source&&m.source===b()&&c.g(m.origin)&&(m.data.n||m.data)===e&&(a.removeEventListener("message",l,!1),d&&m.data.t!==d?k(Error(`Token mismatch while establishing channel "${e}". Expected ${d}, but received ${m.data.t}.`)):(h(RQ(m.ports[0],f)),g&&g(m)))};a.addEventListener("message",l,!1)}));var YQ=Fk(In);var ZQ=Fk(Jn);var $Q=Fk(Ln);var aR=Fk(Hn);var bR=Fk(Kn);var cR={ym:"google_ads_preview",Vm:"google_mc_lab",fn:"google_anchor_debug",en:"google_bottom_anchor_debug",INTERSTITIAL:"google_ia_debug",En:"google_scr_debug",Hn:"google_ia_debug_allow_onclick",bo:"googleads",Ei:"google_pedestal_debug",zo:"google_responsive_slot_preview",yo:"google_responsive_dummy_ad"},dR={google_bottom_anchor_debug:1,google_anchor_debug:2,google_ia_debug:8,google_scr_debug:9,googleads:2,google_pedestal_debug:30};var eR={INTERSTITIAL:1,BOTTOM_ANCHOR:2,TOP_ANCHOR:3,1:"INTERSTITIAL",2:"BOTTOM_ANCHOR",3:"TOP_ANCHOR"};function fR(a,b){if(!a)return!1;a=a.hash;if(!a||!a.indexOf)return!1;if(a.indexOf(b)!=-1)return!0;b=gR(b);return b!="go"&&a.indexOf(b)!=-1?!0:!1}function gR(a){let b="";be(a.split("_"),c=>{b+=c.substr(0,2)});return b}function hR(){var a=r.location;let b=!1;be(cR,c=>{fR(a,c)&&(b=!0)});return b}function iR(a,b){switch(a){case 1:return fR(b,"google_ia_debug");case 2:return fR(b,"google_bottom_anchor_debug");case 3:return fR(b,"google_anchor_debug")||fR(b,"googleads")}};function jR(a){var b=window;return a.google_adtest==="on"||a.google_adbreak_test==="on"||b.location.host.endsWith("h5games.usercontent.goog")||b.location.host==="gamesnacks.com"?b.document.querySelector('meta[name="h5-games-eids"]')?.getAttribute("content")?.split(",").map(c=>Math.floor(Number(c))).filter(c=>!isNaN(c)&&c>0)||[]:[]};function kR(a,b){b&&!a.g&&(b=lR(b),a.g=b.id,a.j=b.creationTimeSeconds)} 
var mR=class{constructor(){this.A=new Date(Date.now());this.j=this.g=null;this.i={[3]:{},[4]:{},[5]:{}};this.i[3]={[71]:(...a)=>{var b=this.g;var c=this.A,d=Number(a[0]);a=Number(a[1]);b=b!==null?de(`${"w5uHecUBa2S"}:${d}:${b}`)%a===Math.floor(c.valueOf()/864E5)%a:void 0;return b}};this.i[4]={[15]:()=>{var a=Number(this.j||void 0);isNaN(a)?a=void 0:(a=new Date(a*1E3),a=a.getFullYear()*1E4+(a.getMonth()+1)*100+a.getDate());return a}}}},nR;function oR(a,b,c=""){return b&&(pR(a,c,b)?.g()??!1)?!0:qR(a,c,d=>Xa(mi(d,fl,2,Th()),e=>Ki(e,1)===1),O(yx)?!!z(b,ji,26)?.g():C(b,6))}function qR(a,b,c,d){a=Xd(a)||a;const e=rR(a,d);b&&(b=Km(String(b)));return zc(e,(f,g)=>Object.prototype.hasOwnProperty.call(e,g)&&(!b||b===g)&&c(f))}function rR(a,b){a=sR(a,b);const c={};be(a,(d,e)=>{try{const f=Wi(il,uh(d));c[e]=f}catch(f){}});return c}function sR(a,b){a=iO({B:a,Ia:b});return st(a)?tR(a.getValue()):{}} 
function tR(a){try{const b=a.getItem("google_adsense_settings");if(!b)return{};const c=JSON.parse(b);return c!==Object(c)?{}:yc(c,(d,e)=>Object.prototype.hasOwnProperty.call(c,e)&&typeof e==="string"&&Array.isArray(d))}catch(b){return{}}}function pR(a,b,c){if(!b)return null;const d=Ai(c,uR,27,vR)?.i();a=Ai(c,uR,27,vR)?.A()?.g()===b&&a.location.host&&E(c,17)===a.location.host;return d===b||a?Ai(c,uR,27,vR):null};function wR(a=r){return a.ggeac||(a.ggeac={})};function xR(a,b=document){return!!b.featurePolicy?.features().includes(a)}function yR(a,b=document){return!!b.featurePolicy?.allowedFeatures().includes(a)}function zR(a=navigator){try{return!!a.protectedAudience?.queryFeatureSupport?.("deprecatedRenderURLReplacements")}catch(b){return!1}}function AR(a,b,c=b.document){return!!(a&&"sharedStorage"in b&&b.sharedStorage&&yR("shared-storage",c)&&yR("shared-storage-select-url",c))};function BR(a=ae()){return b=>de(`${b} + ${a}`)%1E3};function CR(a,b){a.g=ir(14,b,()=>{})}class DR{constructor(){this.g=()=>{}}}function ER(a){N(DR).g(a)};function FR(a=wR()){jr(N(kr),a);GR(a);CR(N(DR),a);N(Lx).i()}function GR(a){const b=N(Lx);b.j=(c,d)=>ir(5,a,()=>!1)(c,d,1);b.l=(c,d)=>ir(6,a,()=>0)(c,d,1);b.C=(c,d)=>ir(7,a,()=>"")(c,d,1);b.g=(c,d)=>ir(8,a,()=>[])(c,d,1);b.A=(c,d)=>ir(17,a,()=>[])(c,d,1);b.i=()=>{ir(15,a,()=>{})(1)}};function lR(a){var b=a.split(":");a=b.find(c=>c.indexOf("ID=")===0)||null;b=b.find(c=>c.indexOf("T=")===0)?.substring(2)||null;return{id:a,creationTimeSeconds:b}}function HR(a,b,c){c?(a=a.B,b=c.g()?$N(b,a):null):b=null;return b}function IR(a,b,c,d){if(d){var e=Uu(vi(c,2))-Date.now()/1E3;e={ie:Math.max(e,0),path:E(c,3),domain:E(c,4),Ie:!1};c=c.getValue();a=a.B;d.g()&&aO(b,c,e,a)}} 
function JR(a,b,c){var d;(d=!c)||(d=a.B,d=!(c.g()&&$N(b,d)));if(!d)for(const f of KR(a.B.location.hostname)){d=b;var e=a.B;c.g()&&e.origin!=="null"&&VN(new UN(e.document),d,"/",f)}}var LR=class{constructor(a){this.B=a}};function KR(a){if(a==="localhost")return["localhost"];a=a.split(".");if(a.length<2)return[];const b=[];for(let c=0;c<a.length-1;++c)b.push(a.slice(c).join("."));return b};function MR(a,b,c){var d={[0]:BR(Ge(b).toString())};if(c){c=HR(new LR(b),"__gads",c)||"";nR||(nR=new mR);b=nR;kR(b,c);ER(b.i);const e=(new RegExp(/(?:^|:)(ID=[^\s:]+)/)).exec(c)?.[1];d[1]=f=>e?BR(e)(f):void 0}d=lr(a,d);qr(GI(N(EI),a,d))}function NR(a){const b=N(kr).g();a=jR(a);return b.concat(a).join(",")}function OR(a){const b=Tm();b&&(a.debug_experiment_id=b)};function PR(a,b){if(a&&!pI(a).ads_density_stats_processed&&!Yl(a)&&(pI(a).ads_density_stats_processed=!0,O(jw)||ae()<.01)){const c=()=>{if(a){var d=yM(tM(a),b.google_ad_client,a.location.hostname,NR(b).split(","));kC("ama_stats",d,1)}};He(a,()=>{r.setTimeout(c,1E3)})}};function QR(a,b,c,d,e,f=null){if(e){if(O(Mv))var g=null;else try{g=e.getItem("google_ama_config")}catch(l){g=null}try{var h=g?Iu(g):null}catch(l){h=null}}else h=null;a:{if(d)try{var k=Iu(d);break a}catch(l){tN(a,{cfg:1,inv:1})}k=null}if(d=k){if(e){k=new Xt;A(d,3,k);h=Tu(d?.g()?.i())||1;h=Date.now()+864E5*h;Number.isFinite(h)&&Mh(k,1,Hg(Math.round(h)));k=Fh(d);d.g()&&(h=new Wt,g=d?.g()?.g(),h=Li(h,23,g),g=d?.g()?.A(),h=Li(h,12,g),A(k,15,h));h=mi(k,vu,1,Th());for(g=0;g<h.length;g++)Mh(h[g],11);Mh(k, 
22);if(O(Mv))BN(a,e);else try{e.setItem("google_ama_config",Ui(k))}catch(l){tN(a,{lserr:1})}}e=zN(a,mi(d,gu,7,Th()));k={};O(Nv)||(k.Ok=z(d,pu,8)||new pu);e&&(k.ca=e);e&&yN(e,3)&&(k.Vc=[1]);e=k;qI(a,2)&&(Kl(5,[th(d)]),c=uN(c),k=(k=e.ca)&&Ji(k,4)||"",c.google_package=k,DN(a,b,d,e,new Nt(["google-auto-placed"],c),f));return!0}h&&(tN(a,{cfg:1,cl:1}),e!=null&&BN(a,e));return!1};function RR(a,b){b=b&&b[0];if(!b)return null;b=b.target;const c=b.getBoundingClientRect(),d=hm(a.g.da()||window);if(c.bottom<=0||c.bottom>d.height||c.right<=0||c.left>=d.width)return null;var e=SR(a,b,c,a.g.g.elementsFromPoint(Ll(c.left+c.width/2,c.left,c.right-1),Ll(c.bottom-1-2,c.top,c.bottom-1)),1,[]),f=SR(a,b,c,a.g.g.elementsFromPoint(Ll(c.left+c.width/2,c.left,c.right-1),Ll(c.top+2,c.top,c.bottom-1)),2,e.Gb),g=SR(a,b,c,a.g.g.elementsFromPoint(Ll(c.left+2,c.left,c.right-1),Ll(c.top+c.height/2, 
c.top,c.bottom-1)),3,[...e.Gb,...f.Gb]);const h=SR(a,b,c,a.g.g.elementsFromPoint(Ll(c.right-1-2,c.left,c.right-1),Ll(c.top+c.height/2,c.top,c.bottom-1)),4,[...e.Gb,...f.Gb,...g.Gb]);var k=TR(a,b,c),l=n=>Za(a.j,n.Ob)&&Za(a.A,n.mg)&&Za(a.i,n.Vh);e=Qa([...e.entries,...f.entries,...g.entries,...h.entries],l);l=Qa(k,l);k=[...e,...l];f=c.left<-2||c.right>d.width+2;f=k.length>0||f;g=im(a.g.g);const m=new Ul(c.left,c.top,c.width,c.height);e=[...Va(e,n=>new Ul(n.Sc.left,n.Sc.top,n.Sc.width,n.Sc.height)),...kb(Va(l, 
n=>Wl(m,n.Sc))),...Qa(Wl(m,new Ul(0,0,d.width,d.height)),n=>n.top>=0&&n.top+n.height<=d.height)];return{entries:k,Fh:f,hi:{scrollX:g.x,scrollY:g.y},target:b,Gc:c,wi:{width:d.width,height:d.height},Ek:e.length<20?UR(m,e):VR(c,e)}} 
function WR(a,b){const c=a.g.da(),d=a.g.g;return new Promise((e,f)=>{const g=c.IntersectionObserver;if(g)if(d.elementsFromPoint)if(d.createNodeIterator)if(d.createRange)if(c.Range.prototype.getBoundingClientRect){var h=new g(k=>{const l=new cn,m=bn(l,()=>RR(a,k));m&&(l.i.length&&(m.Gj=Math.round(Number(l.i[0].duration))),h.disconnect(),e(m))},YR);h.observe(b)}else f(Error("5"));else f(Error("4"));else f(Error("3"));else f(Error("2"));else f(Error("1"))})} 
function SR(a,b,c,d,e,f){if(c.width===0||c.height===0)return{entries:[],Gb:[]};const g=[],h=[];for(let m=0;m<d.length;m++){const n=d[m];if(n===b)continue;if(Za(f,n))continue;h.push(n);const p=n.getBoundingClientRect();if(a.g.contains(n,b)){g.push(ZR(a,c,n,p,1,e));continue}if(a.g.contains(b,n)){g.push(ZR(a,c,n,p,2,e));continue}a:{var k=a;var l=b;const t=k.g.Lj(l,n);if(!t){k=null;break a}const {Ja:B,hc:I}=$R(k,l,t,n)||{},{Ja:U,hc:M}=$R(k,n,t,l)||{};k=B&&I&&U&&M?I<=M?{Ja:B,Ob:aS[I]}:{Ja:U,Ob:bS[M]}: 
B&&I?{Ja:B,Ob:aS[I]}:U&&M?{Ja:U,Ob:bS[M]}:null}const {Ja:w,Ob:u}=k||{};w&&u?g.push(ZR(a,c,n,p,u,e,w)):g.push(ZR(a,c,n,p,9,e))}return{entries:g,Gb:h}} 
function TR(a,b,c){const d=[];for(b=b.parentElement;b;b=b.parentElement){const f=b.getBoundingClientRect();if(f){var e=$d(b,a.g.da());e&&e.overflow!=="visible"&&(e.overflowY!=="auto"&&e.overflowY!=="scroll"&&c.bottom>f.bottom+2?d.push(ZR(a,c,b,f,5,1)):(e=e.overflowX==="auto"||e.overflowX==="scroll",!e&&c.left<f.left-2?d.push(ZR(a,c,b,f,5,3)):!e&&c.right>f.right+2&&d.push(ZR(a,c,b,f,5,4))))}}return d} 
function UR(a,b){if(a.width===0||a.height===0||b.length===0)return 0;let c=0;for(let d=1;d<1<<b.length;d++){let e=a,f=0;for(let g=0;g<b.length&&(!(d&1<<g)||(f++,e=Vl(e,b[g]),e));g++);e&&(c=f%2===1?c+(e.width+1)*(e.height+1):c-(e.width+1)*(e.height+1))}return c/((a.width+1)*(a.height+1))} 
function VR(a,b){if(a.width===0||a.height===0||b.length===0)return 0;let c=0;for(let d=a.left;d<=a.right;d++)for(let e=a.top;e<=a.bottom;e++)for(let f=0;f<b.length;f++)if(d>=b[f].left&&d<=b[f].left+b[f].width&&e>=b[f].top&&e<=b[f].top+b[f].height){c++;break}return c/((a.width+1)*(a.height+1))} 
function ZR(a,b,c,d,e,f,g){g={element:c,Sc:d,Ob:e,Vh:f,mg:null,Ja:g||null};if(Za(a.j,e)&&Za(a.i,f)){b=new Pl(b.top,b.right-1,b.bottom-1,b.left);if((a=cS(a,c))&&Rl(b,a))c=4;else{a=dS(c,d);e=xm(c,"paddingLeft");f=xm(c,"paddingRight");const h=xm(c,"paddingTop"),k=xm(c,"paddingBottom");e=new Pl(parseFloat(h),parseFloat(f),parseFloat(k),parseFloat(e));Rl(b,new Pl(a.top+e.top,a.right-e.right,a.bottom-e.bottom,a.left+e.left))?c=3:(c=dS(c,d),c=Rl(b,c)?2:1)}g.mg=c}return g} 
function $R(a,b,c,d){const e=[];for(var f=b;f&&f!==c;f=f.parentElement)e.unshift(f);c=a.g.da();for(f=0;f<e.length;f++){const h=e[f];var g=$d(h,c);if(g){if(g.position==="fixed")return{Ja:h,hc:1};if(g.position==="sticky"&&a.g.contains(h.parentElement,d))return{Ja:h,hc:2};if(g.position==="absolute")return{Ja:h,hc:3};if(g.cssFloat!=="none"){g=h===e[0];const k=eS(a,e.slice(0,f),h);if(g||k)return{Ja:h,hc:4}}}}return(a=eS(a,e,b))?{Ja:a,hc:5}:null} 
function eS(a,b,c){const d=c.getBoundingClientRect();if(!d)return null;for(let e=0;e<b.length;e++){const f=b[e];if(!a.g.contains(f,c))continue;const g=f.getBoundingClientRect();if(!g)continue;const h=$d(f,a.g.da());if(h&&d.bottom>g.bottom+2&&h.overflowY==="visible")return f}return null} 
function cS(a,b){var c=a.g.g;a=c.createRange();if(!a)return null;c=c.createNodeIterator(b,NodeFilter.SHOW_TEXT,{acceptNode:d=>d.nodeValue===null||/^[\s\xa0]*$/.test(d.nodeValue)?NodeFilter.FILTER_SKIP:NodeFilter.FILTER_ACCEPT});for(b=c.nextNode();c.nextNode(););c=c.previousNode();if(!b||!c)return null;a.setStartBefore(b);a.setEndAfter(c);a=a.getBoundingClientRect();return a.width===0||a.height===0?null:new Pl(a.top,a.right,a.bottom,a.left)} 
function dS(a,b){var c=xm(a,"borderLeftWidth");const d=xm(a,"borderRightWidth"),e=xm(a,"borderTopWidth");a=xm(a,"borderBottomWidth");c=new Pl(parseFloat(e),parseFloat(d),parseFloat(a),parseFloat(c));return new Pl(b.top+c.top,b.right-1-c.right,b.bottom-1-c.bottom,b.left+c.left)}var gS=class{constructor(a){var b=fS;this.j=[5,8,9];this.A=[3,4];this.i=b;this.g=em(a)}}; 
const fS=[1,2,3,4],aS={[1]:3,[4]:10,[3]:12,[2]:4,[5]:5},bS={[1]:6,[4]:11,[3]:13,[2]:7,[5]:8},YR={threshold:[0,.25,.5,.75,.95,.96,.97,.98,.99,1]};function hS(a){a.i!=null||a.A||(a.i=new MutationObserver(b=>{for(const c of b)for(const d of c.addedNodes)ta(d)&&d.nodeType==1&&(b=a,d.matches('A[href]:not([href=""])')&&xs(b.j,d))}),a.i.observe(a.B.document.documentElement,{childList:!0,subtree:!0}))}var iS=class extends Q{constructor(a){super();this.B=a;this.j=new ys;this.i=null;fs(this,()=>{this.i?.disconnect();this.i=null})}};function jS(a,b){b.addEventListener("click",()=>{var c=a.g;var d=b.getAttribute("href");c=d?d==="#"?ot(Ro(4)):d.startsWith("#")?ot(Ro(5)):kS(d,c):qt(Error("Empty href"));if(st(c)){d=c.getValue();c=a.U;var e=new To;d=A(e,1,d);c.call(a,d)}else a.i(c.g)})}var mS=class{constructor(a,b,c){var d=lS();this.B=a;this.g=b;this.U=c;this.i=d}init(){const a=new iS(this.B);Array.from(a.B.document.querySelectorAll('A[href]:not([href=""])')).forEach(b=>{jS(this,b)});hS(a);vs(a.j).listen(b=>{jS(this,b)})}}; 
function kS(a,b){return nS(a,b).map(c=>nS(b).map(d=>{if(c.protocol==="http:"||c.protocol==="https:"){var e=Ro(2);e=Ri(e,2,`${c.host}${c.pathname}`);d=Ri(e,3,`${d.host}${d.pathname}`)}else d=c.protocol==="javascript:"?Ro(3):Ro(1);return d}))}function nS(a,b){return vt(rt(()=>new URL(a,b)),()=>Error("Invalid URL"))};function oS(a){if(a<0||!Number.isInteger(a))return qt(Error(`Not a non-negative integer: ${a}`));const b=[];do b.push("ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_".charAt(a%64)),a=Math.floor(a/64);while(a>0);return ot(b.reverse().join(""))};class pS{constructor(){this.Di=5E3}mj(){return 5E3}}function qS(a,b){return a.quantizer?Math.floor(b/5E3)*5E3/a.quantizer.Di:b}function rS(a,b){b=b.map(c=>qS(a,c));return sS(b,a.g===void 0?void 0:qS(a,a.g)).map(c=>{a:{var d=tS;const e=[];for(const f of c){c=d(f);if(!st(c)){d=qt(c.g);break a}e.push(c.getValue())}d=ot(e)}return d}).map(c=>c.join(".")).map(c=>uS(c,a.quantizer?.mj()))}var vS=class{constructor(a,b){this.quantizer=a;this.g=b}}; 
function tS(a){const b=oS(a.value);if(!st(b))return b;const c=b.getValue();return a.Fe===1?ot(`${c}`):a.Fe===2?ot(`${c}${"~"}`):xt(oS(a.Fe-2),d=>{throw d;}).map(d=>`${c}${"~"}${d}`)}function sS(a,b){const c=[];for(let d=0;d<a.length;d++){const e=a[d]??b;if(e===void 0)return qt(Error("Sparse but no default"));c.length===0||e!==c[c.length-1].value?c.push({value:e,Fe:1}):c[c.length-1].Fe++}return ot(c)}function uS(a,b){return a===""?ot(""):wS(b).map(c=>`${c}${a}`)} 
function wS(a){return a===void 0||a===1?ot(""):wt(oS(a),"ComFactor: ").map(b=>`${"~"}${b}${"."}`)};var xS=class extends Q{constructor(a){super();this.B=a;this.j=new R(!1);this.i=()=>{this.j.g(this.B.document.hasFocus())}}init(){this.B.addEventListener("focus",this.i);this.B.addEventListener("blur",this.i);fs(this,()=>void this.B.removeEventListener("focus",this.i));fs(this,()=>void this.B.removeEventListener("blur",this.i));this.j.g(this.B.document.hasFocus())}};function yS(a){a.j.g(a.B.document.visibilityState==="visible")}var zS=class extends Q{constructor(a){super();this.B=a;this.j=new R(!1);this.i=()=>void yS(this)}init(){this.B.addEventListener("visibilitychange",this.i);fs(this,()=>void this.B.removeEventListener("visibilitychange",this.i));yS(this)}};function AS(a){return a.g!==null?a.i+a.j()-a.g:a.i}var CS=class{constructor(a){this.B=a;this.i=0;this.g=null;this.j=BS(this.B)}start(){this.g===null&&(this.g=this.j())}};function BS(a){return a.performance&&a.performance.now?()=>a.performance.now():()=>Date.now()};function DS(a){a=new ES(a);a.init();return a}function FS(a){const b=Js(a.B,1E3,()=>void a.handleEvent());a.B.addEventListener("scroll",()=>void b())}function GS(a){const b=HS(a.B),c=()=>{const d=HS(a.B),e=Math.abs(d.height-b.height);if(Math.abs(d.width-b.width)>20||e>20)a.G=!0,a.B.removeEventListener("resize",c)};a.B.addEventListener("resize",c)}function IS(a){a.A=!a.g.O;qs(a.g,!1,()=>{a.B.setTimeout(()=>{a.A=!0},100)})} 
function JS(a){ps(a.g,!0,()=>void a.j.start());ps(a.g,!1,()=>{var b=a.j;b.g!==null&&(b.i+=b.j()-b.g);b.g=null});a.D.start()} 
function KS(a){var b=a.B.scrollY;var c=Cr(a.B);b={Me:Math.floor(b/100),Qd:Math.floor((b+c)/100),oi:a.B.performance.now()};if(b.Me<0||b.Qd<0||b.Me>1E3||b.Qd>1E3)a.H=!0,a.i=null;else{if(a.i){c=a.i;var d=new uG(c.Me,c.Qd),e=new uG(b.Me,b.Qd);var f=Math.max(d.start,e.start);d=Math.min(d.end,e.end);if(f=f<=d?new uG(f,d):null)for(c=b.oi-c.oi,d=f.start;d<=f.end;d++)a.C[d]=(a.C[d]??0)+c}a.i=a.l.O?b:null}} 
var ES=class{constructor(a){this.B=a;this.C=[];this.G=this.A=this.H=!1;this.i=null;var b=this.B;a=new xS(b);a.init();a=ms(a.j);b=new zS(b);b.init();b=ms(b.j);this.l=this.g=ls(a,b);this.j=new CS(this.B);this.D=new CS(this.B);this.J=new vS((new vS(new pS)).quantizer,0)}init(){FS(this);GS(this);IS(this);JS(this);this.l.listen(()=>void KS(this));r.setInterval(()=>void this.handleEvent(),5E3);this.handleEvent()}handleEvent(){this.l.O&&KS(this)}};function HS(a){return new Sl(Br(a),Cr(a))};function LS(a,{Ia:b}){a=new MS(a,b);if(!a.Ia&&O(ow)){b=a.B;var c=NS(OS(a));(new mS(b,b.document.baseURI,c)).init()}PS(a)} 
function PS(a){if(O(pw)){var b=DS(a.B);fr(new vI(a.B),QS(()=>{var c=OS(a),d=new Wo,e=rS(b.J,b.C);if(!st(e))throw wt(e,"PVDC: ").g;var f=new Vo;f=Oi(f,2,5E3);f=Oi(f,1,100);e=e.getValue();e=Ri(f,3,e);f=HS(b.B);var g=new Uo;g=Oi(g,1,f.width);f=Oi(g,2,f.height);e=A(e,4,f);f=new Uo;f=Oi(f,1,Gr(b.B).scrollWidth);f=Oi(f,2,Gr(b.B).scrollHeight);e=A(e,5,f);e=Mi(e,6,b.A);f=Math.round(AS(b.D)/1E3);e=Oi(e,8,f);f=Math.round(AS(b.j)/1E3);e=Oi(e,9,f);b.H&&qi(e,7,vg,1,wg);b.G&&qi(e,7,vg,2,wg);d=oi(d,2,Xo,e);c(d)}))}} 
function OS(a){if(!a.U){const b=N(EI);a.U=c=>{LI(b,c)}}return a.U}var MS=class{constructor(a,b){this.B=a;this.Ia=b;this.U=null}};function NS(a){return b=>{var c=new Wo;b=oi(c,1,Xo,b);return void a(b)}}function lS(){return a=>{lC(1243,a,void 0,RS("LCC"))}}function QS(a){return()=>void hC(1243,a,RS("PVC"))}function RS(a){return b=>{b.errSrc=a}};var SS=class extends Q{constructor(a,b){super();this.value=a;fs(this,b)}};function TS(a,b){const c=US(a.getBoundingClientRect()),d=new R(c),e=VS(a,b,f=>{d.g(US(f.boundingClientRect))});return new SS(ms(d),()=>void e.disconnect())}function VS(a,b,c){b=new IntersectionObserver(d=>{d.filter(e=>e.target===a).forEach(c)},{root:b});b.observe(a);return b}function US(a){return a.height>0||a.width>0};var WS={Gn:0,Xo:1,uo:2,0:"INITIAL_RENDER",1:"UNRENDER",2:"RENDER_BACK"};function XS(a,b,c){var d=[1,2];const e=TS(b,c),f=e.value,g=new ys;qs(f,!0,()=>void YS(a,f,g,d));return new SS(vs(g),()=>void e.dispose())}function YS(a,b,c,d){const e=new CS(a);let f=new CS(a);e.start();f.start();let g=0;const h=k=>{k={type:k,Sh:++g,vk:AS(f),uk:AS(e)};f=new CS(a);f.start();return k};d&&!d.includes(0)||xs(c,h(0));b.listen(k=>{k=k?2:1;d&&!d.includes(k)||xs(c,h(k))})};function ZS(a,b){var c=N(EI);hC(1282,()=>void $S(a,b,c))}function $S(a,b,c){const d=aT(a);if(!d)throw Error("No adsbygoogle INS found");const e=XS(a.pubWin,b,d);e.value.listen(f=>{bT(f,d,c,()=>void e.dispose())})}function aT(a){return(a=a.ba.parentElement)&&py.test(a.className)?a:null} 
function bT(a,b,c,d){if(a.Sh>5)d();else{var e=a.type===1;d=a.type===2;if(!e&&!d)throw Error(`Unsupported event type: ${WS[a.type]}`);var f=Ti($Q());f=Mh(f,1,Hg(a.vk));f=Mh(f,2,Hg(a.uk));a=Mh(f,3,Hg(a.Sh));f=b.dataset.vignetteLoaded;var g=Ti(YQ());g=Qi(g,1,b.dataset.adStatus);g=Qi(g,2,b.dataset.sideRailStatus);g=Qi(g,3,b.dataset.anchorStatus);f=Li(g,4,f!==void 0?f==="true":void 0);b=getComputedStyle(b);g=Ti(aR());g=Qi(g,1,b.display);g=Qi(g,2,b.position);g=Qi(g,3,b.width);b=Qi(g,4,b.height);b=Gh(b); 
b=A(f,5,b);b=Gh(b);a=A(a,4,b);e=e?bR():void 0;e=oi(a,5,Mn,e);d=d?ZQ():void 0;d=oi(e,6,Mn,d);d=Vi(Ti(Gh(d)));NI(c,d)}};var cT=class extends Error{constructor(a){super(a)}};class dT{constructor(){this.g=!1}}function eT(a,b){a.g||(a.g=!0,a.A=b,a.i.resolve(b))}class fT extends dT{constructor(){super(...arguments);this.i=new IQ}get promise(){return this.i.promise}get ei(){return this.g}get error(){return this.j}}var gT=class extends fT{setError(a,b){this.g||(this.g=!0,this.A=null,this.j=a,b&&b(this.j),this.i.reject(a))}}; 
class hT extends dT{constructor(a){super();this.i=a}get error(){return this.i.j}ei(){return this.i.g}}var iT=class extends hT{constructor(a){super(a);this.i=a}get value(){return this.i.A??null}},jT=class extends fT{notify(){eT(this,null)}};function kT(a,b){a.i.push({Fd:!1,wf:b})}var lT=class extends Q{constructor(){super(...arguments);this.j=[];this.i=[];this.l=[]}Fd(a){const b=this.i.find(c=>c.wf===a);b&&(b.Fd=!0)}g(){this.j.length=0;this.l.length=0;this.i.length=0;super.g()}};async function mT(a,b){const c=b?a.filter(d=>!d.Fd):a;q(await q(Promise.all(c.map(({wf:d})=>d.promise))));a.length!==c.length&&(a=a.filter(d=>d.Fd),q(await q(Promise.race([Promise.all(a.map(({wf:d})=>d.promise)),new Promise(d=>void setTimeout(d,b))]))))} 
var oT=class extends Q{constructor(a,b){super();this.id=a;this.D=b;this.timeoutMs=void 0;this.l=!1;this.i=new lT;es(this,this.i)}async start(){if(!this.l){this.l=!0;try{if(q(await q(mT(this.i.i,this.M??this.timeoutMs))),!this.A){var a=0;for(const d of this.i.l){if(d.i.A==null)throw Error(`missing input: ${this.id}/${a}`);++a}var b=this.f;a={};for(const [d,e]of Object.entries(this.G))a[d]=e.value;const c=b.call(this,a,...this.J);nT(this,c)}}catch(c){this.A||(c instanceof cT?this.C(c):c instanceof Error&& 
(this.D.fe({methodName:this.id,Kd:c}),this.j(c)))}}}C(){}j(a){if(this.i.j.length){var b=new cT(a.message);for(const e of this.i.j)if(!e.ei){var c=e,d=b;c.g=!0;c.j=d;c.i.reject(d)}}a instanceof cT||console?.error(a)}};function pT(a,b){if(a.l)throw Error("Invalid operation: producer has already started");kT(a.i,b);return a}var qT=class extends oT{constructor(a,b,c,d,e){super(a,c);this.f=b;this.J=e;a={};for(const [f,g]of Object.entries(d))if(d=g)kT(this.i,d),a[f]=new iT(d);this.G=a}C(a){this.j(a)}reportError(){}};function nT(a,b){for(const [c,d]of Object.entries(b)){b=c;const e=d;e instanceof Error&&a[b].setError(e);eT(a[b],e)}a.finished.notify()}class rT extends qT{constructor(a,b,c,d,e,f,g){super(a,b,c,d,g);this.eb=f;this.finished=new jT;a=Object.keys(e);for(const h of a)a=new gT,this.i.j.push(a),this[h]=a}j(a){this.eb?nT(this,this.eb(a)):super.j(a)}}function sT(a,b){a.id=b.id;a.Nb=b.Nb;a.eb=b.eb;return a}function tT(a,b,c,...d){return new rT(a.id,a,b,c,a.Nb,a.eb,d)};var uT=sT(function(a,b,c,d){const e=a.Ea,f=a.Z,g=a.pageState;a=g.g();const h=b(e,f,ui(a,1),a.g(),E(a,9));d.google_sa_impl=k=>c({Z:f,Ma:h,Ea:e,slot:k,pageState:g});d.google_process_slots?.();return{}},{id:1338,Nb:{}});function vT({Zg:a,ji:b}){return a||(b==="dev"?"dev":"")};function wT(a){eC.j(b=>{b.shv=String(a);b.mjsv=vT({Zg:vq(),ji:a});b.eid=NR(r)})};var xT=sT(function(a,b){a=a.pageState?.g()?.g()||E(a.Z,2);wT(a);FR(wR(b));return{}},{id:1337,Nb:{}});var yT=sT(function(a,b){let c=null;c=c??new hr(b);try{mf(d=>{WB(c,1192,d)})}catch(d){}return{}},{id:1335,Nb:{}});var ji=class extends H{g(){return C(this,1)}};function zT(a){return C(a,4)}function AT(a){return si(a,4)!=null}function BT(a){return C(a,6)}function CT(a){return si(a,6)!=null}function DT(a){return E(a,8)}var ET=class extends H{g(){return E(this,3)}};var FT=class extends H{g(){return ki(this,ET,1)}},GT=Hk(FT);var HT=class extends H{g(){return E(this,1)}};var uR=class extends H{i(){return E(this,1)}A(){return z(this,HT,2)}g(){return C(this,3)}};var IT=class extends H{i(){return C(this,1)}g(){return z(this,wu,2)}};var JT=class extends H{},vR=[27,28];var KT=typeof sttc==="undefined"?void 0:sttc;var LT=sT(function(a,b,c,d){try{const g=c.adsbygoogle.pageState;nb(g);var e=GT(g)}catch(g){e=new FT}a=e;c=a.g();a:{e=eC;try{if(nb(b),b.length>0){var f=new JT(JSON.parse(b));break a}}catch(g){e.ma(838,g instanceof Error?g:Error(String(g)))}f=new JT}b=f;Kl(16,[3,th(b)]);d=vT({Zg:d,ji:c.g()||E(b,2)});return{pageState:a,Z:b,Ea:d}},{id:1336,Nb:{pageState:void 0,Z:void 0,Ea:void 0}});var MT=sT(function(a,b){a=(b.Prototype||{}).Version;a!=null&&kC("prtpjs",{version:a});return{}},{id:1339,Nb:{}});function NT(a,b,c,...d){b=tT(b,a.J,c,...d);es(a,b);a.l.push(b);return b}async function OT(a){a.i.length&&q(await q(Promise.all(a.i.map(d=>d.C.promise))));for(var b of a.l)b.start();for(var c of a.G)OT(c);if(a.j&&(b=Object.keys(a.j),b.length)){c=q(await q(Promise.all(Object.values(a.j).map(e=>e.promise))));let d=0;for(const e of b)a.D[e]=c[d++]}a.C.resolve(a.D)} 
var PT=class extends Q{constructor(a){super();this.J=a;this.l=[];this.G=[];this.D={};this.i=[];this.C=new IQ;this.j={}}run(){OT(this)}g(){super.g();this.l.length=0;this.G.length=0;this.i.length=0}};function QT(a,b,c){var d=eC,e=RT;const f=new PT({fe:m=>{const n=m.Kd;d.ma(m.methodName??0,n instanceof Error?n:Error(String(n)))}}),g=NT(f,yT,{},b),{pageState:h,Z:k,Ea:l}=pT(NT(f,LT,{},a,r,b),g.finished);a=NT(f,xT,{Z:k,pageState:h},r);c=pT(NT(f,uT,{Ea:l,Z:k,pageState:h},c,e,r),a.finished);pT(NT(f,MT,{},r),c.finished);f.run()};var TT=a=>{const b=a.F;b.google_ad_output==null&&(b.google_ad_output="html");b.google_ad_client!=null&&(b.google_ad_client=Km(String(b.google_ad_client)));b.google_ad_slot!=null&&(b.google_ad_slot=String(b.google_ad_slot));b.google_webgl_support=!!Cl.WebGLRenderingContext;b.google_ad_section=b.google_ad_section||b.google_ad_region||"";b.google_country=b.google_country||b.google_gl||"";const c=(new Date).getTime();Array.isArray(b.google_color_bg)&&(b.google_color_bg=ST(a,b.google_color_bg,c));Array.isArray(b.google_color_text)&& 
(b.google_color_text=ST(a,b.google_color_text,c));Array.isArray(b.google_color_link)&&(b.google_color_link=ST(a,b.google_color_link,c));Array.isArray(b.google_color_url)&&(b.google_color_url=ST(a,b.google_color_url,c));Array.isArray(b.google_color_border)&&(b.google_color_border=ST(a,b.google_color_border,c));Array.isArray(b.google_color_line)&&(b.google_color_line=ST(a,b.google_color_line,c))};function ST(a,b,c){a.g|=2;return b[c%b.length]};const UT={google:1,googlegroups:1,gmail:1,googlemail:1,googleimages:1,googleprint:1};Rd`https://securepubads.g.doubleclick.net/pagead/js/car.js`;Rd`https://securepubads.g.doubleclick.net/pagead/js/cocar.js`;var VT=Rd`https://ep3.adtrafficquality.google/ivt/worklet/caw.js`;function WT(a){const b=[];for(let c=0;c<8;++c){const d=new eQ(f=>{b.push({url:f})}),e=Zp(Yp(Xp(new $p,a),c));d.D(e)}return b}function XT(a,b=()=>{}){var c=VT;const d=window;d.sharedStorage&&!d.clientAgeRequested&&(d.sharedStorage.createWorklet(c.toString(),{dataOrigin:"script-origin"}).then(e=>{YT(e,d,a,b)}).catch(b),d.clientAgeRequested=!0)} 
function YT(a,b,c,d=()=>{}){a.selectURL("ps_caus",WT(c),{resolveToConfig:!0,savedQuery:"ps_cac"}).then(e=>{if(e){var f=b.document.body;const g=document.createElement("fencedframe");g.id="ps_caff";g.name="ps_caff";g.mode="opaque-ads";g.config=e;tm(g,"display","none");f.appendChild(g)}}).catch(d)};function ZT(a,b){const c=AR(a.isSecureContext,a,a.document),d=!!a.sharedStorage?.createWorklet;b&&c&&d&&!iI(dI(),34,!1)&&(jI(dI(),34,!0),XT(Ge(a),e=>{lC(1279,e)}))};const $T=(a,b)=>{b=b.listener;(a=(0,a.__gpp)("addEventListener",b))&&b(a,!0)},aU=(a,b)=>{(0,a.__gpp)("removeEventListener",b.listener,b.listenerId)},bU={ge:a=>a.listener,hd:(a,b)=>({__gppCall:{callId:b,command:"addEventListener",version:"1.1"}}),Ac:(a,b)=>{b=b.__gppReturn;a(b.returnValue,b.success)}},cU={ge:a=>a.listener,hd:(a,b)=>({__gppCall:{callId:b,command:"removeEventListener",version:"1.1",parameter:a.listenerId}}),Ac:(a,b)=>{b=b.__gppReturn;const c=b.returnValue.data;a?.(c,b.success)}}; 
function dU(a){let b={};typeof a.data==="string"?b=JSON.parse(a.data):b=a.data;return{payload:b,og:b.__gppReturn.callId}} 
function eU(a,b,c){let d=!(b.includes(2)&&c?.idpcApplies),e=!1,f=!1,g=!1;if(a&&!a.startsWith("GPP_ERROR_STRING_")){const FX=uO(a.split("~")[0]),GX=pO(a),Oz=xi(FX,3);for(let jl=0;jl<Oz.length;++jl){const Pz=Oz[jl];if(!b.includes(Pz))continue;const cb=GX[jl];switch(Pz){case 2:if(c?.supportTcfeu){a:{const Y=aQ(cb);if(!Y||!cb){var h=null;break a}const Sa=z(Y,LP,1),kl=z(Y,oP,2)||new oP;var k=ui(Sa,9),l=ui(Sa,4),m=ui(Sa,5),n=C(Sa,10),p=C(Sa,11),w=E(Sa,16),u=C(Sa,15),t={consents:bQ(zi(Sa,13),OP),legitimateInterests:bQ(zi(Sa, 
14),OP)},B={consents:bQ(xi(Sa,17)),legitimateInterests:bQ(xi(Sa,18))},I=bQ(zi(Sa,12),PP),U=mi(Sa,jP,19,Th());const ll={};for(const cq of U){const dq=F(cq,1);ll[dq]=ll[dq]||{};for(const HX of xi(cq,3))ll[dq][HX]=F(cq,2)}h={tcString:cb,tcfPolicyVersion:k,gdprApplies:!0,cmpId:l,cmpVersion:m,isServiceSpecific:n,useNonStandardStacks:p,publisherCC:w,purposeOneTreatment:u,purpose:t,vendor:B,specialFeatureOptins:I,publisher:{restrictions:ll,consents:bQ(zi(kl,1),OP),legitimateInterests:bQ(zi(kl,2),OP),customPurposes:{consents:bQ(xi(kl, 
3)),legitimateInterests:bQ(xi(kl,4))}}}}const aa=h;if(!aa)throw Error("Cannot decode TCF V2 section string.");d=lJ(aa);!oJ(aa,["3","4"],0)&&(e=!0);!oJ(aa,["2","7","9","10"],3)&&(f=!0)}break;case 7:if(cb.length===0)throw Error("Cannot decode empty USNat section string.");const Di=cb.split(".");if(Di.length>2)throw Error(`Expected at most 2 segments but got ${Di.length} when decoding ${cb}.`);var M=void 0,P=void 0,V=void 0,Pa=void 0,Ra=void 0,la=void 0,Ca=void 0,ic=void 0,Ib=void 0,zb=void 0,Aa=void 0, 
ma=void 0,jc=void 0,od=void 0,je=void 0,Ng=void 0,Ld=void 0,Oe=void 0,zf=void 0,Af=void 0,Jb=void 0,Pe=void 0,pd=void 0,ke=void 0,Bf=void 0,Cf=void 0,Md=void 0,Qe=void 0,Df=void 0,Og=void 0,Ef=Di[0];if(Ef.length===0)throw Error("Cannot decode empty core segment string.");let ml=tO(Ef,eP);const eq=rO(ml.slice(0,6));ml=ml.slice(6);if(eq!==1)throw Error(`Unable to decode unsupported USNat Section specification version ${eq} - only version 1 is supported.`);let fq=0;const xa=[];for(let aa=0;aa<dP.length;aa++){const Y= 
dP[aa];xa.push(rO(ml.slice(fq,fq+Y)));fq+=Y}var db=new $O;Og=Oi(db,1,eq);var qd=xa.shift();Df=G(Og,2,qd);var Ff=xa.shift();Qe=G(Df,3,Ff);var Re=xa.shift();Md=G(Qe,4,Re);var Nd=xa.shift();Cf=G(Md,5,Nd);var Gf=xa.shift();Bf=G(Cf,6,Gf);var Fa=xa.shift();ke=G(Bf,7,Fa);var Ei=xa.shift();pd=G(ke,8,Ei);var kc=xa.shift();Pe=G(pd,9,kc);var Pg=xa.shift();Jb=G(Pe,10,Pg);var rd=new ZO,le=xa.shift();Af=G(rd,1,le);var sd=xa.shift();zf=G(Af,2,sd);var Se=xa.shift();Oe=G(zf,3,Se);var lc=xa.shift();Ld=G(Oe,4,lc);var mc= 
xa.shift();Ng=G(Ld,5,mc);var Tc=xa.shift();je=G(Ng,6,Tc);var nc=xa.shift();od=G(je,7,nc);var Kb=xa.shift();jc=G(od,8,Kb);var Fi=xa.shift();ma=G(jc,9,Fi);var Od=xa.shift();Aa=G(ma,10,Od);var Hf=xa.shift();zb=G(Aa,11,Hf);var Hc=xa.shift();Ib=G(zb,12,Hc);ic=A(Jb,11,Ib);var D=new YO,da=xa.shift();Ca=G(D,1,da);var qa=xa.shift();la=G(Ca,2,qa);Ra=A(ic,12,la);var ya=xa.shift();Pa=G(Ra,13,ya);var Ga=xa.shift();V=G(Pa,14,Ga);var Sb=xa.shift();P=G(V,15,Sb);var If=xa.shift();const Qz=M=G(P,16,If);if(Di.length=== 
1)var me=bP(Qz);else{var Ic=bP(Qz),Jf=void 0,Te=void 0,ne=void 0,Pd=Di[1];if(Pd.length===0)throw Error("Cannot decode empty GPC segment string.");const aa=tO(Pd,3),Y=rO(aa.slice(0,2));if(Y<0||Y>1)throw Error(`Attempting to decode unknown GPC segment subsection type ${Y}.`);ne=Y+1;const Sa=rO(aa.charAt(2));var Kf=new aP;Te=G(Kf,2,ne);Jf=Mi(Te,1,!!Sa);me=A(Ic,2,Jf)}const Rz=me,nl=qb(z(Rz,$O,1)),IX=z(nl,YO,12);F(nl,8)!==1&&F(nl,9)!==1&&F(nl,10)!==1&&IX?.g()!==1||(e=!0);var Qg=qb(z(Rz,$O,1));const Sz= 
z(Qg,YO,12)?.i();Sz!==1&&Sz!==2||(g=!0);break;case 8:if(cb.length===0)throw Error("Cannot decode empty USCA section string.");const Gi=cb.split(".");if(Gi.length>2)throw Error(`Expected at most 1 sub-section but got ${Gi.length-1} when decoding ${cb}.`);var gq=void 0,ol=void 0,Lb=void 0,Rg=void 0,Lf=void 0,Mf=void 0,Tz=void 0,Uz=void 0,Vz=void 0,Wz=void 0,Xz=void 0,Yz=void 0,Zz=void 0,$z=void 0,aA=void 0,bA=void 0,cA=void 0,dA=void 0,eA=void 0,fA=void 0,gA=void 0,hA=void 0,iA=void 0,jA=Gi[0];if(jA.length=== 
0)throw Error("Cannot decode empty core segment string.");let pl=tO(jA,DO);const hq=rO(pl.slice(0,6));pl=pl.slice(6);if(hq!==1)throw Error(`Unable to decode unsupported USCA Section specification version ${hq} - only version 1 is supported.`);let iq=0;const Ta=[];for(let aa=0;aa<CO.length;aa++){const Y=CO[aa];Ta.push(rO(pl.slice(iq,iq+Y)));iq+=Y}var JX=new yO;iA=Oi(JX,1,hq);var KX=Ta.shift();hA=G(iA,2,KX);var LX=Ta.shift();gA=G(hA,3,LX);var MX=Ta.shift();fA=G(gA,4,MX);var NX=Ta.shift();eA=G(fA,5, 
NX);var OX=Ta.shift();dA=G(eA,6,OX);var PX=new xO,QX=Ta.shift();cA=G(PX,1,QX);var RX=Ta.shift();bA=G(cA,2,RX);var SX=Ta.shift();aA=G(bA,3,SX);var TX=Ta.shift();$z=G(aA,4,TX);var UX=Ta.shift();Zz=G($z,5,UX);var VX=Ta.shift();Yz=G(Zz,6,VX);var WX=Ta.shift();Xz=G(Yz,7,WX);var XX=Ta.shift();Wz=G(Xz,8,XX);var YX=Ta.shift();Vz=G(Wz,9,YX);Uz=A(dA,7,Vz);var ZX=new wO,$X=Ta.shift();Tz=G(ZX,1,$X);var aY=Ta.shift();Mf=G(Tz,2,aY);Lf=A(Uz,8,Mf);var bY=Ta.shift();Rg=G(Lf,9,bY);var cY=Ta.shift();Lb=G(Rg,10,cY); 
var dY=Ta.shift();ol=G(Lb,11,dY);var eY=Ta.shift();const kA=gq=G(ol,12,eY);if(Gi.length===1)var lA=AO(kA);else{var fY=AO(kA),mA=void 0,nA=void 0,oA=void 0,pA=Gi[1];if(pA.length===0)throw Error("Cannot decode empty GPC segment string.");const aa=tO(pA,3),Y=rO(aa.slice(0,2));if(Y<0||Y>1)throw Error(`Attempting to decode unknown GPC segment subsection type ${Y}.`);oA=Y+1;const Sa=rO(aa.charAt(2));var gY=new zO;nA=G(gY,2,oA);mA=Mi(nA,1,!!Sa);lA=A(fY,2,mA)}const qA=lA,rA=qb(z(qA,yO,1));F(rA,5)!==1&&F(rA, 
6)!==1||(e=!0);var hY=qb(z(qA,yO,1));const ql=z(hY,wO,8);ql?.g()!==1&&ql?.g()!==2&&ql?.i()!==1&&ql?.i()!==2||(g=!0);break;case 9:if(cb.length===0)throw Error("Cannot decode empty USVA section string.");let rl=tO(cb,iP);const jq=rO(rl.slice(0,6));rl=rl.slice(6);if(jq!==1)throw Error(`Unable to decode unsupported USVA Section specification version ${jq} - only version 1 is supported.`);let kq=0;const ib=[];for(let aa=0;aa<hP.length;aa++){const Y=hP[aa];ib.push(rO(rl.slice(kq,kq+Y)));kq+=Y}var iY=jq, 
jY=new gP,kY=Oi(jY,1,iY),lY=ib.shift(),mY=G(kY,2,lY),nY=ib.shift(),oY=G(mY,3,nY),pY=ib.shift(),qY=G(oY,4,pY),rY=ib.shift(),sY=G(qY,5,rY),tY=ib.shift();var uY=G(sY,6,tY);var vY=new fP,wY=ib.shift(),xY=G(vY,1,wY),yY=ib.shift(),zY=G(xY,2,yY),AY=ib.shift(),BY=G(zY,3,AY),CY=ib.shift(),DY=G(BY,4,CY),EY=ib.shift(),FY=G(DY,5,EY),GY=ib.shift(),HY=G(FY,6,GY),IY=ib.shift(),JY=G(HY,7,IY),KY=ib.shift();var LY=G(JY,8,KY);var MY=A(uY,7,LY),NY=ib.shift(),OY=G(MY,8,NY),PY=ib.shift(),QY=G(OY,9,PY),RY=ib.shift(),SY= 
G(QY,10,RY),TY=ib.shift();const lq=G(SY,11,TY);F(lq,5)!==1&&F(lq,6)!==1||(e=!0);const sA=F(lq,8);sA!==1&&sA!==2||(g=!0);break;case 10:if(cb.length===0)throw Error("Cannot decode empty USCO section string.");const Hi=cb.split(".");if(Hi.length>2)throw Error(`Expected at most 2 segments but got ${Hi.length} when decoding ${cb}.`);var UY=void 0,tA=void 0,uA=void 0,vA=void 0,wA=void 0,xA=void 0,yA=void 0,zA=void 0,AA=void 0,BA=void 0,CA=void 0,DA=void 0,EA=void 0,FA=void 0,GA=void 0,HA=void 0,IA=void 0, 
JA=void 0,KA=Hi[0];if(KA.length===0)throw Error("Cannot decode empty core segment string.");let sl=tO(KA,KO);const mq=rO(sl.slice(0,6));sl=sl.slice(6);if(mq!==1)throw Error(`Unable to decode unsupported USCO Section specification version ${mq} - only version 1 is supported.`);let nq=0;const Ab=[];for(let aa=0;aa<JO.length;aa++){const Y=JO[aa];Ab.push(rO(sl.slice(nq,nq+Y)));nq+=Y}var VY=new FO;JA=Oi(VY,1,mq);var WY=Ab.shift();IA=G(JA,2,WY);var XY=Ab.shift();HA=G(IA,3,XY);var YY=Ab.shift();GA=G(HA, 
4,YY);var ZY=Ab.shift();FA=G(GA,5,ZY);var $Y=Ab.shift();EA=G(FA,6,$Y);var aZ=new EO,bZ=Ab.shift();DA=G(aZ,1,bZ);var cZ=Ab.shift();CA=G(DA,2,cZ);var dZ=Ab.shift();BA=G(CA,3,dZ);var eZ=Ab.shift();AA=G(BA,4,eZ);var fZ=Ab.shift();zA=G(AA,5,fZ);var gZ=Ab.shift();yA=G(zA,6,gZ);var hZ=Ab.shift();xA=G(yA,7,hZ);wA=A(EA,7,xA);var iZ=Ab.shift();vA=G(wA,8,iZ);var jZ=Ab.shift();uA=G(vA,9,jZ);var kZ=Ab.shift();tA=G(uA,10,kZ);var lZ=Ab.shift();const LA=UY=G(tA,11,lZ);if(Hi.length===1)var MA=HO(LA);else{var mZ=HO(LA), 
NA=void 0,OA=void 0,PA=void 0,QA=Hi[1];if(QA.length===0)throw Error("Cannot decode empty GPC segment string.");const aa=tO(QA,3),Y=rO(aa.slice(0,2));if(Y<0||Y>1)throw Error(`Attempting to decode unknown GPC segment subsection type ${Y}.`);PA=Y+1;const Sa=rO(aa.charAt(2));var nZ=new GO;OA=G(nZ,2,PA);NA=Mi(OA,1,!!Sa);MA=A(mZ,2,NA)}const RA=MA,SA=qb(z(RA,FO,1));F(SA,5)!==1&&F(SA,6)!==1||(e=!0);var oZ=qb(z(RA,FO,1));const TA=F(oZ,8);TA!==1&&TA!==2||(g=!0);break;case 12:if(cb.length===0)throw Error("Cannot decode empty usct section string."); 
const Ii=cb.split(".");if(Ii.length>2)throw Error(`Expected at most 2 segments but got ${Ii.length} when decoding ${cb}.`);var pZ=void 0,UA=void 0,VA=void 0,WA=void 0,XA=void 0,YA=void 0,ZA=void 0,$A=void 0,aB=void 0,bB=void 0,cB=void 0,dB=void 0,eB=void 0,fB=void 0,gB=void 0,hB=void 0,iB=void 0,jB=void 0,kB=void 0,lB=void 0,mB=void 0,nB=void 0,oB=Ii[0];if(oB.length===0)throw Error("Cannot decode empty core segment string.");let tl=tO(oB,SO);const oq=rO(tl.slice(0,6));tl=tl.slice(6);if(oq!==1)throw Error(`Unable to decode unsupported USCT Section specification version ${oq} - only version 1 is supported.`); 
let pq=0;const Ya=[];for(let aa=0;aa<RO.length;aa++){const Y=RO[aa];Ya.push(rO(tl.slice(pq,pq+Y)));pq+=Y}var qZ=new NO;nB=Oi(qZ,1,oq);var rZ=Ya.shift();mB=G(nB,2,rZ);var sZ=Ya.shift();lB=G(mB,3,sZ);var tZ=Ya.shift();kB=G(lB,4,tZ);var uZ=Ya.shift();jB=G(kB,5,uZ);var vZ=Ya.shift();iB=G(jB,6,vZ);var wZ=new MO,xZ=Ya.shift();hB=G(wZ,1,xZ);var yZ=Ya.shift();gB=G(hB,2,yZ);var zZ=Ya.shift();fB=G(gB,3,zZ);var AZ=Ya.shift();eB=G(fB,4,AZ);var BZ=Ya.shift();dB=G(eB,5,BZ);var CZ=Ya.shift();cB=G(dB,6,CZ);var DZ= 
Ya.shift();bB=G(cB,7,DZ);var EZ=Ya.shift();aB=G(bB,8,EZ);$A=A(iB,7,aB);var FZ=new LO,GZ=Ya.shift();ZA=G(FZ,1,GZ);var HZ=Ya.shift();YA=G(ZA,2,HZ);var IZ=Ya.shift();XA=G(YA,3,IZ);WA=A($A,8,XA);var JZ=Ya.shift();VA=G(WA,9,JZ);var KZ=Ya.shift();UA=G(VA,10,KZ);var LZ=Ya.shift();const pB=pZ=G(UA,11,LZ);if(Ii.length===1)var qB=PO(pB);else{var MZ=PO(pB),rB=void 0,sB=void 0,tB=void 0,uB=Ii[1];if(uB.length===0)throw Error("Cannot decode empty GPC segment string.");const aa=tO(uB,3),Y=rO(aa.slice(0,2));if(Y< 
0||Y>1)throw Error(`Attempting to decode unknown GPC segment subsection type ${Y}.`);tB=Y+1;const Sa=rO(aa.charAt(2));var NZ=new OO;sB=G(NZ,2,tB);rB=Mi(sB,1,!!Sa);qB=A(MZ,2,rB)}const vB=qB,qq=qb(z(vB,NO,1)),wB=z(qq,LO,8);F(qq,5)!==1&&F(qq,6)!==1&&wB?.i()!==1&&wB?.A()!==1||(e=!0);var OZ=qb(z(vB,NO,1));const xB=z(OZ,LO,8);xB?.g()!==1&&xB?.g()!==2||(g=!0);break;case 13:if(cb.length===0)throw Error("Cannot decode empty USFL section string.");let ul=tO(cb,XO);const rq=rO(ul.slice(0,6));ul=ul.slice(6); 
if(rq!==1)throw Error(`Unable to decode unsupported USFL Section specification version ${rq} - only version 1 is supported.`);let sq=0;const Ua=[];for(let aa=0;aa<WO.length;aa++){const Y=WO[aa];Ua.push(rO(ul.slice(sq,sq+Y)));sq+=Y}var PZ=rq,QZ=new VO,RZ=Oi(QZ,1,PZ),SZ=Ua.shift(),TZ=G(RZ,2,SZ),UZ=Ua.shift(),VZ=G(TZ,3,UZ),WZ=Ua.shift(),XZ=G(VZ,4,WZ),YZ=Ua.shift(),ZZ=G(XZ,5,YZ),$Z=Ua.shift();var a_=G(ZZ,6,$Z);var b_=new UO,c_=Ua.shift(),d_=G(b_,1,c_),e_=Ua.shift(),f_=G(d_,2,e_),g_=Ua.shift(),h_=G(f_, 
3,g_),i_=Ua.shift(),j_=G(h_,4,i_),k_=Ua.shift(),l_=G(j_,5,k_),m_=Ua.shift(),n_=G(l_,6,m_),o_=Ua.shift(),p_=G(n_,7,o_),q_=Ua.shift();var r_=G(p_,8,q_);var s_=A(a_,7,r_);var t_=new TO,u_=Ua.shift(),v_=G(t_,1,u_),w_=Ua.shift(),x_=G(v_,2,w_),y_=Ua.shift();var z_=G(x_,3,y_);var A_=A(s_,8,z_),B_=Ua.shift(),C_=G(A_,9,B_),D_=Ua.shift(),E_=G(C_,10,D_),F_=Ua.shift(),G_=G(E_,11,F_),H_=Ua.shift();const vl=G(G_,12,H_),yB=z(vl,TO,8);F(vl,5)!==1&&F(vl,6)!==1&&yB?.i()!==1&&yB?.A()!==1||(e=!0);const zB=z(vl,TO,8)?.g(); 
zB!==1&&zB!==2||(g=!0)}}}return{kl:d,Rh:e,rl:f,Xg:g}} 
var iU=class extends Q{constructor(a){({timeoutMs:b}={});var b;super();this.caller=new bJ(a,"__gppLocator",c=>typeof c.__gpp==="function",dU);this.caller.D.set("addEventListener",$T);this.caller.C.set("addEventListener",bU);this.caller.D.set("removeEventListener",aU);this.caller.C.set("removeEventListener",cU);this.timeoutMs=b??500}g(){this.caller.dispose();super.g()}i(){return!!ZI(this.caller)}addEventListener(a){const b=tc(()=>{a(fU,!0)}),c=this.timeoutMs===-1?void 0:setTimeout(()=>{b()},this.timeoutMs); 
aJ(this.caller,"addEventListener",{listener:(d,e)=>{clearTimeout(c);try{if(d.pingData?.gppVersion===void 0||d.pingData.gppVersion==="1"||d.pingData.gppVersion==="1.0"){this.removeEventListener(d.listenerId);var f={eventName:"signalStatus",data:"ready",pingData:{internalErrorState:1,gppString:"GPP_ERROR_STRING_IS_DEPRECATED_SPEC",applicableSections:[-1]}}}else Array.isArray(d.pingData.applicableSections)?f=d:(this.removeEventListener(d.listenerId),f={eventName:"signalStatus",data:"ready",pingData:{internalErrorState:2, 
gppString:"GPP_ERROR_STRING_EXPECTED_APPLICATION_SECTION_ARRAY",applicableSections:[-1]}});a(f,e)}catch{if(d?.listenerId)try{this.removeEventListener(d.listenerId)}catch{a(gU,!0);return}a(hU,!0)}}})}removeEventListener(a){aJ(this.caller,"removeEventListener",{listener:()=>{},listenerId:a})}}; 
const hU={eventName:"signalStatus",data:"ready",pingData:{internalErrorState:2,gppString:"GPP_ERROR_STRING_UNAVAILABLE",applicableSections:[-1]},listenerId:-1},fU={eventName:"signalStatus",data:"ready",pingData:{gppString:"GPP_ERROR_STRING_LISTENER_REGISTRATION_TIMEOUT",internalErrorState:2,applicableSections:[-1]},listenerId:-1},gU={eventName:"signalStatus",data:"ready",pingData:{gppString:"GPP_ERROR_STRING_REMOVE_EVENT_LISTENER_ERROR",internalErrorState:2,applicableSections:[-1]},listenerId:-1}; 
function jU(a){return!a||a.length===1&&a[0]===-1};function kU(a){a=new iU(a);if(!a.i())return Promise.resolve(null);const b=dI(),c=iI(b,35);if(c)return Promise.resolve(c);const d=new Promise(e=>{e={resolve:e};const f=iI(b,36,[]);f.push(e);jI(b,36,f)});c||c===null||(jI(b,35,null),a.addEventListener(e=>{if(e.pingData.signalStatus==="ready"||jU(e.pingData.applicableSections)){e=e.pingData;jI(b,35,e);for(const f of iI(b,36,[]))f.resolve(e);jI(b,36,[])}}));return d};function lU(a){a=new uJ(a,{timeoutMs:-1,Zi:!0});if(!qJ(a))return Promise.resolve(null);const b=dI(),c=iI(b,24);if(c)return Promise.resolve(c);const d=new Promise(e=>{e={resolve:e};const f=iI(b,25,[]);f.push(e);jI(b,25,f)});c||c===null||(jI(b,24,null),a.addEventListener(e=>{if(kJ(e)){jI(b,24,e);for(const f of iI(b,25,[]))f.resolve(e);jI(b,25,[])}else jI(b,24,null)}));return d};const mU=(a,b)=>{(0,a.__uspapi)("getUSPData",1,(c,d)=>{b.pb({Nc:c??void 0,mh:d?void 0:2})})},nU={ge:a=>a.pb,hd:(a,b)=>({__uspapiCall:{callId:b,command:"getUSPData",version:1}}),Ac:(a,b)=>{b=b.__uspapiReturn;a({Nc:b.returnValue??void 0,mh:b.success?void 0:2})}};function oU(a){let b={};typeof a.data==="string"?b=JSON.parse(a.data):b=a.data;return{payload:b,og:b.__uspapiReturn.callId}} 
function pU(a,b){let c={};if(ZI(a.caller)){var d=tc(()=>{b(c)});aJ(a.caller,"getDataWithCallback",{pb:e=>{e.mh||(c=e.Nc);d()}});setTimeout(d,a.timeoutMs)}else b(c)}var qU=class extends Q{constructor(a){super();this.timeoutMs={}.timeoutMs??500;this.caller=new bJ(a,"__uspapiLocator",b=>typeof b.__uspapi==="function",oU);this.caller.D.set("getDataWithCallback",mU);this.caller.C.set("getDataWithCallback",nU)}g(){this.caller.dispose();super.g()}};function rU(a){const b=new qU(a);return new Promise(c=>{pU(b,d=>{d&&typeof d.uspString==="string"?c(d.uspString):c(null)})})} 
function sU(a,{ql:b,Al:c,Vj:d}){var e=new fO;var f=O(kx)?si(d,5)!=null?d.g():si(b,5)!=null?b.g():a.g():si(b,5)!=null?b.g():a.g();e=bO(e,f);f=O(kx)?si(d,8)!=null?C(d,8):si(b,8)!=null?C(b,8):void 0:si(b,8);e=cO(e,f);a=si(a,14);a=Li(e,14,a);f=si(b,3);a=Li(a,3,f);f=ti(b,2);a=Qi(a,2,f);f=ti(b,4);a=Qi(a,4,f);f=wg(y(b,7));a=Si(a,7,f);b=si(b,9);b=Li(a,9,b);a=ti(c,1);b=Qi(b,1,a);c=si(c,13);c=Li(b,13,c);b=ti(d,11);c=Qi(c,11,b);b=Uh(d,10,Tg,Th(),void 0,0);dO(ci(c,10,b,Bg),si(d,12));return e} 
async function tU(a,{Ia:b,ik:c}){const [d,e,f]=q(await q(Promise.all([lU(a.pubWin),rU(a.pubWin),kU(a.pubWin)])));b=!!b&&(O(lx)||!AQ());var g=bO(new fO,!b);c=Li(g,14,c&&navigator.globalPrivacyControl);g=new fO;if(d){var h=bO(g,lJ(d,{idpcApplies:b}));h=Qi(h,2,d.tcString);h=Qi(h,4,d.addtlConsent||"");h=Si(h,7,d.internalErrorState);var k=!oJ(d,["3","4"],0);h=Li(h,9,k);cO(h,!oJ(d,["2","7","9","10"],3));d.gdprApplies!=null&&Li(g,3,d.gdprApplies)}h=new fO;if(e){k=Qi(h,1,e);var l=e.toUpperCase();if(l.length== 
4&&(l.indexOf("-")==-1||l.substring(1)==="---")&&l[0]>="1"&&l[0]<="9"&&nO.hasOwnProperty(l[1])&&nO.hasOwnProperty(l[2])&&nO.hasOwnProperty(l[3])){var m=new mO;m=Oi(m,1,parseInt(l[0],10));m=G(m,2,nO[l[1]]);m=G(m,3,nO[l[2]]);l=G(m,4,nO[l[3]])}else l=null;l=l?.Sj()===2;Li(k,13,l)}k=new fO;if(f)if(f.internalErrorState)Qi(k,11,f.gppString);else if(jU(f.applicableSections))dO(ci(k,10,f.applicableSections,Bg),!1),O(kx)&&bO(k,!0);else if(l=ci(k,10,f.applicableSections,Bg),Qi(l,11,f.gppString),O(kx))try{const n= 
eU(f.gppString,f.applicableSections,{idpcApplies:b,supportTcfeu:!0});cO(eO(dO(bO(k,n.kl),n.Rh),n.Xg),n.rl)}catch(n){lC(1182,n),cO(eO(dO(bO(k,!b),!1),!1),!1)}else try{const n=eU(f.gppString,f.applicableSections,{idpcApplies:b});eO(dO(k,n.Rh),n.Xg)}catch(n){lC(1182,n),eO(dO(k,!1),!1)}a.j=sU(c,{ql:g,Al:h,Vj:k})};async function uU(a){const b=Um(),c=a.Z,d=a.pageState.g();FI(g=>{if(F(g,1)===0){var h=!!(AT(d)?zT(d):O(yx)?z(c,ji,26)?.g():C(c,6));g=Mi(g,2,h);h=!(si(d,5)!=null?!C(d,5):!C(c,20));g=Mi(g,6,h);G(g,1,1)}});vU(a.pubWin,z(d,ji,10)||ii(c));wU(a.F.google_ad_client);FI(g=>{F(g,1)===1&&G(g,1,2)});const e=new iJ(a.pubWin);q(await q(xU(e,DT(d)||E(c,8))));FI(g=>{F(g,1)===2&&(g=Mi(g,3,!0),G(g,1,3))});q(await q(tU(a,{Ia:AT(d)?zT(d):O(yx)?!!z(c,ji,26)?.g():C(c,6),ik:si(d,7)!=null?C(d,7):O(yx)?!!z(c,ji,26)?.g(): 
C(c,25)})));const f=Um();FI(g=>{if(F(g,1)===3){g=Mi(g,3,f-b>500);var h=!!a.j?.l();g=Mi(g,4,h);h=!!a.j?.g();g=Mi(g,5,h);h=!!a.j?.i();g=Mi(g,7,h);h=!!a.j?.A();g=Mi(g,8,h);G(g,1,4)}})}function vU(a,b){var c=O(mx);a!==a.top||a.__uspapi||a.frames.__uspapiLocator||(a=new zQ(a,b,c),uQ(a),vQ(a))}function wU(a){var b=te(r.top,"googlefcPresent");r.googlefc&&!b&&kC("adsense_fc_has_namespace_but_no_iframes",{publisherId:a},1)}function xU(a,b){return fJ(a,b===".google.cn")?gJ(a):Promise.resolve(null)};function yU(a,b=!1){try{return b?(new Sl(a.innerWidth,a.innerHeight)).round():hm(a||window).round()}catch(c){return new Sl(-12245933,-12245933)}}function zU(a=r){a=a.devicePixelRatio;return typeof a==="number"?+a.toFixed(3):null}function AU(a,b=r){a=a.scrollingElement||(a.compatMode==="CSS1Compat"?a.documentElement:a.body);return new Ol(b.pageXOffset||a.scrollLeft,b.pageYOffset||a.scrollTop)} 
function BU(a){try{return!(!a||!(a.offsetWidth||a.offsetHeight||a.getClientRects().length))}catch(b){return!1}};function CU(a,b){var c=eC,d;var e;d=(e=(e=Yl())&&(d=e.initialLayoutRect)&&typeof d.top==="number"&&typeof d.left==="number"&&typeof d.width==="number"&&typeof d.height==="number"?new Ul(d.left,d.top,d.width,d.height):null)?new Ol(e.left,e.top):(d=am())&&d.rootBounds&&ta(d.rootBounds)?new Ol(d.rootBounds.left+d.boundingClientRect.left,d.rootBounds.top+d.boundingClientRect.top):null;if(d)return d;try{{const k=new Ol(0,0);var f=gm(b);let l=f?f.defaultView:window;if(dc(l,"parent")){do{if(l==a)var g=Am(b); 
else{const m=zm(b);g=new Ol(m.left,m.top)}f=g;k.x+=f.x;k.y+=f.y}while(l&&l!=a&&l!=l.parent&&(b=l.frameElement)&&(l=l.parent))}var h=k}return h}catch(k){return c.ma(888,k),new Ol(-12245933,-12245933)}}function DU(a,b,c,d=!1){a=CU(a,c);c=bm()||yU(b.top);if(!a||a.y===-12245933||c.width===-12245933||c.height===-12245933||!c.height)return 0;let e=0;try{const f=b.top;e=AU(f.document,f).y}catch(f){return 0}b=e+c.height;return a.y<e?d?0:(e-a.y)/c.height:a.y>b?(a.y-b)/c.height:0};function EU(a,b,c,d){const e=DQ(a,"gpi-uoo",(f,g)=>{if(g.source===c){g=gl(f.userOptOut?"1":"0");g=Mh(g,2,Hg(2147483647));g=Qi(g,3,"/");g=Qi(g,4,a.location.hostname);var h=new LR(a);IR(h,"__gpi_opt_out",g,b);if(f.userOptOut||f.clearAdsData)JR(h,"__gads",b),JR(h,"__gpi",b)}});d.push(e)};function FU(a,b){const c=a.pubWin,d=a.F.google_ad_client,e=lI();let f=null;const g=DQ(c,"pvt",(h,k)=>{typeof h.token==="string"&&k.source===b.contentWindow&&(f=h.token,g(),e[d]=e[d]||[],e[d].push(f),e[d].length>100&&e[d].shift())});a.i.push(g);return()=>{f&&Array.isArray(e[d])&&($a(e[d],f),e[d].length||delete e[d],f=null)}};function GU(a){return a.length?a.join("~"):void 0};function HU({L:a,Ak:b,sk:c,hj:d,zp:e,Ap:f,I:g,Xj:h}){let k=0;try{k|=Ar(a,f);const n=Math.min(a.screen.width||0,a.screen.height||0);k|=n?n<320?8192:0:2048;k|=a.navigator&&IU(a.navigator.userAgent)?1048576:0;if(b){f=k;const p=a.innerHeight;var l=(bc()&&wc()?Ke(a):1)*p>=b;var m=f|(l?0:1024)}else m=k|(a.innerHeight>=a.innerWidth?0:8);k=m;k|=Dr(a,c,!0,e)}catch{k|=32}switch(d){case 2:JU(a,g,h)&&(k|=16777216);break;case 1:KU(a,g,h)&&(k|=16777216)}return k} 
function IU(a){return/Android 2/.test(a)||/iPhone OS [34]_/.test(a)||/Windows Phone (?:OS )?[67]/.test(a)||/MSIE.*Windows NT/.test(a)||/Windows NT.*Trident/.test(a)}function JU(a,b=null,c=!1){const d=lG({Eg:0,Ff:a.innerWidth,ig:3,Fg:0,Gf:Math.min(Math.round(a.innerWidth/320*50),LU)+15,jg:3});return qG(MU(a,b),d,c)} 
function KU(a,b=null,c=!1){const d=a.innerWidth,e=a.innerHeight,f=Math.min(Math.round(a.innerWidth/320*50),LU)+15,g=lG({Eg:0,Ff:d,ig:3,Fg:e-f,Gf:e,jg:3});f>25&&g.push({x:d-25,y:e-25});return qG(MU(a,b),g,c)}function MU(a,b=null){return new sG(a,{qh:NU(a,b)})}function NU(a,b=null){if(b)return(c,d,e)=>{ln(b,"ach_evt",{tn:c.tagName,id:c.getAttribute("id")??"",cls:c.getAttribute("class")??"",ign:String(e),pw:a.innerWidth,ph:a.innerHeight,x:d.x,y:d.y},!0,1)}}const LU=90*1.38;function OU(a,b){return HU({L:a,sk:3E3,Ak:a.innerWidth>zr?650:0,I:cC,hj:b,Xj:O(Cv)})};function PU(a){let b=0;try{b|=Ar(a)}catch(c){b|=32}return b};function QU(a){let b=0;try{b|=Ar(a),b|=Dr(a,1E4)}catch(c){b|=32}return b};function RU(){const a={};Mx(Dv)&&(a.bust=Mx(Dv));return a};function SU(){const {promise:a,resolve:b}=new IQ;return{promise:a,resolve:b}};function TU(a,b,c=()=>{}){b.google_llp||(b.google_llp={});b=b.google_llp;let d=b[a];if(d)return d;d=SU();b[a]=d;c();return d}function UU(a,b,c){return TU(a,b,()=>{Yd(b.document,c)}).promise};function VU(a){return a.prerendering?3:{visible:1,hidden:2,prerender:3,preview:4,unloaded:5,"":0}[a.visibilityState||a.webkitVisibilityState||a.mozVisibilityState||""]??0}function WU(a){let b;a.visibilityState?b="visibilitychange":a.mozVisibilityState?b="mozvisibilitychange":a.webkitVisibilityState&&(b="webkitvisibilitychange");return b}function XU(a){return a.hidden!=null?a.hidden:a.mozHidden!=null?a.mozHidden:a.webkitHidden!=null?a.webkitHidden:null} 
function YU(a,b){if(VU(b)===3)var c=!1;else a(),c=!0;if(!c){const d=()=>{Mk(b,"prerenderingchange",d);a()};Lk(b,"prerenderingchange",d)}};Array.from({length:11},(a,b)=>b/10);function ZU(a,b=!1){let c=0;try{c|=Ar(a);var d;if(!(d=!a.navigator)){var e=a.navigator;d="brave"in e&&"isBrave"in e.brave||!1}c|=d||/Android 2/.test(a.navigator.userAgent)?1048576:0;c|=Dr(a,b?Number.MAX_SAFE_INTEGER:2500,!0)}catch(f){c|=32}return c};const $U="body div footer header html main section".split(" ");function aV(a,b=null,c=!1,d=!1,e=!1){let f=Ar(a);IU(a.navigator?.userAgent)&&(f|=1048576);const g=a.innerWidth;g<1200&&(f|=65536);const h=a.innerHeight;h<650&&(f|=2097152);b&&f===0&&(b=b===3?"left":"right",(c=bV({L:a,mk:!1,el:1,position:b,T:g,W:h,kc:new Set,minWidth:120,minHeight:500,Pf:c,lg:d,kg:e}))?kE(a).sideRailPlasParam.set(b,`${c.width}x${c.height}_${String(b).charAt(0)}`):f|=16);return f} 
function cV(a){a=kE(a).sideRailPlasParam;return[...Array.from(a.values())].join("|")}function dV(a,b){return qm(a,c=>c.nodeType===Node.ELEMENT_NODE&&b.has(c))!==null}function eV(a,b){return qm(a,c=>c.nodeType===Node.ELEMENT_NODE&&b.getComputedStyle(c,null).position==="fixed")}function fV(a){const b=[];for(const c of a.document.querySelectorAll("*")){const d=a.getComputedStyle(c,null);d.position==="fixed"&&d.display!=="none"&&d.visibility!=="hidden"&&b.push(c)}return b} 
function gV(a,b){const {top:c,left:d,bottom:e,right:f}=b.getBoundingClientRect();return c>=0&&d>=0&&e<=a.innerHeight&&f<=a.innerWidth}function hV(a){return Math.round(Math.round(a/10)*10)}function iV(a){return`${a.position}-${hV(a.T)}x${hV(a.W)}-${hV(a.scrollY+a.Dc)}Y`}function jV(a){return`f-${iV({position:a.position,Dc:a.Dc,scrollY:0,T:a.T,W:a.W})}`}function kV(a,b){a=Math.min(a??Infinity,b??Infinity);return a!==Infinity?a:0} 
function lV(a,b,c){const d=kE(c.L).sideRailProcessedFixedElements;if(!d.has(a)){var e=a.getBoundingClientRect();if(e){var f=Math.max(e.top-10,0),g=Math.min(e.bottom+10,c.W),h=Math.max(e.left-10,0);e=Math.min(e.right+10,c.T);for(var k=c.T*.3;f<=g;f+=10){if(e>0&&h<k){var l=jV({position:"left",Dc:f,T:c.T,W:c.W});b.set(l,kV(b.get(l),h))}if(h<c.T&&e>c.T-k){l=jV({position:"right",Dc:f,T:c.T,W:c.W});const m=c.T-e;b.set(l,kV(b.get(l),m))}}d.add(a)}}} 
function mV(a,b){const c=b.L,d=b.Pf,e=b.kg;var f=`f-${hV(b.T)}x${hV(b.W)}`;a.has(f)||(a.set(f,0),f=fV(c),d||e?(nV(a,b,f.filter(g=>gV(c,g))),oV(c,f.filter(g=>!gV(c,g)).concat(e?Array.from(c.document.querySelectorAll("[google-side-rail-overlap=false]")):[]))):nV(a,b,f))} 
function nV(a,b,c){var d=b.kc;const e=b.L;kE(e).sideRailProcessedFixedElements.clear();d=new Set([...Array.from(e.document.querySelectorAll("[data-anchor-status],[data-side-rail-status]")),...d]);for(const f of c)dV(f,d)||lV(f,a,b)} 
function pV(a){if(a.T<1200||a.W<650)return null;var b=kE(a.L).sideRailAvailableSpace;a.mk||mV(b,{L:a.L,T:a.T,W:a.W,kc:a.kc,Pf:a.Pf,kg:a.kg});const c=[];var d=a.W*.9,e=Lr(a.L),f=(a.W-d)/2,g=f,h=d/7;for(var k=0;k<8;k++){var l=c,m=l.push;a:{var n=g;var p=a.position,w=b,u={L:a.L,T:a.T,W:a.W,kc:a.kc,lg:a.lg};const U=jV({position:p,Dc:n,T:u.T,W:u.W}),M=iV({position:p,Dc:n,scrollY:e,T:u.T,W:u.W});if(w.has(M)){n=kV(w.get(U),w.get(M));break a}const P=p==="left"?20:u.T-20;let V=P;p=u.T*.3/5*(p==="left"?1:-1); 
for(let Pa=0;Pa<6;Pa++){var t=mG(u.L.document,{x:Math.round(V),y:Math.round(n)}),B=dV(t,u.kc),I=eV(t,u.L);if(!B&&I!==null){lV(I,w,u);w.delete(M);break}B||(B=u,t.getAttribute("google-side-rail-overlap")==="true"?B=!0:t.getAttribute("google-side-rail-overlap")==="false"||B.lg&&!$U.includes(t.tagName.toLowerCase())?B=!1:(I=t.offsetHeight>=B.W*.25,B=t.offsetWidth>=B.T*.9&&I));if(B)w.set(M,Math.round(Math.abs(V-P)+20));else if(V!==P)V-=p,p/=2;else{w.set(M,0);break}V+=p}n=kV(w.get(U),w.get(M))}m.call(l, 
n);g+=h}b=a.el;e=a.position;d=Math.round(d/8);f=Math.round(f);g=a.minWidth;a=a.minHeight;m=[];h=Array(c.length).fill(0);for(l=0;l<c.length;l++){for(;m.length!==0&&c[m[m.length-1]]>=c[l];)m.pop();h[l]=m.length===0?0:m[m.length-1]+1;m.push(l)}m=[];k=c.length-1;l=Array(c.length).fill(0);for(n=k;n>=0;n--){for(;m.length!==0&&c[m[m.length-1]]>=c[n];)m.pop();l[n]=m.length===0?k:m[m.length-1]-1;m.push(n)}m=null;for(k=0;k<c.length;k++)if(n={position:e,width:Math.round(c[k]),height:Math.round((l[k]-h[k]+1)* 
d),offsetY:f+h[k]*d},w=n.width>=g&&n.height>=a,b===0&&w){m=n;break}else b===1&&w&&(!m||n.width*n.height>m.width*m.height)&&(m=n);return m}function oV(a,b){const c=kE(a);if(b.length&&!c.g){var d=new MutationObserver(()=>{setTimeout(()=>{qV(a);for(const e of c.sideRailMutationCallbacks)e()},500)});for(const e of b)d.observe(e,{attributes:!0});c.g=d}}function qV(a){({sideRailAvailableSpace:a}=kE(a));const b=Array.from(a.keys()).filter(c=>c.startsWith("f-"));for(const c of b)a.delete(c)} 
function bV(a){if(a.Aa)return a.Aa.sb(1228,()=>pV(a))||null;try{return pV(a)}catch{}return null};const rV={[27]:512,[26]:128}; 
var sV=(a,b,c,d)=>{d=WN(d);switch(c){case 1:case 2:return OU(a,c)===0;case 3:case 4:return aV(a,c,!0,O(nw),!0)===0;case 8:return ZU(a,O(rv))===0;case 9:return b=!(b.google_adtest==="on"||fR(a.location,"google_scr_debug")),!BK(a,b,d);case 30:return rM(a)===0;case 26:return QU(a)===0;case 27:return PU(a)===0;case 40:return!0;default:return!1}},tV=(a,b,c,d)=>{d=d?WN(d):null;switch(c){case 0:case 40:case 10:case 11:return 0;case 1:case 2:return OU(a,c);case 3:case 4:return aV(a,c,!1,O(nw));case 8:return ZU(a, 
O(rv));case 9:return BK(a,!(b.google_adtest==="on"||fR(a.location,"google_scr_debug")),d);case 16:return Zx(b,a)?0:8388608;case 30:return rM(a);case 26:return QU(a);case 27:return PU(a);default:return 32}},uV=a=>{if(!a.hash)return null;let b=null;be(cR,c=>{!b&&fR(a,c)&&(b=dR[c]||null)});return b},wV=(a,b)=>{const c=kE(a).tagSpecificState[1]||null;c!==null&&c.debugCard==null&&be(eR,d=>{!c.debugCardRequested&&typeof d==="number"&&iR(d,a.location)&&(c.debugCardRequested=!0,vV(a,b,e=>{c.debugCard=e.createDebugCard(d, 
a)}))})},yV=(a,b,c)=>{if(!b)return null;const d=kE(b);let e=0;be(Bc,f=>{const g=rV[f];g&&xV(a,b,f,c)===0&&(e|=g)});d.wasPlaTagProcessed&&(e|=256);a.google_reactive_tag_first&&(e|=1024);return e?`${e}`:null},zV=(a,b,c)=>{const d=[];be(Bc,e=>{const f=xV(b,a,e,c);f!==0&&d.push(`${e}:${f}`)});return d.join(",")||null},AV=a=>{const b=[],c={};be(a,(d,e)=>{if((e=xr[e])&&!c[e]){c[e]=!0;if(d)d=1;else if(d===!1)d=2;else return;b.push(`${e}:${d}`)}});return b.join(",")},BV=a=>{a=a.overlays;if(!a)return"";a= 
a.bottom;return typeof a==="boolean"?a?"1":"0":""},xV=(a,b,c,d)=>{if(!b)return 256;let e=0;const f=kE(b),g=Hr(f,c);if(a.google_reactive_ad_format===c||g)e|=64;let h=!1;be(f.reactiveTypeDisabledByPublisher,(k,l)=>{String(c)===String(l)&&(h=!0)});return h&&uV(b.location)!==c&&(e|=128,c===2||c===1||c===3||c===4||c===8)?e:e|tV(b,a,c,d)},CV=(a,b)=>{if(a){var c=kE(a),d={};be(b,(e,f)=>{(f=xr[f])&&(e===!1||/^false$/i.test(e))&&(d[f]=!0)});be(Bc,e=>{d[yr[e]]&&(c.reactiveTypeDisabledByPublisher[e]=!0)})}}, 
DV=(a,b,c)=>{b=iC(b,c);c={...RU()};const d=W(nx);[0,1].includes(d)&&(c.osttc=`${d}`);return UU(1,window,Sd(a,new Map(Object.entries(c)))).then(b)},vV=(a,b,c)=>{c=iC(212,c);UU(3,a,b).then(c)},EV=(a,b,c)=>{a.dataset.adsbygoogleStatus="reserved";a.className+=" adsbygoogle-noablate";c.adsbygoogle||(c.adsbygoogle=[],Yd(c.document,Rd`https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js`));c.adsbygoogle.push({element:a,params:b})},FV=a=>{a=a.google_reactive_ad_format;return Ac(a)?`${a}`:null}, 
GV=a=>!!FV(a)||a.google_pgb_reactive!=null,HV=a=>{a=Number(FV(a));return a===26||a===27||a===30||a===16||a===40||a===41};function IV(a){return typeof a.google_reactive_sra_index==="number"} 
function JV(a,b,c){const d=b.L||b.pubWin,e=b.F,f=WN(c);c=zV(d,e,c);e.google_reactive_plat=c;(c=AV(a))&&(e.google_reactive_plaf=c);(c=BV(a))&&(e.google_reactive_fba=c);(c=cV(d))&&(e.google_plas=c);KV(a,e);c=uV(b.pubWin.location);LV(a,c,e);c?(e.fra=c,e.google_pgb_reactive=6):e.google_pgb_reactive=5;e.asro=O(Jw);e.aihb=O(qw);e.aifxl=GU(Nx(uw));W(Ow)&&(e.aiapm=W(Ow));W(Pw)&&(e.aiapmi=W(Pw));W(Nw)&&(e.aiact=W(Nw));W(Vw)&&(e.aicct=W(Vw));W(Ww)&&(e.ailct=W(Ww));W(Zw)&&(e.aimart=W(Zw));e.aiof=GU(Nx(Fw)); 
e.fsapi=!0;c!==8&&(f&&xK(f)?(c=AK(f,86400,"__lsv__"),c?.length&&(c=Math.floor((Date.now()-Math.max(...c))/6E4),c>=0&&(e.vmsli=c))):e.vmsli=-1);c=AK(f,600,"__lsa__");c?.length&&Math.floor((Date.now()-Math.max(...c))/6E4)<=W(lv)&&(e.dap=3);bm()||yU(b.pubWin.top);c=EQ(b.pubWin,"rsrai",iC(429,(g,h)=>MV(b,d,e.google_ad_client,a,g,h,f)),iC(430,(g,h)=>Or(b.pubWin,"431",cC,h)));b.i.push(c);kE(d).wasReactiveTagRequestSent=!0;NV(b,a,f)} 
function NV(a,b,c){const d=a.F,e=ta(b.page_level_pubvars)?b.page_level_pubvars:{};b=EQ(a.pubWin,"apcnf",iC(353,(f,g)=>{var h=a.pubWin,k=d.google_ad_client;return De(g.origin)?QR(h,k,e,f.config,c,null):!1}),iC(353,(f,g)=>Or(a.pubWin,"353",cC,g)));a.i.push(b)} 
function MV(a,b,c,d,e,f,g){if(!De(f.origin))return!1;f=e.data;if(!Array.isArray(f))return!1;if(!qI(b,1))return!0;f&&Kl(6,[f]);e=e.amaConfig;const h=[],k=[],l=kE(b);let m=null;for(let n=0;n<f.length;n++){if(!f[n])continue;const p=f[n],w=p.adFormat;l&&p.enabledInAsfe&&(l.reactiveTypeEnabledInAsfe[w]=!0);if(!p.noCreative){p.google_reactive_sra_index=n;if(w===9&&e){p.pubVars=Object.assign(p.pubVars||{},OV(d,p));const u=new CK;if(vK(u,p)&&u.H(p)){m=u;continue}}h.push(p);k.push(w)}}h.length&&DV(a.Ma.bi, 
522,n=>{PV(h,b,n,d,g)});e&&QR(b,c,d,e,g,m);return!0}function OV(a,b){const c=b.adFormat,d=b.adKey;delete b.adKey;const e={};a=a.page_level_pubvars;ta(a)&&Object.assign(e,a);e.google_ad_unit_key=d;e.google_reactive_sra_index=b.google_reactive_sra_index;c===30&&(e.google_reactive_ad_format=30);e.google_pgb_reactive=e.google_pgb_reactive||5;return b.pubVars=e} 
function PV(a,b,c,d,e){const f=[];for(let g=0;g<a.length;g++){const h=a[g],k=h.adFormat,l=h.adKey,m=c.configProcessorForAdFormat(k);k&&m&&l&&(h.pubVars=OV(d,h),delete h.google_reactive_sra_index,f.push(k),hC(466,()=>m.verifyAndProcessConfig(b,h,e)))}} 
function KV(a,b){const c=[];let d=!1;be(xr,(e,f)=>{let g;a.hasOwnProperty(f)&&(f=a[f],f?.google_ad_channel&&(g=String(f.google_ad_channel)));--e;c[e]&&c[e]!=="+"||(c[e]=g?g.replace(/,/g,"+"):"+",d||(d=!!g))});d&&(b.google_reactive_sra_channels=c.join(","))}function LV(a,b,c){if(!c.google_adtest){var d=a.page_level_pubvars;if(a.google_adtest==="on"||d?.google_adtest==="on"||b)c.google_adtest="on"}};var QV=class{constructor(a,b){this.g=a;this.A=b}height(){return this.A}i(a){return a>W(Jv)&&this.A>300?this.g:Math.min(1200,Math.round(a))}j(){}};function RV(a){return b=>!!(b.vb()&a)}var SV=class extends QV{constructor(a,b,c,d=!1){super(a,b);this.C=c;this.l=d}vb(){return this.C}j(a,b,c){c.style.height=`${this.height()}px`;b.rpe=!0}};const TV={image_stacked:1/1.91,image_sidebyside:1/3.82,mobile_banner_image_sidebyside:1/3.82,pub_control_image_stacked:1/1.91,pub_control_image_sidebyside:1/3.82,pub_control_image_card_stacked:1/1.91,pub_control_image_card_sidebyside:1/3.74,pub_control_text:0,pub_control_text_card:0},UV={image_stacked:80,image_sidebyside:0,mobile_banner_image_sidebyside:0,pub_control_image_stacked:80,pub_control_image_sidebyside:0,pub_control_image_card_stacked:85,pub_control_image_card_sidebyside:0,pub_control_text:80, 
pub_control_text_card:80},VV={pub_control_image_stacked:100,pub_control_image_sidebyside:200,pub_control_image_card_stacked:150,pub_control_image_card_sidebyside:250,pub_control_text:100,pub_control_text_card:150}; 
function WV(a){var b=0;a.Qa&&b++;a.Fa&&b++;a.Ga&&b++;if(b<3)return{mb:"Tags data-matched-content-ui-type, data-matched-content-columns-num and data-matched-content-rows-num should be set together."};b=a.Qa.split(",");const c=a.Ga.split(",");a=a.Fa.split(",");if(b.length!==c.length||b.length!==a.length)return{mb:'Lengths of parameters data-matched-content-ui-type, data-matched-content-columns-num and data-matched-content-rows-num must match. Example: \n data-matched-content-rows-num="4,2"\ndata-matched-content-columns-num="1,6"\ndata-matched-content-ui-type="image_stacked,image_card_sidebyside"'}; 
if(b.length>2)return{mb:"The parameter length of attribute data-matched-content-ui-type, data-matched-content-columns-num and data-matched-content-rows-num is too long. At most 2 parameters for each attribute are needed: one for mobile and one for desktop, while "+`you are providing ${b.length} parameters. Example: ${'\n data-matched-content-rows-num="4,2"\ndata-matched-content-columns-num="1,6"\ndata-matched-content-ui-type="image_stacked,image_card_sidebyside"'}.`};const d=[],e=[];for(let g=0;g< 
b.length;g++){var f=Number(c[g]);if(Number.isNaN(f)||f===0)return{mb:`Wrong value '${c[g]}' for ${"data-matched-content-rows-num"}.`};d.push(f);f=Number(a[g]);if(Number.isNaN(f)||f===0)return{mb:`Wrong value '${a[g]}' for ${"data-matched-content-columns-num"}.`};e.push(f)}return{Ga:d,Fa:e,Ih:b}} 
function XV(a){return a>=1200?{width:1200,height:600}:a>=850?{width:a,height:Math.floor(a*.5)}:a>=550?{width:a,height:Math.floor(a*.6)}:a>=468?{width:a,height:Math.floor(a*.7)}:{width:a,height:Math.floor(a*3.44)}}function YV(a,b,c,d){b=Math.floor(((a-8*b-8)/b*TV[d]+UV[d])*c+8*c+8);return a>1500?{width:0,height:0,gl:`Calculated slot width is too large: ${a}`}:b>1500?{width:0,height:0,gl:`Calculated slot height is too large: ${b}`}:{width:a,height:b}} 
function ZV(a,b){const c=a-8-8;--b;return{width:a,height:Math.floor(c/1.91+70)+Math.floor((c*TV.mobile_banner_image_sidebyside+UV.mobile_banner_image_sidebyside)*b+8*b+8)}};const $V=cc("script");var aW=class{constructor(a,b,c=null,d=null,e=null,f=null,g=null,h=null,k=null,l=null,m=null,n=null){this.H=a;this.ib=b;this.vb=c;this.g=d;this.D=e;this.la=f;this.Pa=g;this.A=h;this.l=k;this.i=l;this.j=m;this.C=n}size(){return this.ib}};const bW=["google_content_recommendation_ui_type","google_content_recommendation_columns_num","google_content_recommendation_rows_num"];var cW=class extends QV{i(a){return Math.min(1200,Math.max(this.g,Math.round(a)))}}; 
function dW(a,b){eW(a,b);if(b.google_content_recommendation_ui_type==="pedestal")return new aW(9,new cW(a,Math.floor(a*2.189)));if(O(ov)){var c=wc();var d=W(pv);var e=W(nv),f=W(mv);a<468?c?(a=ZV(a,d),d={jb:a.width,hb:a.height,Fa:1,Ga:d,Qa:"mobile_banner_image_sidebyside"}):(a=YV(a,1,d,"image_sidebyside"),d={jb:a.width,hb:a.height,Fa:1,Ga:d,Qa:"image_sidebyside"}):(d=XV(a),e===1&&(d.height=Math.floor(d.height*.5)),d={jb:d.width,hb:d.height,Fa:f,Ga:e,Qa:"image_stacked"})}else d=wc(),a<468?d?(d=ZV(a, 
12),d={jb:d.width,hb:d.height,Fa:1,Ga:12,Qa:"mobile_banner_image_sidebyside"}):(d=XV(a),d={jb:d.width,hb:d.height,Fa:1,Ga:13,Qa:"image_sidebyside"}):(d=XV(a),d={jb:d.width,hb:d.height,Fa:4,Ga:2,Qa:"image_stacked"});fW(b,d);return new aW(9,new cW(d.jb,d.hb))} 
function gW(a,b){eW(a,b);{const f=WV({Ga:b.google_content_recommendation_rows_num,Fa:b.google_content_recommendation_columns_num,Qa:b.google_content_recommendation_ui_type});if(f.mb)a={jb:0,hb:0,Fa:0,Ga:0,Qa:"image_stacked",mb:f.mb};else{var c=f.Ih.length===2&&a>=468?1:0;var d=f.Ih[c];d=d.indexOf("pub_control_")===0?d:"pub_control_"+d;var e=VV[d];let g=f.Fa[c];for(;a/g<e&&g>1;)g--;e=g;c=f.Ga[c];a=YV(a,e,c,d);a={jb:a.width,hb:a.height,Fa:e,Ga:c,Qa:d}}}if(a.mb)throw new bC(a.mb);fW(b,a);return new aW(9, 
new cW(a.jb,a.hb))}function eW(a,b){if(a<=0)throw new bC(`Invalid responsive width from Matched Content slot ${b.google_ad_slot}: ${a}. Please ensure to put this Matched Content slot into a non-zero width div container.`);}function fW(a,b){a.google_content_recommendation_ui_type=b.Qa;a.google_content_recommendation_columns_num=b.Fa;a.google_content_recommendation_rows_num=b.Ga};var hW=class extends QV{i(){return this.g}j(a,b,c){gy(a,c);c.style.height=`${this.height()}px`;b.rpe=!0}};const iW={"image-top":a=>a<=600?284+(a-250)*.414:429,"image-middle":a=>a<=500?196-(a-250)*.13:164+(a-500)*.2,"image-side":a=>a<=500?205-(a-250)*.28:134+(a-500)*.21,"text-only":a=>a<=500?187-.228*(a-250):130,"in-article":a=>a<=420?a/1.2:a<=460?a/1.91+130:a<=800?a/4:200};var jW={"image-top":0,"image-middle":1,"image-side":2,"text-only":3,"in-article":4},kW=class extends QV{i(){return Math.min(1200,this.g)}}; 
function lW(a,b,c,d,e){var f=e.google_ad_layout||"image-top";if(f==="in-article"){var g=a;if(e.google_full_width_responsive==="false")a=g;else if(a=$x(b,c,g,W(wv),e),a!==!0)e.gfwrnwer=a,a=g;else if(a=Br(b))if(e.google_full_width_responsive_allowed=!0,c.parentElement){b:{g=c;for(let h=0;h<100&&g.parentElement;++h){const k=g.parentElement.childNodes;for(let l=0;l<k.length;++l){const m=k[l];if(m!==g&&cy(b,m))break b}g=g.parentElement;g.style.width="100%";g.style.height="auto"}}gy(b,c)}else a=g;else a= 
g}if(a<250)throw new bC("Fluid responsive ads must be at least 250px wide: "+`availableWidth=${a}`);a=Math.min(1200,Math.floor(a));if(d&&f!=="in-article"){f=Math.ceil(d);if(f<50)throw new bC("Fluid responsive ads must be at least 50px tall: "+`height=${f}`);return new aW(11,new QV(a,f))}if(f!=="in-article"&&(d=e.google_ad_layout_key)){f=`${d}`;if(d=(c=f.match(/([+-][0-9a-z]+)/g))&&c.length)for(b=[],e=0;e<d;e++)b.push(parseInt(c[e],36)/1E3);else b=null;if(!b)throw new bC(`Invalid data-ad-layout-key value: ${f}`); 
f=(a+-725)/1E3;c=0;d=1;e=b.length;for(g=0;g<e;g++)c+=b[g]*d,d*=f;f=Math.ceil(c*1E3- -725+10);if(isNaN(f))throw new bC(`Invalid height: height=${f}`);if(f<50)throw new bC("Fluid responsive ads must be at least 50px tall: "+`height=${f}`);if(f>1200)throw new bC("Fluid responsive ads must be at most 1200px tall: "+`height=${f}`);return new aW(11,new QV(a,f))}d=iW[f];if(!d)throw new bC("Invalid data-ad-layout value: "+f);c=my(c,b);b=Br(b);b=f!=="in-article"||c||a!==b?Math.ceil(d(a)):Math.ceil(d(a)*1.25); 
return new aW(11,f==="in-article"?new kW(a,b):new QV(a,b))};function mW(a){return b=>{for(let c=a.length-1;c>=0;--c)if(!a[c](b))return!1;return!0}}function nW(a,b){var c=oW.slice(0);const d=c.length;let e=null;for(let f=0;f<d;++f){const g=c[f];if(a(g)){if(b==null||b(g))return g;e===null&&(e=g)}}return e};var pW=[new SV(970,90,2),new SV(728,90,2),new SV(468,60,2),new SV(336,280,1),new SV(320,100,2),new SV(320,50,2),new SV(300,600,4),new SV(300,250,1),new SV(250,250,1),new SV(234,60,2),new SV(200,200,1),new SV(180,150,1),new SV(160,600,4),new SV(125,125,1),new SV(120,600,4),new SV(120,240,4),new SV(120,120,1,!0)],oW=[pW[6],pW[12],pW[3],pW[0],pW[7],pW[14],pW[1],pW[8],pW[10],pW[4],pW[15],pW[2],pW[11],pW[5],pW[13],pW[9],pW[16]];function qW(a,b,c,d,e){e.google_full_width_responsive==="false"?c={va:a,la:1}:b==="autorelaxed"&&e.google_full_width_responsive||rW(b)||e.google_ad_resize?(b=ay(a,c,d,e),c=b!==!0?{va:a,la:b}:{va:Br(c)||a,la:!0}):c={va:a,la:2};const {va:f,la:g}=c;return g!==!0?{va:a,la:g}:d.parentElement?{va:f,la:g}:{va:a,la:g}} 
function sW(a,b,c,d,e){const {va:f,la:g}=hC(247,()=>qW(a,b,c,d,e));var h=g===!0;const k=oe(d.style.width),l=oe(d.style.height),{ib:m,Pa:n,vb:p,Gh:w}=tW(f,b,c,d,e,h);h=uW(b,p);var u;const t=(u=hy(d,c,"marginLeft"))?`${u}px`:"",B=(u=hy(d,c,"marginRight"))?`${u}px`:"";u=jy(d,c)||"";return new aW(h,m,p,null,w,g,n,t,B,l,k,u)}function rW(a){return a==="auto"||/^((^|,) *(horizontal|vertical|rectangle) *)+$/.test(a)} 
function tW(a,b,c,d,e,f){b=vW(c,a,b);let g;var h=!1;let k=!1;var l=Br(c)<488;if(l){g=Ux(d,c);var m=my(d,c);h=!m&&g;k=m&&g}m=[ky(a),RV(b)];O(Hv)||m.push(ly(l,c,d,k));e.google_max_responsive_height!=null&&m.push(ny(e.google_max_responsive_height));l=[u=>!u.l];if(h||k)h=oy(c,d),l.push(ny(h));const n=nW(mW(m),mW(l));if(!n)throw new bC(`No slot size for availableWidth=${a}`);const {ib:p,Pa:w}=hC(248,()=>{var u;a:if(f){if(e.gfwrnh&&(u=oe(e.gfwrnh))){u={ib:new hW(a,u),Pa:!0};break a}u=W(yv);u=u>0?a/u:a/ 
1.2;if(e.google_resizing_allowed||e.google_full_width_responsive==="true")var t=Infinity;else{t=d;let I=Infinity;do{var B=hy(t,c,"height");B&&(I=Math.min(I,B));(B=hy(t,c,"maxHeight"))&&(I=Math.min(I,B))}while(t.parentElement&&(t=t.parentElement)&&t.tagName!=="HTML");t=I}!(O(Av)&&t<=u*2)&&(t=Math.min(u,t),t<u*.5||t<100)&&(t=u);u={ib:new hW(a,Math.floor(t)),Pa:t<u?102:!0}}else u={ib:n,Pa:100};return u});return e.google_ad_layout==="in-article"?{ib:wW(a,c,d,p,e),Pa:!1,vb:b,Gh:g}:{ib:p,Pa:w,vb:b,Gh:g}} 
function uW(a,b){if(a==="auto")return 1;switch(b){case 2:return 2;case 1:return 3;case 4:return 4;case 3:return 5;case 6:return 6;case 5:return 7;case 7:return 8;default:throw Error("bad mask");}}function vW(a,b,c){if(c==="auto")c=Math.min(1200,Br(a)),b=b/c<=.25?4:3;else{b=0;for(const d in Tx)c.indexOf(d)!==-1&&(b|=Tx[d])}return b}function wW(a,b,c,d,e){const f=e.google_ad_height||hy(c,b,"height");b=lW(a,b,c,f,e).size();return b.g*b.height()>a*d.height()?new SV(b.g,b.height(),1):d};function xW(a,b,c,d,e){var f;(f=Br(b))?Br(b)<488?b.innerHeight>=b.innerWidth?(e.google_full_width_responsive_allowed=!0,gy(b,c),f={va:f,la:!0}):f={va:a,la:5}:f={va:a,la:4}:f={va:a,la:10};const {va:g,la:h}=f;if(h!==!0||a===g)return new aW(12,new QV(a,d),null,null,!0,h,100);const {ib:k,Pa:l,vb:m}=tW(g,"auto",b,c,e,!0);return new aW(1,k,m,2,!0,h,l)};function yW(a){const b=a.google_ad_format;if(b==="autorelaxed"){if(O(Kv))return a.google_ad_format="auto",1;a:{if(a.google_content_recommendation_ui_type!=="pedestal")for(const c of bW)if(a[c]!=null){a=!0;break a}a=!1}return a?9:5}if(rW(b))return 1;if(b==="link")return 4;if(b==="fluid")return a.google_ad_layout==="in-article"?(zW(a),1):8;if(a.google_reactive_ad_format===27)return zW(a),1} 
function AW(a,b,c,d,e=!1){var f=b.offsetWidth||(c.google_ad_resize||e)&&hy(b,d,"width")||c.google_ad_width||0;a===4&&(c.google_ad_format="auto",a=1);e=(e=BW(a,f,b,c,d))?e:sW(f,c.google_ad_format,d,b,c);e.size().j(d,c,b);e.vb!=null&&(c.google_responsive_formats=e.vb);e.D!=null&&(c.google_safe_for_responsive_override=e.D);e.la!=null&&(e.la===!0?c.google_full_width_responsive_allowed=!0:(c.google_full_width_responsive_allowed=!1,c.gfwrnwer=e.la));e.Pa!=null&&e.Pa!==!0&&(c.gfwrnher=e.Pa);d=e.j||c.google_ad_width; 
d!=null&&(c.google_resizing_width=d);d=e.i||c.google_ad_height;d!=null&&(c.google_resizing_height=d);d=e.size().i(f);const g=e.size().height();c.google_ad_width=d;c.google_ad_height=g;var h=e.size();f=`${h.i(f)}x${h.height()}`;c.google_ad_format=f;c.google_responsive_auto_format=e.H;e.g!=null&&(c.armr=e.g);c.google_ad_resizable=!0;c.google_override_format=1;c.google_loader_features_used=128;e.la===!0&&(c.gfwrnh=`${e.size().height()}px`);e.A!=null&&(c.gfwroml=e.A);e.l!=null&&(c.gfwromr=e.l);e.i!=null&& 
(c.gfwroh=e.i);e.j!=null&&(c.gfwrow=e.j);e.C!=null&&(c.gfwroz=e.C);f=Xd(window)||window;fR(f.location,"google_responsive_dummy_ad")&&(Za([1,2,3,4,5,6,7,8],e.H)||e.g===1)&&e.g!==2&&(f=JSON.stringify({googMsgType:"adpnt",key_value:[{key:"qid",value:"DUMMY_AD"}]}),c.dash=`<${$V}>window.top.postMessage('${f}', '*'); 
          </${$V}> 
          <div id="dummyAd" style="width:${d}px;height:${g}px; 
            background:#ddd;border:3px solid #f00;box-sizing:border-box; 
            color:#000;"> 
            <p>Requested size:${d}x${g}</p> 
            <p>Rendered size:${d}x${g}</p> 
          </div>`);a!==1&&(a=e.size().height(),b.style.height=`${a}px`)}function BW(a,b,c,d,e){const f=d.google_ad_height||hy(c,e,"height")||0;switch(a){case 5:const {va:g,la:h}=hC(247,()=>qW(b,d.google_ad_format,e,c,d));h===!0&&b!==g&&gy(e,c);h===!0?d.google_full_width_responsive_allowed=!0:(d.google_full_width_responsive_allowed=!1,d.gfwrnwer=h);return dW(g,d);case 9:return gW(b,d);case 8:return lW(b,e,c,f,d);case 10:return xW(b,e,c,f,d)}}function zW(a){a.google_ad_format="auto";a.armr=3};function CW(a,b){a.google_resizing_allowed=!0;a.ovlp=!0;a.google_ad_format="auto";a.iaaso=!0;a.armr=b};var DW={"120x90":!0,"160x90":!0,"180x90":!0,"200x90":!0,"468x15":!0,"728x15":!0};function EW(a,b){if(b==15){if(a>=728)return 728;if(a>=468)return 468}else if(b==90){if(a>=200)return 200;if(a>=180)return 180;if(a>=160)return 160;if(a>=120)return 120}return null};function FW(a,b){var c=Xd(b);if(c){c=Br(c);const d=$d(a,b)||{},e=d.direction;if(d.width==="0px"&&d.cssFloat!=="none")return-1;if(e==="ltr"&&c)return Math.floor(Math.min(1200,c-a.getBoundingClientRect().left));if(e==="rtl"&&c)return a=b.document.body.getBoundingClientRect().right-a.getBoundingClientRect().right,Math.floor(Math.min(1200,c-a-Math.floor((c-b.document.body.clientWidth)/2)))}return-1};function GW(a,b){switch(a){case "google_reactive_ad_format":return a=parseInt(b,10),isNaN(a)?0:a;default:return b}}function HW(a,b){if(a=Yl(a))switch(a.data&&a.data.autoFormat){case "rspv":return 13;case "mcrspv":return 15;default:return 14}else return b.google_ad_intent_query?17:12};function IW(a,b){if(!Zx(b,a))return()=>{};a=JW(b,a);if(!a)return()=>{};const c=oI();b=Dc(b);const d={Qb:a,F:b,offsetWidth:a.offsetWidth};c.push(d);return()=>$a(c,d)}function JW(a,b){a=b.document.getElementById(a.google_async_iframe_id);if(!a)return null;for(a=a.parentElement;a&&!py.test(a.className);)a=a.parentElement;return a} 
function KW(a,b){for(let c=0;c<a.childNodes.length;c++){const d={},e=a.childNodes[c];Vx(e.style,d);if(d.google_ad_width==b.google_ad_width&&d.google_ad_height==b.google_ad_height)return e}return null}function LW(a,b){a.style.display=b?"inline-block":"none";const c=a.parentElement;b?c.dataset.adStatus=a.dataset.adStatus:(a.dataset.adStatus=c.dataset.adStatus,delete c.dataset.adStatus)} 
function MW(a,b,c){const d=b.innerHeight>=b.innerWidth?1:2;if(a.i!=d){a.i=d;a=oI();for(const e of a)if(e.Qb.offsetWidth!=e.offsetWidth||e.F.google_full_width_responsive_allowed)e.offsetWidth=e.Qb.offsetWidth,hC(467,()=>{var f=e.Qb,g=e.F;const h=KW(f,g);g.google_full_width_responsive_allowed&&(f.style.marginLeft=g.gfwroml||"",f.style.marginRight=g.gfwromr||"",f.style.height=g.gfwroh?`${g.gfwroh}px`:"",f.style.width=g.gfwrow?`${g.gfwrow}px`:"",f.style.zIndex=g.gfwroz||"",delete g.google_full_width_responsive_allowed); 
delete g.google_ad_format;delete g.google_ad_width;delete g.google_ad_height;delete g.google_content_recommendation_ui_type;delete g.google_content_recommendation_rows_num;delete g.google_content_recommendation_columns_num;if(f.getAttribute("src")){var k=f.getAttribute("src")||"",l=zd(k,"client");l&&(g.google_ad_client=GW("google_ad_client",l));(k=zd(k,"host"))&&(g.google_ad_host=GW("google_ad_host",k))}for(var m of f.attributes)/data-/.test(m.name)&&(k=Qb(m.name.replace("data-matched-content","google_content_recommendation").replace("data", 
"google").replace(/-/g,"_")),g.hasOwnProperty(k)||(l=GW(k,m.value),l!==null&&(g[k]=l)));if(b.document&&b.document.body&&!yW(g)&&!g.google_reactive_ad_format&&!g.google_ad_intent_query&&(l=parseInt(f.style.width,10),k=FW(f,b),k>0&&l>k)){m=parseInt(f.style.height,10);l=!!DW[l+"x"+m];let n=k;if(l){const p=EW(k,m);if(p)n=p,g.google_ad_format=p+"x"+m+"_0ads_al";else throw new bC("No slot size for availableWidth="+k);}g.google_ad_resize=!0;g.google_ad_width=n;l||(g.google_ad_format=null,g.google_override_format= 
!0);k=n;f.style.width=`${k}px`;CW(g,4)}if(O(qv)||Br(b)<488){k=Xd(b)||b;m=f.offsetWidth||hy(f,b,"width")||g.google_ad_width||0;l=g.google_ad_client;if(k=fR(k.location,"google_responsive_slot_preview")||oR(k,c,l))b:if(g.google_reactive_ad_format||g.google_ad_resize||yW(g)||Wx(f,g))k=!1;else{for(k=f;k;k=k.parentElement){l=$d(k,b);if(!l){g.gfwrnwer=18;k=!1;break b}if(!Za(["static","relative"],l.position)){g.gfwrnwer=17;k=!1;break b}}if(!O(Iv)&&(k=W(xv),m=$x(b,f,m,k,g),m!==!0)){g.gfwrnwer=m;k=!1;break b}k= 
b===b.top?!0:!1}k?(CW(g,1),m=!0):m=!1}else m=!1;if(k=yW(g))AW(k,f,g,b,m);else{if(Wx(f,g)){if(m=$d(f,b))f.style.width=m.width,f.style.height=m.height,Vx(m,g);g.google_ad_width||(g.google_ad_width=f.offsetWidth);g.google_ad_height||(g.google_ad_height=f.offsetHeight);g.google_loader_features_used=256;g.google_responsive_auto_format=HW(b,g)}else Vx(f.style,g);b.location&&b.location.hash==="#gfwmrp"||g.google_responsive_auto_format===12&&g.google_full_width_responsive==="true"?AW(10,f,g,b,!1):Math.random()< 
.01&&g.google_responsive_auto_format===12&&(m=ay(f.offsetWidth||parseInt(f.style.width,10)||g.google_ad_width,b,f,g),m!==!0?(g.efwr=!1,g.gfwrnwer=m):g.efwr=!0)}m=KW(f,g);!m&&h&&f.childNodes.length==1?(LW(h,!1),g.google_reactive_ad_format=16,g.google_ad_section="responsive_resize",EV(f,g,b)):m&&h&&m!=h&&(LW(h,!1),LW(m,!0))})}} 
var NW=class extends Q{constructor(){super(...arguments);this.i=null}init(a,b){const c=dI();if(!iI(c,27,!1)){jI(c,27,!0);this.i=a.innerHeight>=a.innerWidth?1:2;var d=()=>{MW(this,a,b)};Lk(a,"resize",d);fs(this,()=>{Mk(a,"resize",d)})}}};var OW=class{constructor(a,b){this.L=a;this.Qb=b;this.g=null;this.j=0}run(){this.g=r.setInterval(Da(this.i,this),500);this.i()}i(){++this.j>=10&&r.clearInterval(this.g);var a=ey(this.L,this.Qb);fy(this.L,this.Qb,a);a=Yx(this.Qb,this.L);a!=null&&a.x===0||r.clearInterval(this.g)}};var PW=class{constructor(a){this.g=0;this.j=this.M=null;this.G=0;this.i=[];this.Uc=this.D="";this.A=this.J=null;this.H=!1;this.L=a.L;this.pubWin=a.pubWin;this.F=a.F;this.Z=a.Z;this.Ma=a.Ma;this.Ea=a.Ea;this.ba=a.ba;this.pageState=a.pageState}};function QW(a,b,c=1E5){a-=b;return a>=c?"M":a>=0?a:"-M"};var ub={Vo:0,Ro:1,So:9,Oo:2,Po:3,Uo:5,To:7,Qo:10};var RW=class extends H{},SW=Hk(RW),TW=[1,3];const UW=Rd`https://securepubads.g.doubleclick.net/static/topics/topics_frame.html`; 
function VW(a){const b=a.google_tag_topics_state??(a.google_tag_topics_state={});return b.messageChannelSendRequestFn?Promise.resolve(b.messageChannelSendRequestFn):new Promise(c=>{function d(h){return g.g(h).then(({data:k})=>k)}const e=Zd("IFRAME");e.style.display="none";e.name="goog_topics_frame";e.src=Oc(UW).toString();const f=(new URL(UW.toString())).origin,g=XQ({destination:a,Ca:e,origin:f,lf:"goog:gRpYw:doubleclick"});g.g("goog:topics:frame:handshake:ack").then(({data:h})=>{h==="goog:topics:frame:handshake:ack"&& 
c(d)});b.messageChannelSendRequestFn=d;a.document.documentElement.appendChild(e)})} 
function WW(a,b,c){var d=eC;const {Id:e,Hd:f}=XW(c);b=b.google_tag_topics_state??(b.google_tag_topics_state={});b.getTopicsPromise||(a=a({message:"goog:topics:frame:get:topics",skipTopicsObservation:!0}).then(g=>{var h=f;if(g instanceof Uint8Array){if(!h){if(h=e instanceof Uint8Array)a:if(sa(g)&&sa(e)&&g.length==e.length){h=g.length;for(let m=0;m<h;m++)if(g[m]!==e[m]){h=!1;break a}h=!0}else h=!1;h=!h}}else if(tb()(g))h||(h=g!==e);else return d.ma(989,Error(JSON.stringify(g))),7;if(h&&c)try{var k= 
new RW;var l=Mh(k,2,Hg(Um()));g instanceof Uint8Array?ei(l,1,TW,Tf(g,!1)):ei(l,3,TW,g==null?g:vg(g));c.setItem("goog:cached:topics",Ui(l))}catch{}return g}),b.getTopicsPromise=a);return e&&!f?Promise.resolve(e):b.getTopicsPromise} 
function XW(a){if(!a)return{Id:null,Hd:!0};try{const k=a.getItem("goog:cached:topics");if(!k)return{Id:null,Hd:!0};const l=SW(k);let m;const n=hi(l,TW);switch(n){case 0:m=null;break;case 1:a=l;var b=Rh(l,TW,1);const w=y(a,b,void 0,void 0,$h);var c=w==null?ff():w;b=Uint8Array;jf(df);var d=c.g;if(d==null||d!=null&&d instanceof Uint8Array)var e=d;else{if(typeof d==="string"){let u;u=af.test(d)?d.replace(af,cf):d;let t;t=atob(u);const B=new Uint8Array(t.length);for(d=0;d<t.length;d++)B[d]=t.charCodeAt(d); 
var f=B}else f=null;e=f}var g=e;var h=g==null?g:c.g=g;m=new b(h||0);break;case 3:m=F(l,Rh(l,TW,3));break;default:ad(n,void 0)}const p=Uu(vi(l,2))+6048E5<Um();return{Id:m,Hd:p}}catch{return{Id:null,Hd:!0}}};function YW(a){return O(rx)&&a?!!a.match(Mx(px)):!1}async function ZW(a,b,c,d,e,f){if(yR("browsing-topics",b.document)&&e&&!O(wx)&&!YW(f?.label))if($W(c,d)){a.A=1;const g=WN(c,b);q(await q(e.then(async h=>{q(await q(WW(h,b,g).then(k=>{a.A=k})))})))}else a.A=5} 
function $W(a,b){return!b.google_restrict_data_processing&&b.google_tag_for_under_age_of_consent!==1&&b.google_tag_for_child_directed_treatment!==1&&!!a.g()&&!nI()&&!C(a,9)&&!C(a,13)&&!C(a,12)&&(typeof b.google_privacy_treatments!=="string"||!b.google_privacy_treatments.split(",").includes("disablePersonalization"))&&!C(a,14)&&!C(a,16)};function aX(a,b,c){const d=b.parentElement?.classList.contains("adsbygoogle")?b.parentElement:b;c.addEventListener("load",()=>{bX(d)});return EQ(a,"adpnt",(e,f)=>{if(Kr(f,c.contentWindow)){e=Nr(e).qid;try{c.setAttribute("data-google-query-id",e),a.googletag??(a.googletag={cmd:[]}),a.googletag.queryIds=a.googletag.queryIds??[],a.googletag.queryIds.push(e),a.googletag.queryIds.length>500&&a.googletag.queryIds.shift()}catch{}d.dataset.adStatus="filled";e=!0}else e=!1;return e})} 
function bX(a){setTimeout(()=>{a.dataset.adStatus!=="filled"&&(a.dataset.adStatus="unfilled")},1E3)};function cX(a,b,{pg:c,qg:d}){return C(b,8)||(c||!b.g())&&d||!YN(a.g)?!1:!0}function dX(a,b,{pg:c,qg:d}){if(cX(a,b,{pg:c,qg:d}))return $N("__eoi",a.g)??void 0}var eX=class{constructor(a){this.g=a}};function fX(a,b,c){try{if(!De(c.origin)||!Kr(c,a.i.contentWindow))return}catch(f){return}const d=b.msg_type;let e=null;typeof d==="string"&&(e=a.messageHandlers[d])&&a.Aa.sb(168,()=>{e.call(a,b,c)})} 
var gX=class extends Q{constructor(a,b){var c=eC,d=cC;super();this.j=a;this.i=b;this.Aa=c;this.I=d;this.messageHandlers={};this.V=[];this.Ka=this.Aa.tb(168,(e,f)=>void fX(this,e,f));this.Kg=this.Aa.tb(169,(e,f)=>Or(this.j,"ras::xsf",this.I,f));this.init({})}init(){this.X(this.messageHandlers);this.V.push(DQ(this.j,"sth",this.Ka,this.Kg))}g(){for(const a of this.V)a();this.V.length=0;super.g()}};var hX=class extends gX{};function iX(a,b,c,d=null){return new jX(a,b,c,d)} 
var jX=class extends hX{constructor(a,b,c,d){super(a,b);this.Ia=c;this.C=d;this.D=N(EI);this.l=()=>{};Lk(this.i,"load",this.l)}g(){Mk(this.i,"load",this.l);super.g()}X(a){a["adsense-labs"]=b=>{if(b=Nr(b).settings)if(b=Wi(il,JSON.parse(b)),ti(b,1)!=null){var c=b.P;if(li(b,c,c[x]|0,hl,4,3).length>0){var d=c=mi(b,hl,4,Th(Vf)),e=this.D;const h=new Yo;for(var f of d)switch(f.getVersion()){case 1:Li(h,1,!0);break;case 2:Li(h,2,!0)}f=new Zo;f=oi(f,1,$o,h);MI(e,f);f=this.j;e=this.C;if(!iI(dI(),37,!1)){f= 
new LR(f);for(var g of c)switch(g.getVersion()){case 1:IR(f,"__gads",g,e);break;case 2:IR(f,"__gpi",g,e)}jI(dI(),37,!0)}Mh(b,4)}if(g=z(b,hl,5))c=this.j,iI(dI(),40,!1)||(c=new eX(c),f=qb(Uu(vi(g,2)))-Date.now()/1E3,f={ie:Math.max(f,0),path:qb(E(g,3)),domain:qb(E(g,4)),Ie:!1},aO("__eoi",qb(g.getValue()),f,c.g),jI(dI(),40,!0));Mh(b,5);g=this.j;c=E(b,1)||"";f=this.Ia;if(st(iO({B:g,Ia:f}))){f=sR(g,f);b!==null&&(f[c]=th(b));try{g.localStorage.setItem("google_adsense_settings",JSON.stringify(f))}catch(h){}}}}}};function kX(a){a.l=a.C;a.D.style.transition="height 500ms";a.ta.style.transition="height 500ms";a.i.style.transition="height 500ms";lX(a)}function mX(a,b){FQ(a.i.contentWindow,"sth",{msg_type:"expand-on-scroll-result",eos_success:!0,eos_amount:b},"*")} 
function lX(a){const b=`rect(0px, ${a.i.width}px, ${a.l}px, 0px)`;a.i.style.clip=b;a.ta.style.clip=b;a.i.setAttribute("height",a.l.toString());a.i.style.height=`${a.l}px`;a.ta.setAttribute("height",a.l.toString());a.ta.style.height=`${a.l}px`;a.D.style.height=`${a.l}px`} 
function nX(a,b){b=ie(b.r_nh);a.C=b==null?0:b;if(a.C<=0)return"1";a.J=Am(a.D).y;a.G=Lr(a.j);if(a.J+a.l<a.G)return"2";if(a.J>Fr(a.j)-a.j.innerHeight)return"3";b=a.G;a.i.setAttribute("height",a.C.toString());a.i.style.height=`${a.C}px`;a.ta.style.overflow="hidden";a.D.style.position="relative";a.D.style.transition="height 100ms";a.ta.style.transition="height 100ms";a.i.style.transition="height 100ms";b=Math.min(b+a.j.innerHeight-a.J,a.l);tm(a.ta,{position:"relative",top:"auto",bottom:"auto"});b=`rect(0px, ${a.i.width}px, ${b}px, 0px)`; 
tm(a.i,{clip:b});tm(a.ta,{clip:b});return"0"} 
var oX=class extends hX{constructor(a,b){super(a.L,b);this.nb=this.zd=!1;this.pa=this.G=this.C=0;this.ta=a.ba;this.D=this.ta.parentElement&&this.ta.parentElement.classList.contains("adsbygoogle")?this.ta.parentElement:this.ta;this.l=parseInt(this.ta.style.height,10);this.Gi=this.l/5;this.J=Am(this.D).y;this.Fi=vc(iC(651,()=>{this.J=Am(this.D).y;var c=this.G;this.G=Lr(this.j);this.l<this.C?(c=this.G-c,c>0&&(this.pa+=c,this.pa>=this.Gi?(kX(this),mX(this,this.C)):(this.l=Math.min(this.C,this.l+c),mX(this, 
c),lX(this)))):Mk(this.j,"scroll",this.M)}),this);this.M=()=>{var c=this.Fi;Cl.requestAnimationFrame?Cl.requestAnimationFrame(c):c()}}X(a){a["expand-on-scroll"]=(b,c)=>{b=Nr(b);this.zd||(this.zd=!0,b=nX(this,b),b==="0"&&Lk(this.j,"scroll",this.M,Ik),FQ(c.target,"sth",{msg_type:"expand-on-scroll-result",eos_success:b==="0"},"*"))};a["expand-on-scroll-force-expand"]=()=>{this.nb||(this.nb=!0,kX(this),Mk(this.j,"scroll",this.M))}}g(){this.M&&Mk(this.j,"scroll",this.M,Ik);super.g()}};function pX(a){const b=a.J.getBoundingClientRect(),c=b.top+b.height<0;return!(b.top>a.i.innerHeight)&&!c} 
var qX=class extends Q{constructor(a,b,c){super();this.i=a;this.C=b;this.J=c;this.D=0;this.l=pX(this);const d=uc(this.G,this);this.j=iC(433,()=>{Cl.requestAnimationFrame?Cl.requestAnimationFrame(d):d()});Lk(this.i,"scroll",this.j,Ik)}G(){const a=pX(this);if(a&&!this.l){var b={rr:"vis-bcr"};const c=this.C.contentWindow;c&&(GQ(c,"ig",b,"*",2),++this.D>=10&&this.dispose())}this.l=a}dispose(){this.j&&Mk(this.i,"scroll",this.j,Ik)}};function rX(a,b){Array.isArray(b)||(b=[b]);b=b.map(function(c){return typeof c==="string"?c:c.property+" "+c.duration+"s "+c.timing+" "+c.delay+"s"});tm(a,"transition",b.join(","))}var sX=sc(function(){const a=jm(document,"DIV"),b=hc?"-webkit":gc?"-moz":null;let c="transition:opacity 1s linear;";b&&(c+=b+"-transition:opacity 1s linear;");hd(a,Kd("div",{style:c}));return wm(a.firstChild,"transition")!=""});function tX(a,b,c){a.i[b].indexOf(c)<0&&(a.i[b]+=c)}function uX(a,b){a.g.indexOf(b)>=0||(a.g=b+a.g)}function vX(a,b){a.errors.indexOf(b)<0&&(a.errors=b+a.errors)}function wX(a,b,c,d){return a.errors!=""||b?null:a.g.replace(xX,"")==""?c!=null&&a.i[0]||d!=null&&a.i[1]?!1:!0:!1}function yX(a){var b=wX(a,"",null,0);if(b===null)return"XS";b=b?"C":"N";a=a.g;return a.indexOf("a")>=0?b+"A":a.indexOf("f")>=0?b+"F":b+"S"} 
var zX=class{constructor(a,b){this.i=["",""];this.g=a||"";this.errors=b||""}toString(){return[this.i[0],this.i[1],this.g,this.errors].join("|")}}; 
function AX(a){let b=a.X;a.D=()=>{};BX(a,a.C,b);let c=a.C.parentElement;if(!c)return a.g;let d=!0,e=null;for(;c;){try{e=/^head|html$/i.test(c.nodeName)?null:$d(c,b)}catch(g){vX(a.g,"c")}const f=CX(a,b,c,e);c.classList.contains("adsbygoogle")&&e&&(/^\-.*/.test(e["margin-left"])||/^\-.*/.test(e["margin-right"]))&&(a.V=!0);if(d&&!f&&DX(e)){uX(a.g,"l");a.G=c;break}d=d&&f;if(e&&EX(a,e))break;c=c.parentElement;if(!c){if(b===a.pubWin)break;try{if(c=b.frameElement,b=b.parent,!Ud(b)){uX(a.g,"c");break}}catch(g){uX(a.g, 
"c");break}}}a.H&&a.l&&I_(a);return a.g} 
function J_(a){function b(m){for(let n=0;n<m.length;n++)tm(k,m[n],"0px")}function c(){K_(d,g,h);!k||l||h||(b(L_),b(M_))}const d=a.C;d.style.overflow=a.Dd?"visible":"hidden";a.H&&(a.G?(rX(d,N_()),rX(a.G,N_())):rX(d,"opacity 1s cubic-bezier(.4, 0, 1, 1), width .2s cubic-bezier(.4, 0, 1, 1) .3s, height .5s cubic-bezier(.4, 0, 1, 1)"));a.M!==null&&(d.style.opacity=String(a.M));const e=a.width!=null&&a.j!=null&&(a.Ee||a.j>a.width)?a.j:null,f=a.height!=null&&a.i!=null&&(a.Ee||a.i>a.height)?a.i:null;if(a.J){const m= 
a.J.length;for(let n=0;n<m;n++)K_(a.J[n],e,f)}const g=a.j,h=a.i,k=a.G,l=a.V;a.H?r.setTimeout(c,1E3):c()} 
function O_(a){if(a.l&&!a.pa||a.j==null&&a.i==null&&a.M==null&&a.l)return a.g;var b=a.l;a.l=!1;AX(a);a.l=b;if(!b||a.check!=null&&!wX(a.g,a.check,a.j,a.i))return a.g;a.g.g.indexOf("n")>=0&&(a.width=null,a.height=null);if(a.width==null&&a.j!==null||a.height==null&&a.i!==null)a.H=!1;(a.j==0||a.i==0)&&a.g.g.indexOf("l")>=0&&(a.j=0,a.i=0);b=a.g;b.i[0]="";b.i[1]="";b.g="";b.errors="";J_(a);return AX(a)} 
function EX(a,b){let c=!1;b.display=="none"&&(uX(a.g,"n"),a.l&&(c=!0));b.visibility!="hidden"&&b.visibility!="collapse"||uX(a.g,"v");b.overflow=="hidden"&&uX(a.g,"o");b.position=="absolute"?(uX(a.g,"a"),c=!0):b.position=="fixed"&&(uX(a.g,"f"),c=!0);return c} 
function BX(a,b,c){let d=0;if(!b||!b.parentElement)return!0;let e=!1,f=0;const g=b.parentElement.childNodes;for(let k=0;k<g.length;k++){var h=g[k];h==b?e=!0:(h=P_(a,h,c),d|=h,e&&(f|=h))}f&1&&(d&2&&tX(a.g,0,"o"),d&4&&tX(a.g,1,"o"));return!(d&1)} 
function CX(a,b,c,d){let e=null;try{e=c.style}catch(t){vX(a.g,"s")}var f=c.getAttribute("width"),g=ie(f),h=c.getAttribute("height"),k=ie(h),l=d&&/^block$/.test(d.display)||e&&/^block$/.test(e.display);b=BX(a,c,b);var m=d&&d.width;const n=d&&d.height;var p=e&&e.width,w=e&&e.height,u=oe(m)==a.width&&oe(n)==a.height;m=u?m:p;w=u?n:w;p=oe(m);u=oe(w);g=a.width!==null&&(p!==null&&a.width>=p||g!==null&&a.width>=g);u=a.height!==null&&(u!==null&&a.height>=u||k!==null&&a.height>=k);k=!b&&DX(d);u=b||u||k||!(f|| 
m||d&&(!Q_(String(d.minWidth))||!R_(String(d.maxWidth))));l=b||g||k||l||!(h||w||d&&(!Q_(String(d.minHeight))||!R_(String(d.maxHeight))));S_(a,0,u,c,"width",f,a.width,a.j);T_(a,0,"d",u,e,d,"width",m,a.width,a.j);T_(a,0,"m",u,e,d,"minWidth",e&&e.minWidth,a.width,a.j);T_(a,0,"M",u,e,d,"maxWidth",e&&e.maxWidth,a.width,a.j);a.ug?(c=/^html|body$/i.test(c.nodeName),f=oe(n),h=d?d.overflowY==="auto"||d.overflowY==="scroll":!1,h=a.i!=null&&d&&f&&Math.round(f)!==a.i&&!h&&d.minHeight!=="100%",a.l&&!c&&h&&(e.setProperty("height", 
"auto","important"),d&&!Q_(String(d.minHeight))&&e.setProperty("min-height","0px","important"),d&&!R_(String(d.maxHeight))&&a.i&&Math.round(f)<a.i&&e.setProperty("max-height","none","important"))):(S_(a,1,l,c,"height",h,a.height,a.i),T_(a,1,"d",l,e,d,"height",w,a.height,a.i),T_(a,1,"m",l,e,d,"minHeight",e&&e.minHeight,a.height,a.i),T_(a,1,"M",l,e,d,"maxHeight",e&&e.maxHeight,a.height,a.i));return b} 
function I_(a){function b(){if(c>0){var l=$d(e,d)||{width:0,height:0};const m=oe(l.width);l=oe(l.height);m!==null&&f!==null&&h&&h(0,f-m);l!==null&&g!==null&&h&&h(1,g-l);--c}else r.clearInterval(k),h&&(h(0,0),h(1,0))}let c=31.25;const d=a.X,e=a.C,f=a.j,g=a.i,h=a.D;let k;r.setTimeout(()=>{k=r.setInterval(b,16)},990)} 
function P_(a,b,c){if(b.nodeType==3)return/\S/.test(b.data)?1:0;if(b.nodeType==1){if(/^(head|script|style)$/i.test(b.nodeName))return 0;let d=null;try{d=$d(b,c)}catch(e){}if(d){if(d.display=="none"||d.position=="fixed")return 0;if(d.position=="absolute"){if(!a.A.boundingClientRect||d.visibility=="hidden"||d.visibility=="collapse")return 0;c=null;try{c=b.getBoundingClientRect()}catch(e){return 0}return(c.right>a.A.boundingClientRect.left?2:0)|(c.bottom>a.A.boundingClientRect.top?4:0)}}return 1}return 0} 
function S_(a,b,c,d,e,f,g,h){if(h!=null){if(typeof f=="string"){if(f=="100%"||!f)return;f=ie(f);f==null&&(vX(a.g,"n"),tX(a.g,b,"d"))}if(f!=null)if(c){if(a.l)if(a.H){const k=Math.max(f+h-(g||0),0),l=a.D;a.D=(m,n)=>{m==b&&id(d,e,String(k-n));l&&l(m,n)}}else id(d,e,String(h))}else tX(a.g,b,"d")}} 
function T_(a,b,c,d,e,f,g,h,k,l){if(l!=null){f=f&&f[g];typeof f!="string"||(c=="m"?Q_(f):R_(f))||(f=oe(f),f==null?uX(a.g,"p"):k!=null&&uX(a.g,f==k?"E":"e"));if(typeof h=="string"){if(c=="m"?Q_(h):R_(h))return;h=oe(h);h==null&&(vX(a.g,"p"),tX(a.g,b,c))}if(h!=null)if(d&&e){if(a.l)if(a.H){const m=Math.max(h+l-(k||0),0),n=a.D;a.D=(p,w)=>{p==b&&(e[g]=`${m-w}px`);n&&n(p,w)}}else e[g]=`${l}px`}else tX(a.g,b,c)}} 
var Y_=class{constructor(a,b,c,d,e,f,g){this.pubWin=a;this.C=b;this.J=c;this.G=this.D=null;this.V=!1;this.A=new U_(this.C);this.X=(a=this.C.ownerDocument)&&(a.defaultView||a.parentWindow);this.A=new U_(this.C);this.l=g;this.pa=V_(this.A,d.Gg,d.height,d.rd);this.width=this.l?this.A.boundingClientRect?this.A.boundingClientRect.right-this.A.boundingClientRect.left:null:e;this.height=this.l?this.A.boundingClientRect?this.A.boundingClientRect.bottom-this.A.boundingClientRect.top:null:f;this.j=W_(d.width); 
this.i=W_(d.height);this.M=this.l?W_(d.opacity):null;this.check=d.check;this.rd=!!d.rd;this.H=d.Gg=="animate"&&!X_(this.A,this.i,this.rd)&&sX();this.Dd=!!d.Dd;this.g=new zX;X_(this.A,this.i,this.rd)&&uX(this.g,"r");e=this.A;e.g&&e.i>=e.W&&uX(this.g,"b");this.Ee=!!d.Ee;this.ug=!!d.ug}};function X_(a,b,c){var d;(d=a.g)&&!(d=!a.visible)&&(c?(b=a.i+Math.min(b,W_(a.getHeight())),a=a.g&&b>=a.W):a=a.g&&a.i>=a.W,d=a);return d} 
var U_=class{constructor(a){this.boundingClientRect=null;var b=a&&a.ownerDocument,c=b&&(b.defaultView||b.parentWindow);c=c&&Xd(c);this.g=!!c;if(a)try{this.boundingClientRect=a.getBoundingClientRect()}catch(g){}var d=a;let e=0,f=this.boundingClientRect;for(;d;)try{f&&(e+=f.top);const g=d.ownerDocument,h=g&&(g.defaultView||g.parentWindow);(d=h&&h.frameElement)&&(f=d.getBoundingClientRect())}catch(g){break}this.i=e;c=c||r;this.W=(c.document.compatMode=="CSS1Compat"?c.document.documentElement:c.document.body).clientHeight; 
b=b&&VU(b);this.visible=!!a&&!(b==2||b==3)&&!(this.boundingClientRect&&this.boundingClientRect.top>=this.boundingClientRect.bottom&&this.boundingClientRect.left>=this.boundingClientRect.right)}isVisible(){return this.visible}getWidth(){return this.boundingClientRect?this.boundingClientRect.right-this.boundingClientRect.left:null}getHeight(){return this.boundingClientRect?this.boundingClientRect.bottom-this.boundingClientRect.top:null}}; 
function V_(a,b,c,d){switch(b){case "no_rsz":return!1;case "force":case "animate":return!0;default:return X_(a,c,d)}}function DX(a){return!!a&&/^left|right$/.test(a.cssFloat||a.styleFloat)}var Z_=new zX("s",""),xX=RegExp("[lonvafrbpEe]","g");function R_(a){return!a||/^(auto|none|100%)$/.test(a)}function Q_(a){return!a||/^(0px|auto|none|0%)$/.test(a)} 
function K_(a,b,c){b!==null&&ie(a.getAttribute("width"))!==null&&a.setAttribute("width",String(b));c!==null&&ie(a.getAttribute("height"))!==null&&a.setAttribute("height",String(c));b!==null&&(a.style.width=`${b}px`);c!==null&&(a.style.height=`${c}px`)}var L_="margin-left margin-right padding-left padding-right border-left-width border-right-width".split(" "),M_="margin-top margin-bottom padding-top padding-bottom border-top-width border-bottom-width".split(" "); 
function N_(){let a="opacity 1s cubic-bezier(.4, 0, 1, 1), width .2s cubic-bezier(.4, 0, 1, 1), height .3s cubic-bezier(.4, 0, 1, 1) .2s",b=L_;for(var c=0;c<b.length;c++)a+=", "+b[c]+" .2s cubic-bezier(.4, 0, 1, 1)";b=M_;for(c=0;c<b.length;c++)a+=", "+b[c]+" .3s cubic-bezier(.4, 0, 1, 1) .2s";return a}function W_(a){return typeof a==="string"?ie(a):typeof a==="number"&&isFinite(a)?a:null};var $_=class extends hX{constructor(a,b,c){super(a,b);this.ba=c}X(a){a["resize-me"]=(b,c)=>{b=Nr(b);var d=b.r_chk;if(d==null||d===""){var e=ie(b.r_nw),f=ie(b.r_nh),g=ie(b.r_no);g!=null||e!==0&&f!==0||(g=0);var h=b.r_str;h=h?h:null;{var k=/^true$/.test(b.r_ao),l=/^true$/.test(b.r_ifr),m=/^true$/.test(b.r_cab);const w=window;if(w)if(h==="no_rsz")b.err="7",e=!0;else{var n=new U_(this.i);if(n.g){var p=n.getWidth();p!=null&&(b.w=p);p=n.getHeight();p!=null&&(b.h=p);V_(n,h,f,m)?(n=this.ba,d=O_(new Y_(w, 
n,[this.i],{width:e,height:f,opacity:g,check:d,Gg:h,Dd:k,Ee:l,rd:m},null,null,!0)),b.r_cui&&/^true$/.test(b.r_cui.toString())&&v(n,{height:`${f===null?0:f-48}px`,top:"24px"}),e!=null&&(b.nw=e),f!=null&&(b.nh=f),b.rsz=d.toString(),b.abl=yX(d),b.frsz=(h==="force").toString(),b.err="0",e=!0):(b.err="1",e=!1)}else b.err="3",e=!1}else b.err="2",e=!1}FQ(c.source,"sth",{msg_type:"resize-result",r_str:h,r_status:e},"*");this.i.dataset.googleQueryId||this.i.setAttribute("data-google-query-id",b.qid)}}}};function a0(a,b){return new IntersectionObserver(b,a)}function b0(a,b,c){Lk(a,b,c);return()=>Mk(a,b,c)}let c0=null;function d0(){c0=Um()}function e0(a,b){return b?c0===null?(Lk(a,"mousemove",d0,{passive:!0}),Lk(a,"scroll",d0,{passive:!0}),d0(),!1):Um()-c0>=b*1E3:!1} 
function f0({B:a,element:b,ul:c,jl:d,il:e=0,pb:f,Fj:g,options:h={},tk:k=!0,mp:l=a0}){let m,n=!1,p=!1;const w=[],u=l(h,(t,B)=>{try{const I=()=>{w.length||(d&&(w.push(b0(b,"mouseenter",()=>{n=!0;I()})),w.push(b0(b,"mouseleave",()=>{n=!1;I()}))),w.push(b0(a.document,"visibilitychange",()=>I())));const U=e0(a,e),M=XU(a.document);if(p&&!n&&!U&&!M)m=m||a.setTimeout(()=>{e0(a,e)?I():(f(),B.disconnect())},c*1E3);else if(k||n||U||M)a.clearTimeout(m),m=void 0};({isIntersecting:p}=t[t.length-1]);I()}catch(I){g&& 
g(I)}});u.observe(b);return()=>{u.disconnect();for(const t of w)t();m!=null&&a.clearTimeout(m)}};function g0(a,b,c,d,e){return new h0(a,b,c,d,e)}function i0(a,b,c){const d=a.i,e=a.D;if(e!=null&&d!=null&&Kr(c,d.contentWindow)&&(b=b.config,typeof b==="string")){try{var f=JSON.parse(b);if(!Array.isArray(f))return;a.l=Wi(wl,f)}catch(g){return}a.dispose();f=ui(a.l,1);f<=0||(a.C=f0({B:a.j,element:e,ul:f-.2,jl:!wc(),il:ui(a.l,3),pb:()=>void j0(a,e),Fj:g=>or.ma(1223,g,void 0,void 0),options:{threshold:wi(a.l,2,1)},tk:!0}))}} 
function j0(a,b){a.G();setTimeout(or.tb(1224,()=>{var c=Number(a.F.rc);a.F.rc=c?c+1:1;c=b.parentElement||null;c&&py.test(c.className)||(c=jm(document,"INS"),c.className="adsbygoogle",b.parentNode&&b.parentNode.insertBefore(c,b.nextSibling));O(hw)?(k0(a,c,b),a.F.no_resize=!0,qs(WJ(c),"filled",()=>{km(b)})):km(b);EV(c,a.F,a.j)}),200)} 
function k0(a,b,c){a.j.getComputedStyle(b).position==="static"&&(b.style.position="relative");c.style.position="absolute";c.style.top="0";c.style.left="0";delete b.dataset.adsbygoogleStatus;delete b.dataset.adStatus;b.classList.remove("adsbygoogle-noablate")} 
var h0=class extends hX{constructor(a,b,c,d,e){super(a,b);this.F=c;this.D=d;this.G=e;this.l=this.C=null;(b=(b=b.contentWindow)&&b.parent)&&a!==b&&this.V.push(DQ(b,"sth",this.Ka,this.Kg))}X(a){a.av_ref=(b,c)=>{i0(this,b,c)}}g(){super.g();this.D=null;this.C&&this.C()}};const l0=/^blogger$/,m0=/^wordpress(.|\s|$)/i,n0=/^joomla!/i,o0=/^drupal/i,p0=/\/wp-content\//,q0=/\/wp-content\/plugins\/advanced-ads/,r0=/\/wp-content\/themes\/genesis/,s0=/\/wp-content\/plugins\/genesis/; 
function t0(a){var b=a.getElementsByTagName("script"),c=b.length;for(var d=0;d<c;++d){var e=b[d];if(e.hasAttribute("src")){e=e.getAttribute("src")||"";if(q0.test(e))return 5;if(s0.test(e))return 6}}b=a.getElementsByTagName("link");c=b.length;for(d=0;d<c;++d)if(e=b[d],e.hasAttribute("href")&&(e=e.getAttribute("href")||"",r0.test(e)||s0.test(e)))return 6;a=a.getElementsByTagName("meta");d=a.length;for(e=0;e<d;++e){var f=a[e];if(f.getAttribute("name")=="generator"&&f.hasAttribute("content")){f=f.getAttribute("content")|| 
"";if(l0.test(f))return 1;if(m0.test(f))return 2;if(n0.test(f))return 3;if(o0.test(f))return 4}}for(a=0;a<c;++a)if(d=b[a],d.getAttribute("rel")=="stylesheet"&&d.hasAttribute("href")&&(d=d.getAttribute("href")||"",p0.test(d)))return 2;return 0};var u0={google_ad_block:"ad_block",google_ad_client:"client",google_ad_intent_query:"ait_q",google_ad_output:"output",google_ad_callback:"callback",google_ad_height:"h",google_ad_resize:"twa",google_ad_slot:"slotname",google_ad_unit_key:"adk",google_ad_dom_fingerprint:"adf",google_ad_intent_qetid:"aiqeid",google_ad_intents_format:"ait_f",google_placement_id:"pi",google_daaos_ts:"daaos",google_erank:"epr",google_ad_width:"w",abgtt:"abgtt",google_captcha_token:"captok",google_content_recommendation_columns_num:"cr_col", 
google_content_recommendation_rows_num:"cr_row",google_ctr_threshold:"ctr_t",google_cust_criteria:"cust_params",gfwrnwer:"fwrn",gfwrnher:"fwrnh",google_image_size:"image_size",google_last_modified_time:"lmt",google_loeid:"loeid",google_max_num_ads:"num_ads",google_max_radlink_len:"max_radlink_len",google_mtl:"mtl",google_native_settings_key:"nsk",google_enable_content_recommendations:"ecr",google_num_radlinks:"num_radlinks",google_num_radlinks_per_unit:"num_radlinks_per_unit",google_pucrd:"pucrd", 
google_reactive_plaf:"plaf",google_reactive_plat:"plat",google_reactive_fba:"fba",google_reactive_sra_channels:"plach",google_responsive_auto_format:"rafmt",armr:"armr",google_plas:"plas",google_rl_dest_url:"rl_dest_url",google_rl_filtering:"rl_filtering",google_rl_mode:"rl_mode",google_rt:"rt",google_video_play_muted:"vpmute",google_source_type:"src_type",google_restrict_data_processing:"rdp",google_tag_for_child_directed_treatment:"tfcd",google_tag_for_under_age_of_consent:"tfua",google_tag_origin:"to", 
google_ad_semantic_area:"sem",google_tfs:"tfs",google_package:"pwprc",google_tag_partner:"tp",fra:"fpla",google_ml_rank:"mlr",google_apsail:"psa",google_ad_channel:"channel",google_ad_type:"ad_type",google_ad_format:"format",google_color_bg:"color_bg",google_color_border:"color_border",google_color_link:"color_link",google_color_text:"color_text",google_color_url:"color_url",google_page_url:"url",google_ad_section:"region",google_cpm:"cpm",google_encoding:"oe",google_safe:"adsafe",google_font_face:"f", 
google_font_size:"fs",google_hints:"hints",google_ad_host:"host",google_ad_host_channel:"h_ch",google_ad_host_tier_id:"ht_id",google_kw_type:"kw_type",google_kw:"kw",google_contents:"contents",google_targeting:"targeting",google_adtest:"adtest",google_alternate_color:"alt_color",google_alternate_ad_url:"alternate_ad_url",google_cust_age:"cust_age",google_cust_gender:"cust_gender",google_cust_l:"cust_l",google_cust_lh:"cust_lh",google_language:"hl",google_city:"gcs",google_country:"gl",google_region:"gr", 
google_content_recommendation_ad_positions:"ad_pos",google_content_recommendation_ui_type:"crui",google_content_recommendation_use_square_imgs:"cr_sq_img",sso:"sso",google_color_line:"color_line",google_disable_video_autoplay:"disable_video_autoplay",google_full_width_responsive_allowed:"fwr",google_full_width_responsive:"fwrattr",efwr:"efwr",google_pgb_reactive:"pra",rc:"rc",google_resizing_allowed:"rs",google_resizing_height:"rh",google_resizing_width:"rw",rpe:"rpe",google_responsive_formats:"resp_fmts", 
google_safe_for_responsive_override:"sfro",google_video_doc_id:"video_doc_id",google_video_product_type:"video_product_type",google_webgl_support:"wgl",aihb:"aihb",aiof:"aiof",asro:"asro",aifxl:"aifxl",vmsli:"itsi",dap:"dap",aiapm:"aiapm",aiapmi:"aiapmi",aiact:"aiact",aicct:"aicct",ailct:"ailct",aimart:"aimart"};function v0(a){a.g===-1&&(a.g=a.data.reduce((b,c,d)=>b+(c?2**d:0),0));return a.g}var w0=class{constructor(){this.data=[];this.g=-1}set(a,b=!0){0<=a&&a<52&&Number.isInteger(a)&&this.data[a]!==b&&(this.data[a]=b,this.g=-1)}get(a){return!!this.data[a]}};function x0(){const a=new w0;"SVGElement"in r&&"createElementNS"in r.document&&a.set(0);const b=se();b["allow-top-navigation-by-user-activation"]&&a.set(1);b["allow-popups-to-escape-sandbox"]&&a.set(2);r.crypto&&r.crypto.subtle&&a.set(3);"TextDecoder"in r&&"TextEncoder"in r&&a.set(4);return v0(a)};var y0=Gk(lO);function z0(a=document){const b=[],c=[];for(const f of Array.from(a.querySelectorAll("meta[name=generator][content]"))){if(!f)continue;var d=f.getAttribute("content")??"";const [,g,h]=/^([^0-9]+)(?:\s([0-9]+(?:\.[0-9]+){0,2})[.0-9]*)?[^0-9]*$/.exec(d)??[],k=g,l=h;a=new kO;l&&Qi(a,3,l.substring(0,20));let m,n;if(k){for(const [p,w]of(new Map([[1,"WordPress"],[2,"Drupal"],[3,"MediaWiki"],[4,"Blogger"],[5,"SEOmatic"],[7,"Flutter"],[8,"Joomla! - Open Source Content Management"]])).entries()){var e=p;if(w=== 
k.trim()){m=e;break}}for(const [p,w]of(new Map([[1,"All in One SEO (AIOSEO)"],[2,"All in One SEO Pro (AIOSEO)"],[3,"AMP for WP"],[4,"Site Kit by Google"],[5,"Elementor"],[6,"Powered by WPBakery Page Builder - drag and drop page builder for WordPress."]])).entries())if(e=p,w===k.trim()){n=e;break}}n?(d=Si(a,1,1),Si(d,2,n)):m?Si(a,1,m):(e=Si(a,1,0),Mh(e,3),c.push({content:d,name:k,version:l}));b.push(a)}return{labels:b,yp:c}};const A0=new Map([["navigate",1],["reload",2],["back_forward",3],["prerender",4]]),B0=new Map([[0,1],[1,2],[2,3]]);function C0(a){try{const b=a.performance?.getEntriesByType("navigation")?.[0];if(b?.type)return A0.get(b.type)??null}catch{}return B0.get(a.performance?.navigation?.type)??null};var D0=class extends H{};var E0=class extends H{};function F0(a,b){if(bc()){var c=a.document.documentElement.lang;G0(a)?H0(b,Ge(a),!0,"",c):(new MutationObserver((d,e)=>{G0(a)&&(H0(b,Ge(a),!1,c,a.document.documentElement.lang),e.disconnect())})).observe(a.document.documentElement,{attributeFilter:["class"]})}}function G0(a){a=a.document?.documentElement?.classList;return!(!a?.contains("translated-rtl")&&!a?.contains("translated-ltr"))}function H0(a,b,c,d,e){zl({ptt:`${a}`,pvsid:`${b}`,ibatl:String(c),pl:d,nl:e},"translate-event")};function I0(a){if(a=a.navigator?.userActivation){var b=0;a?.hasBeenActive&&(b|=1);a?.isActive&&(b|=2);return b}};const J0=/[+, ]/;function K0(a){try{if(a.parentNode)return a.parentNode}catch{return null}if(a.nodeType===9)a:{try{const c=a?a.defaultView:window;if(c){const d=c.frameElement;if(d&&Ud(c.parent)){var b=d;break a}}}catch{}b=null}else b=null;return b}function L0(a,b){var c=NR(a.pubWin);a.F.saaei&&(c+=(c===""?"":",")+a.F.saaei);a.F.google_ad_intent_eids&&(c+=`${c===""?"":","}${a.F.google_ad_intent_eids}`);b.eid=c;c=a.F.google_loeid;typeof c==="string"&&(a.g|=4096,b.loeid=c)} 
function M0(a,b){a=(a=Xd(a.pubWin))&&a.document?AU(a.document,a):new Ol(-12245933,-12245933);b.scr_x=Math.round(a.x);b.scr_y=Math.round(a.y)}function N0(a){try{const b=r.top.location.hash;if(b){const c=b.match(a);return c&&c[1]||""}}catch{}return""} 
function O0(a,b,c){const d=a.F;var e=a.pubWin,f=a.L,g=Je(window);d.fsapi&&(b.fsapi=!0);b.ref=d.google_referrer_url;b.loc=d.google_page_location;var h;(h=Yl(e))&&h.data&&ta(h.data)&&typeof h.data.type==="string"?(h=h.data.type.toLowerCase(),h=h==="doubleclick"||h==="adsense"?null:h):h=null;h&&(b.apn=h.substr(0,10));g=aI(g);b.url||b.loc||!g.url||(b.url=g.url,g.Yf||(b.usrc=1));g.url!=(b.loc||b.url)&&(b.top=g.url);a.Uc&&(b.etu=a.Uc);(c=yV(d,f,c))&&(b.fc=c);if(!Im(d)){c=a.pubWin.document;g="";if(c.documentMode&& 
(h=rm(new fm(c),"IFRAME"),h.frameBorder="0",h.style.height=0,h.style.width=0,h.style.position="absolute",c.body)){c.body.appendChild(h);try{const ma=h.contentWindow.document;ma.open();var k=cd("<!DOCTYPE html>");ma.write(dd(k));ma.close();g+=ma.documentMode}catch(ma){}c.body.removeChild(h)}b.docm=g}let l,m,n,p,w,u,t,B,I,U;try{l=e.screenX,m=e.screenY}catch(ma){}try{n=e.outerWidth,p=e.outerHeight}catch(ma){}try{w=e.innerWidth,u=e.innerHeight}catch(ma){}try{t=e.screenLeft,B=e.screenTop}catch(ma){}try{w= 
e.innerWidth,u=e.innerHeight}catch(ma){}try{I=e.screen.availWidth,U=e.screen.availTop}catch(ma){}b.brdim=[t,B,l,m,I,U,n,p,w,u].join();k=0;r.postMessage===void 0&&(k|=1);k>0&&(b.osd=k);b.vis=VU(e.document);k=a.ba;e=GV(d)?Z_:O_(new Y_(e,k,null,{width:0,height:0},d.google_ad_width,d.google_ad_height,!1));b.rsz=e.toString();b.abl=yX(e);if(!GV(d)&&(e=Jm(d),e!=null)){k=0;a:{try{{var M=d.google_async_iframe_id;const ma=window.document;if(M)var P=ma.getElementById(M);else{var V=ma.getElementsByTagName("script"), 
Pa=V[V.length-1];P=Pa&&Pa.parentNode||null}}if(M=P){P=[];V=0;for(var Ra=Date.now();++V<=100&&Date.now()-Ra<50&&(M=K0(M));)M.nodeType===1&&P.push(M);var la=P;b:{for(Ra=0;Ra<la.length;Ra++){c:{var Ca=la[Ra];try{if(Ca.parentNode&&Ca.offsetWidth>0&&Ca.offsetHeight>0&&Ca.style&&Ca.style.display!=="none"&&Ca.style.visibility!=="hidden"&&(!Ca.style.opacity||Number(Ca.style.opacity)!==0)){const ma=Ca.getBoundingClientRect();var ic=ma.right>0&&ma.bottom>0;break c}}catch(ma){}ic=!1}if(!ic){var Ib=!1;break b}}Ib= 
!0}if(Ib){b:{const ma=Date.now();Ib=/^html|body$/i;ic=/^fixed/i;for(Ca=0;Ca<la.length&&Date.now()-ma<50;Ca++){const jc=la[Ca];if(!Ib.test(jc.tagName)&&ic.test(jc.style.position||ym(jc,"position"))){var zb=jc;break b}}zb=null}break a}}}catch{}zb=null}zb&&zb.offsetWidth*zb.offsetHeight<=e.width*e.height*4&&(k=1);b.pfx=k}a:{if(Math.random()<.05&&f)try{const ma=f.document.getElementsByTagName("head")[0];var Aa=ma?t0(ma):0;break a}catch(ma){}Aa=0}f=Aa;f!==0&&(b.cms=f);d.google_lrv!==a.Ea&&(b.alvm=d.google_lrv|| 
"none")}function P0(a,b){let c=0;a.location&&a.location.ancestorOrigins?c=a.location.ancestorOrigins.length:Vd(()=>{c++;return!1},a);c&&(b.nhd=c)}function Q0(a,b){const c=iI(b,8,{});b=iI(b,9,{});const d=a.google_ad_section,e=a.google_ad_format;a=a.google_ad_slot;e?c[d]=c[d]?c[d]+`,${e}`:e:a&&(b[d]=b[d]?b[d]+`,${a}`:a)} 
function R0(a,b,c){const d=a.F;var e=a.F;b.dt=rr;e.google_async_iframe_id&&e.google_bpp&&(b.bpp=e.google_bpp);a:{try{var f=r.performance;if(f&&f.timing&&f.now){var g=f.timing.navigationStart+Math.round(f.now())-f.timing.domLoading;break a}}catch(m){}g=null}(e=(e=g)?QW(e,r.Date.now()-rr,1E6):null)&&(b.bdt=e);b.idt=QW(a.G,rr);e=a.F;b.shv=a.pageState.g().g()||E(a.Z,2);a.Ea&&(b.mjsv=a.Ea);e.google_loader_used=="sd"?b.ptt=5:e.google_loader_used=="aa"&&(b.ptt=9);/^\w{1,3}$/.test(e.google_loader_used)&& 
(b.saldr=e.google_loader_used);if(e=Yl(a.pubWin))b.is_amp=1,b.amp_v=Zl(e),(e=$l(e))&&(b.act=e);e=a.pubWin;e==e.top&&(b.abxe=1);e=new LR(a.pubWin);(g=HR(e,"__gads",c))?b.cookie=g:c.g()&&YN(e.B)&&(b.cookie_enabled="1");(g=HR(e,"__gpi",c))&&!g.includes("&")&&(b.gpic=g);HR(e,"__gpi_opt_out",c)==="1"&&(b.pdopt="1");e=new eX(a.pubWin);g={pg:!1,qg:!a.H};(f=dX(e,c,g))?b.eo_id_str=f:cX(e,c,g)&&(b.eoidce="1");c=dI();g=iI(c,8,{});e=d.google_ad_section;g[e]&&(b.prev_fmts=g[e]);g=iI(c,9,{});g[e]&&(b.prev_slotnames= 
g[e].toLowerCase());Q0(d,c);e=iI(c,15,0);e>0&&(b.nras=String(e));(g=Yl(window))?(g?(e=g.pageViewId,g=g.clientId,typeof g==="string"&&(e+=g.replace(/\D/g,"").substring(0,6))):e=null,e=+e):(e=Je(window),g=e.google_global_correlator,g||(e.google_global_correlator=g=1+Math.floor(Math.random()*8796093022208)),e=g);b.correlator=iI(c,7,e);O(Ex)&&(b.rume=1);if(d.google_ad_channel){e=iI(c,10,{});g="";f=d.google_ad_channel.split(J0);for(var h=0;h<f.length;h++){var k=f[h];e[k]?g+=k+"+":e[k]=!0}b.pv_ch=g}if(d.google_ad_host_channel){e= 
d.google_ad_host_channel;g=iI(c,11,[]);f=e.split("|");c=-1;e=[];for(h=0;h<f.length;h++){k=f[h].split(J0);g[h]||(g[h]={});let m="";for(let n=0;n<k.length;n++){const p=k[n];p!==""&&(g[h][p]?m+="+"+p:g[h][p]=!0)}m=m.slice(1);e[h]=m;m!==""&&(c=h)}g="";if(c>-1){for(f=0;f<c;f++)g+=e[f]+"|";g+=e[c]}b.pv_h_ch=g}b.frm=d.google_iframing;b.ife=d.google_iframing_environment;a:{c=d.google_ad_client;try{const m=Je(window);let n=m.google_prev_clients;n||(n=m.google_prev_clients={});if(c in n){var l=1;break a}n[c]= 
!0;l=2;break a}catch{l=0;break a}l=void 0}b.pv=l;a.L&&O(lw)&&(l=a.L,l=bc()&&G0(l)?l.document.documentElement.lang:void 0,l&&(b.tl=l));O(mw)&&a.pubWin.location.host.endsWith("h5games.usercontent.goog")&&(b.cdm=a.pubWin.location.host);P0(a.pubWin,b);(a=d.google_ad_layout)&&jW[a]>=0&&(b.rplot=jW[a])} 
function S0(a,b){a=a.j;nI()&&(b.npa=1);if(a){si(a,3)!=null&&(b.gdpr=a.l()?"1":"0");var c=Ji(a,1);c&&(b.us_privacy=c);(c=Ji(a,2))&&(b.gdpr_consent=c);(c=Ji(a,4))&&(b.addtl_consent=c);(c=Ki(a,7))&&(b.tcfe=c);(c=E(a,11))&&(b.gpp=c);(a=Uh(a,10,Ug,1,void 0,1024))&&a.length>0&&(b.gpp_sid=a.join(","))}}function T0(a,b){const c=a.F;S0(a,b);be(u0,(d,e)=>{b[d]=c[e]});GV(c)&&(a=FV(c),b.fa=a);b.pi||c.google_ad_slot==null||(a=Dz(c),st(a)&&(a=Gt(a.getValue()),b.pi=a))} 
function U0(a,b){var c=bm()||yU(a.pubWin.top);c&&(b.biw=c.width,b.bih=c.height);c=a.pubWin;c!==c.top&&(a=yU(a.pubWin))&&(b.isw=a.width,b.ish=a.height)}function V0(a,b){var c=a.pubWin;c!==null&&c!=c.top?(a=[c.document.URL],c.name&&a.push(c.name),c=yU(c,!1),a.push(c.width.toString()),a.push(c.height.toString()),a=de(a.join(""))):a=0;a!==0&&(b.ifk=a)}function W0(a,b){(a=lI()[a.F.google_ad_client])&&(b.psts=a.join())}function X0(a,b){a=a.pageState.g();(a=ui(a,2))&&a>=0&&(b.tmod=a)} 
function Y0(a,b){(a=a.pubWin.google_user_agent_client_hint)&&(b.uach=Ye(a))}function Z0(a,b){try{const e=a.pubWin&&a.pubWin.external&&a.pubWin.external.getHostEnvironmentValue&&a.pubWin.external.getHostEnvironmentValue.bind(a.pubWin.external);if(e){var c=JSON.parse(e("os-mode")),d=parseInt(c["os-mode"],10);d>=0&&(b.wsm=d+1)}}catch{}}function $0(a,b){a.F.google_ad_public_floor>=0&&(b.pubf=a.F.google_ad_public_floor);a.F.google_ad_private_floor>=0&&(b.pvtf=a.F.google_ad_private_floor)} 
function a1(a,b){const c=Number(a.F.google_traffic_source);c&&Object.values(Ka).includes(c)&&(b.trt=a.F.google_traffic_source)}function b1(a,b){var c;if(c=!O(Kx))c=a.C?.label,c=!(O(rx)&&c&&c.match(Mx(px)));c&&("runAdAuction"in a.pubWin.navigator&&"joinAdInterestGroup"in a.pubWin.navigator&&(b.td=1),c=a.pubWin.navigator,a.pubWin.isSecureContext&&"runAdAuction"in c&&c.runAdAuction instanceof Function&&yR("run-ad-auction",a.pubWin.document)&&(c=new w0,c.set(1,zR(a.pubWin.navigator)),b.tdf=v0(c)))} 
function c1(a,b){if(navigator.deprecatedRunAdAuctionEnforcesKAnonymity){var c=new E0;var d=new D0;d=Ri(d,4,"deprecated_kanon");c=A(c,2,d)}a.C!=null&&bc()&&(c??(c=new E0),d=Ri(c,3,a.C.label),G(d,4,a.C.status));c&&(b.psd=Ye(Ui(c)))}function d1(a,b){O(Bx)||yR("attribution-reporting",a.pubWin.document)&&(b.nt=1)} 
function e1(a,b){if(typeof a.F.google_privacy_treatments==="string"){var c=new Map([["disablePersonalization",1]]);a=a.F.google_privacy_treatments.split(",");var d=[];for(const [e,f]of c.entries())c=f,a.includes(e)&&d.push(c);d.length&&(b.ppt=d.join("~"))}}function f1(a,b){if(a.l){a.l.hk&&(b.xatf=1);try{a.l.Of?.disconnect(),a.l.Of=void 0}catch{}}}function g1(a,b=document){if(O(uv))try{const {labels:c}=z0(b);c.length&&(a.pgls=c.map(d=>{d=y0(d);return We(d)}).join("~"))}catch(c){eC.ma(1278,c)}} 
function h1(a,b){const c={};T0(a,c);Y0(a,c);R0(a,c,b);c.u_tz=-(new Date).getTimezoneOffset();try{var d=Cl.history.length}catch(e){d=0}c.u_his=d;c.u_h=Cl.screen?.height;c.u_w=Cl.screen?.width;c.u_ah=Cl.screen?.availHeight;c.u_aw=Cl.screen?.availWidth;c.u_cd=Cl.screen?.colorDepth;c.u_sd=zU(a.pubWin);c.dmc=a.pubWin.navigator?.deviceMemory;hC(889,()=>{if(a.L==null)c.adx=-12245933,c.ady=-12245933;else{var e=CU(a.L,a.ba);c.adx&&c.adx!=-12245933&&c.ady&&c.ady!=-12245933||(c.adx=Math.round(e.x),c.ady=Math.round(e.y)); 
BU(a.ba)||(c.adx=-12245933,c.ady=-12245933,a.g|=32768)}});U0(a,c);V0(a,c);M0(a,c);L0(a,c);c.oid=2;W0(a,c);c.pvsid=Ge(a.pubWin,eC);X0(a,c);Z0(a,c);c.uas=I0(a.pubWin);(d=C0(a.pubWin))&&(c.nvt=d);a.D&&(c.scar=a.D);a.A instanceof Uint8Array?c.topics=We(a.A):a.A&&(c.topics=a.A,c.tps=a.A);f1(a,c);O0(a,c,b);c.fu=a.g;c.bc=x0();if(CT(a.pageState.g())?BT(a.pageState.g()):C(a.Z,9))if(OR(c),c.creatives=N0(/\b(?:creatives)=([\d,]+)/),c.adgroups=N0(/\b(?:adgroups)=([\d,]+)/),c.adgroups||c.sso)c.adtest="on",c.disable_budget_throttling= 
!0,c.use_budget_filtering=!1,c.retrieve_only=!0,c.disable_fcap=!0;Il()&&(c.atl=!0);c.bz=Ke(a.pubWin);$0(a,c);a1(a,c);b1(a,c);c1(a,c);d1(a,c);e1(a,c);String(a.F.google_special_category_data)==="true"&&(c.scd=1);g1(c,a.pubWin.document);return c}const i1=/YtLoPri/;var j1=class extends H{};function k1(a){return mi(a,j1,15,Th())}var l1=class extends H{},m1=Hk(l1);function n1(){var a=new o1;var b=new vu;b=Si(b,2,4);b=Si(b,8,1);var c=new Ct;c=Qi(c,7,"#dpId");b=A(b,1,c);return ri(a,3,vu,b)}var o1=class extends H{},p1=Hk(o1);function q1(a,b){return E(a,10).replace("TERM",b)};var r1=class{constructor(a){this.Kb=a.Kb??[];this.Af=!!a.Af;this.Cf=!!a.Cf;this.Bf=!!a.Bf;this.ve=a.ve??250;this.ue=a.ue??300;this.Ye=a.Ye??15E3;this.Xe=a.Xe??15E3;this.Ze=a.Ze??0;this.me=a.me??0;this.qe=a.qe??670;this.wd=!!a.wd;this.Jf=a.Jf??[];this.Ge=a.Ge||"450px";this.He=!!a.He;this.Uf=!!a.Uf;this.pe=a.pe??0;this.md=!!a.md;this.nd=!!a.nd;this.sg=a.sg??!0;this.je=a.je??0;this.Zh=!!a.Zh;this.te=a.te??0;this.yc=a.yc??0;this.eg=new Set(a.eg??[]);this.rg=!!a.rg;this.Lb=a.Lb??0}};function s1(a,b,c,d,e,f,g,h,k){const l=k(999,a.top,m=>{m.data.action==="init"&&m.data.adChannel==="ShoppingVariant"&&t1(a,b,d,c,e,f,g,h)});g(()=>{a.top.removeEventListener("message",l)})}function t1(a,b,c,d,e,f,g,h){C(f,13)||iE(c,d,e);const k=b.contentDocument.documentElement,l=new ResizeObserver(()=>{b.height=`${Math.ceil(k.offsetHeight+26)}px`});l.observe(k);const m=h(1066,a,()=>{const n=k.offsetHeight;n&&(b.height=`${n+26}px`)},1E3);g(()=>{l.disconnect();a.clearInterval(m)})} 
var u1=class{constructor(a){this.Yb=a}kf(a){const b=a.B.document.createElement("iframe"),c=a.R,d=new jE({Ca:b,Na:E(c,16),jc:"anno-cse",Za:this.Yb.replace("ca","partner"),yd:"ShoppingVariant",location:a.B.location,language:E(c,7),tc:q1(c,a.sa),Wa:a.K.Kb.filter(e=>e!==42),Ya:!1,Pb:void 0,od:!0,Bg:void 0,Xa:!0});d.init();s1(a.B,b,a.sa,d,a.wg,c,a.Ta,a.Tb,a.Bb);return b}};function v1(a){var b={},c=a.sa;const d=a.Ej;var e=a.Yb,f=a.Nf;const g=a.Pd,h=a.Hi,k=a.Nj,l=a.nk,m=a.yl,n=a.eids,p=a.Zk,w=a.Fk,u=a.Gk;var t=a.Ik,B=a.Ii;const I=b&&b.Oc;a=b&&b.nj;b=CD;f=(m?"":'<link href="https://fonts.googleapis.com/css?family=Google+Sans:500" rel="stylesheet"'+(I?' nonce="'+X(aE(I))+'"':"")+">")+"<style"+(I?' nonce="'+X(aE(I))+'"':"")+">#gda-search-term {height: 24px; font-size: 18px; font-weight: 500; color: #202124; font-family: 'Google Sans'; padding-bottom: 6px;"+(l?"padding-right: 16px;": 
"padding-left: 16px;")+"}"+(k?"#display-slot {display: inline-block; height: "+Z(h)+"; width: "+Z(g)+"px;}":"")+'</style><div id="gda-search-term">'+AD(c)+"</div>"+(u!==-1?"<script"+(a?' nonce="'+X(aE(a))+'"':"")+">window["+MD(ND(w))+"] = "+MD(ND(u))+";\x3c/script>":"")+(f!==""?'<meta name="google-adsense-platform-account" content="'+X(f)+'">':"")+'<ins id="display-slot" class="adsbygoogle"'+(k?"":' style="display:inline-block;width:'+X(Z(g))+"px;height:calc("+X(Z(h))+')"')+' data-ad-client="'+X(e)+ 
'"></ins>'+(p?"<script"+(a?' nonce="'+X(aE(a))+'"':"")+">(adsbygoogle=window.adsbygoogle||[]).requestNonPersonalizedAds=1;\x3c/script>":"")+(m?"<script"+(a?' nonce="'+X(aE(a))+'"':"")+">const el = document.querySelector('ins.adsbygoogle'); el.dir = 'ltr'; el.style.backgroundColor = 'lightblue'; el.style.fontSize = '25px'; el.style.textDecoration = 'none'; el.textContent = \"Loading display ads inside this slot for query = "+String(c).replace(OD,PD)+' and " + "property code = '+String(e).replace(OD, 
PD)+'";\x3c/script>':"")+"<script"+(a?' nonce="'+X(aE(a))+'"':"")+">top.postMessage({'action':'sgda-ready'}, top.location.origin);\x3c/script>";m?e="":(c='<script data-ad-intent-query="'+X(c)+'" data-ad-intent-qetid="'+X(d)+'" data-ad-intent-eids="'+X(n)+'"',t?(BD(t,tD)||BD(t,uD)?t=String(t).replace(TD,UD):Rc(t)?t=SD(Sc(t)):t instanceof Mc?t=SD(Oc(t).toString()):(t=String(t),t=YD.test(t)?t.replace(TD,UD):"about:invalid#zSoyz"),t=' data-page-url="'+X(t)+'"'):t="",B=c+t+' data-ad-intents-format="'+ 
X(B)+'" async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=',e=encodeURIComponent(String(e)),QD.lastIndex=0,e=QD.test(e)?e.replace(QD,RD):e,e=B+e+'" crossorigin="anonymous"'+(a?' nonce="'+X(aE(a))+'"':"")+">\x3c/script>");return b(f+e)};function w1(a,b){return a?.95*b.innerHeight-30:b.innerHeight};var A1=class{constructor(a,b,c,d){this.Yb=a;this.Nf=b;this.g=c;this.ic=d}kf(a){var b=v1({sa:a.sa,Ej:a.lh||"",Yb:this.Yb,Nf:this.Nf??"",Pd:a.Pd,Hi:a.K.He?a.K.Ge.replace("<DH>",`${w1(a.qa,a.B)}px`):a.K.Ge,Nj:a.K.He,nk:a.aa,yl:!!C(a.R,13),eids:a.K.Jf.join(","),Zk:a.K.sg,Fk:"goog_pvsid",Gk:this.g,Ik:this.ic,Ii:a.format});b=Kd("body",{dir:a.aa?"rtl":"ltr",lang:E(a.R,7),style:"margin:0;height:100%;padding-top:0;overflow:hidden"},xD(b));const c=a.B.document.createElement("iframe");v(c,{border:"0",width:"100%"}); 
c.height="24px";const d=a.Bb(999,a.B,e=>{if(e.data.action==="sgda-ready"&&(e=c.contentDocument,e?.body)){var f=e.getElementById("display-slot");if(f){var g=e.createElement("iframe"),h=new MutationObserver((k,l)=>{if(f.getAttribute("data-ad-status")==="unfilled")km(f);else if(O(Gw)&&f.getAttribute("data-ad-status")==="filled")g.contentDocument?.body&&(g.contentDocument.body.innerText="");else if(f.getAttribute("data-ad-status")!=="filled")return;x1(g,c);y1(a.B,g,c,a.Ta,a.Tb);l.disconnect()});h.observe(f, 
{attributes:!0,attributeFilter:["data-ad-status"]});a.Ta(()=>void h.disconnect());z1(this.Yb,g,a);e.body.append(g)}}});a.Ta(()=>{a.B.removeEventListener("message",d)});c.srcdoc=dd(b);return c}}; 
function z1(a,b,c){const d=new jE({Ca:b,Na:E(c.R,16),jc:"anno-cse",Za:a.replace("ca","partner"),yd:"ShoppingVariant",location:c.B.location,language:E(c.R,7),tc:q1(c.R,c.sa),Wa:c.K.Kb.filter(f=>f!==42),Ya:!1,Pb:void 0,od:!0,Bg:void 0,Xa:!0,Cg:!0}),e=c.Bb(999,c.B,f=>{f.data.action==="init"&&f.data.adChannel==="ShoppingVariant"&&(C(c.R,13)||iE(d,c.sa,c.wg))});d.init();c.Ta(()=>{c.B.removeEventListener("message",e)})} 
function y1(a,b,c,d,e){const f=b.contentDocument.documentElement,g=new ResizeObserver(()=>void x1(b,c));g.observe(f);const h=e(1066,a,()=>void x1(b,c),1E3);d(()=>{g.disconnect();a.clearInterval(h)})}function x1(a,b){const c=a.contentDocument?.documentElement?.offsetHeight;if(O(Gw)){if(c===void 0)return}else if(!c)return;const d=b.contentDocument?.getElementById("display-slot")?.offsetHeight??0;a.height=`${Math.ceil(c+26)}px`;b.height=`${Math.ceil(c+26+d)}px`};function B1(a,b){a=Kz(cz([...b],a),a);if(a.length!==0)return a.reduce((c,d)=>c.na.g>d.na.g?c:d)};async function C1(a,b){q(await q(new Promise(c=>void a.B.setTimeout(c,0))));a.i=a.g.ka(b)+a.j}var D1=class{constructor(a,b){var c=W(Uw);this.B=a;this.g=b;this.j=c;this.i=b.ka(2)+c}};var E1=class{constructor(a){this.performance=a}ka(){return this.performance.now()}},F1=class{ka(){return Date.now()}};const G1=[255,255,255];function H1(a){function b(d){return[Number(d[1]),Number(d[2]),Number(d[3]),d.length>4?Number(d[4]):1]}var c=a.match(/rgb\(([0-9]+),\s*([0-9]+),\s*([0-9]+)\)/);if(c||(c=a.match(/rgba\(([0-9]+),\s*([0-9]+),\s*([0-9]+),\s*([0-9\\.]+)\)/)))return b(c);if(a==="transparent"||a==="")return[0,0,0,0];throw Error(`Invalid color: ${a}`);} 
function I1(a){var b=getComputedStyle(a);if(b.backgroundImage!=="none")return null;b=H1(b.backgroundColor);var c=J1(b);if(c)return c;a=(a=a.parentElement)?I1(a):G1;if(!a)return null;c=b[3];return[Math.round(c*b[0]+(1-c)*a[0]),Math.round(c*b[1]+(1-c)*a[1]),Math.round(c*b[2]+(1-c)*a[2])]}function J1(a){return a[3]===1?[a[0],a[1],a[2]]:null};function K1(a,b){const c=b.qa===b.aa;var d=L1(a,b,c);if(!d)return null;d=d.position.Wd();a=M1(a,d,b,function(f){f=f.getBoundingClientRect();return c?b.T-f.right:f.left});if(!a||a-16<200)return null;const e=b.T;return{Da:c?e-a:16,Ua:c?16:e-a,ha:d}}function N1(a,b){const c=Br(a),d=Cr(a);return nG(new sG(a),new Pl(d-b.ha-50,c-b.Ua,d-b.ha,b.Da)).size>0} 
function L1(a,b,c){b=Math.floor(b.W*.3);return b<66?null:vG(a,{Ec:c?BG({ha:16,Ua:16}):zG({ha:16,Da:16}),ag:b-66,Xb:200,fg:50,ke:b,Eb:16},[a.document.body]).hf}function M1(a,b,c,d){a=c.qa?O1(a,b,c):P1(a,b,c);b=c.T;let e=c.qa?b:b*.35;a.forEach(f=>{e=Math.min(e,d(f))});return e<16?null:e-16}function O1(a,b,c){const d=c.W;return nG(new sG(a),new Pl(d-b-50,c.T-16,d-b,16))}function P1(a,b,c){const d=c.W,e=c.T;c=c.aa;return nG(new sG(a),new Pl(d-b-50,(c?e*.35:e)-16,d-b,(c?16:e*.65)+16))} 
function Q1(a,b,c){const d=a.aa;return{Da:d?R1(a,b,c):c,Ua:d?c:R1(a,b,c),ha:16}}function R1(a,b,c){const d=a.T;return a.qa?d-b+16:Math.max(d-c-d*.35,d-b+16)}function S1(a,b){const c=b.aa,d=b.T;return[...(b.qa?O1(a,16,b):P1(a,16,b))].map(e=>new uG(c?d-e.getBoundingClientRect().right:e.getBoundingClientRect().left,c?d-e.getBoundingClientRect().left:e.getBoundingClientRect().right)).sort((e,f)=>e.start-f.start)};function T1(a){v(a,{border:"0","box-shadow":"none",display:"inline","float":"none",margin:"0",outline:"0",padding:"0"})}function U1(a,b){b=a.document.createElement(b);$u(a,b);v(b,{color:"inherit",cursor:"inherit",direction:"inherit","font-family":"inherit","font-size":"inherit","font-weight":"inherit","text-align":"inherit","text-orientation":"inherit",visibility:"inherit","writing-mode":"inherit"});return b}function V1(a){a.dataset.googleVignette="false";a.dataset.googleInterstitial="false"};function W1(a,b,c){a=X1(a,"100 -1000 840 840",`calc(${b} - 2px)`,b,Y1[c]);v(a,{color:"inherit",cursor:"inherit",fill:"currentcolor"});return a}function Z1(a,b){a=$1(a,"20px","#1A73E8",b);a.classList.add("google-anno-sa-intent-icon");return a} 
function a2(a,b){a=X1(a,"0 -960 960 960","20px","20px","m256-200-56-56 224-224-224-224 56-56 224 224 224-224 56 56-224 224 224 224-56 56-224-224-224 224Z");v(a,{left:"13px",right:"","pointer-events":"initial",position:"absolute",top:"15px",transform:"none",fill:"#1A73E8"});a.role="button";a.ariaLabel=b;a.tabIndex=0;return a} 
const Y1={[0]:"M503-104q-24 24-57 24t-57-24L103-390q-23-23-23-56.5t23-56.5l352-353q11-11 26-17.5t32-6.5h286q33 0 56.5 23.5T879-800v286q0 17-6.5 32T855-456L503-104Zm196-536q25 0 42.5-17.5T759-700q0-25-17.5-42.5T699-760q-25 0-42.5 17.5T639-700q0 25 17.5 42.5T699-640ZM446-160l353-354v-286H513L160-446l286 286Zm353-640Z",[1]:"m274-274-128-70 42-42 100 14 156-156-312-170 56-56 382 98 157-155q17-17 42.5-17t42.5 17q17 17 17 42.5T812-726L656-570l98 382-56 56-170-312-156 156 14 100-42 42-70-128Z",[2]:"M784-120 532-372q-30 24-69 38t-83 14q-109 0-184.5-75.5T120-580q0-109 75.5-184.5T380-840q109 0 184.5 75.5T640-580q0 44-14 83t-38 69l252 252-56 56ZM380-400q75 0 127.5-52.5T560-580q0-75-52.5-127.5T380-760q-75 0-127.5 52.5T200-580q0 75 52.5 127.5T380-400Z", 
[3]:"M480-80q-83 0-156-31.5T197-197q-54-54-85.5-127T80-480q0-83 31.5-156T197-763q54-54 127-85.5T480-880q83 0 156 31.5T763-763q54 54 85.5 127T880-480q0 83-31.5 156T763-197q-54 54-127 85.5T480-80Zm200-500 54-18 16-54q-32-48-77-82.5T574-786l-54 38v56l160 112Zm-400 0 160-112v-56l-54-38q-54 17-99 51.5T210-652l16 54 54 18Zm-42 308 46-4 30-54-58-174-56-20-40 30q0 65 18 118.5T238-272Zm242 112q26 0 51-4t49-12l28-60-26-44H378l-26 44 28 60q24 8 49 12t51 4Zm-90-200h180l56-160-146-102-144 102 54 160Zm332 88q42-50 60-103.5T800-494l-40-28-56 18-58 174 30 54 46 4Z", 
[4]:"M120-680v-160l160 80-160 80Zm600 0v-160l160 80-160 80Zm-280-40v-160l160 80-160 80Zm0 640q-76-2-141.5-12.5t-114-26.5Q136-135 108-156t-28-44v-360q0-25 31.5-46.5t85.5-38q54-16.5 127-26t156-9.5q83 0 156 9.5t127 26q54 16.5 85.5 38T880-560v360q0 23-28 44t-76.5 37q-48.5 16-114 26.5T520-80v-160h-80v160Zm40-440q97 0 167.5-11.5T760-558q0-5-76-23.5T480-600q-128 0-204 18.5T200-558q42 15 112.5 26.5T480-520ZM360-166v-154h240v154q80-8 131-23.5t69-27.5v-271q-55 22-138 35t-182 13q-99 0-182-13t-138-35v271q18 12 69 27.5T360-166Zm120-161Z", 
[5]:"M200-80q-33 0-56.5-23.5T120-160v-480q0-33 23.5-56.5T200-720h80q0-83 58.5-141.5T480-920q83 0 141.5 58.5T680-720h80q33 0 56.5 23.5T840-640v480q0 33-23.5 56.5T760-80H200Zm0-80h560v-480H200v480Zm280-240q83 0 141.5-58.5T680-600h-80q0 50-35 85t-85 35q-50 0-85-35t-35-85h-80q0 83 58.5 141.5T480-400ZM360-720h240q0-50-35-85t-85-35q-50 0-85 35t-35 85ZM200-160v-480 480Z",[6]:"M80-160v-120h80v-440q0-33 23.5-56.5T240-800h600v80H240v440h240v120H80Zm520 0q-17 0-28.5-11.5T560-200v-400q0-17 11.5-28.5T600-640h240q17 0 28.5 11.5T880-600v400q0 17-11.5 28.5T840-160H600Zm40-120h160v-280H640v280Zm0 0h160-160Z", 
[7]:"M400-40v-80H200q-33 0-56.5-23.5T120-200v-560q0-33 23.5-56.5T200-840h200v-80h80v880h-80ZM200-240h200v-240L200-240Zm360 120v-360l200 240v-520H560v-80h200q33 0 56.5 23.5T840-760v560q0 33-23.5 56.5T760-120H560Z",[8]:"M300-240q25 0 42.5-17.5T360-300q0-25-17.5-42.5T300-360q-25 0-42.5 17.5T240-300q0 25 17.5 42.5T300-240Zm0-360q25 0 42.5-17.5T360-660q0-25-17.5-42.5T300-720q-25 0-42.5 17.5T240-660q0 25 17.5 42.5T300-600Zm180 180q25 0 42.5-17.5T540-480q0-25-17.5-42.5T480-540q-25 0-42.5 17.5T420-480q0 25 17.5 42.5T480-420Zm180 180q25 0 42.5-17.5T720-300q0-25-17.5-42.5T660-360q-25 0-42.5 17.5T600-300q0 25 17.5 42.5T660-240Zm0-360q25 0 42.5-17.5T720-660q0-25-17.5-42.5T660-720q-25 0-42.5 17.5T600-660q0 25 17.5 42.5T660-600ZM200-120q-33 0-56.5-23.5T120-200v-560q0-33 23.5-56.5T200-840h560q33 0 56.5 23.5T840-760v560q0 33-23.5 56.5T760-120H200Zm0-80h560v-560H200v560Zm0-560v560-560Z", 
[9]:"M160-80v-440H80v-240h208q-5-9-6.5-19t-1.5-21q0-50 35-85t85-35q23 0 43 8.5t37 23.5q17-16 37-24t43-8q50 0 85 35t35 85q0 11-2 20.5t-6 19.5h208v240h-80v440H160Zm400-760q-17 0-28.5 11.5T520-800q0 17 11.5 28.5T560-760q17 0 28.5-11.5T600-800q0-17-11.5-28.5T560-840Zm-200 40q0 17 11.5 28.5T400-760q17 0 28.5-11.5T440-800q0-17-11.5-28.5T400-840q-17 0-28.5 11.5T360-800ZM160-680v80h280v-80H160Zm280 520v-360H240v360h200Zm80 0h200v-360H520v360Zm280-440v-80H520v80h280Z",[10]:"m456-200 174-340H510v-220L330-420h126v220Zm24 120q-83 0-156-31.5T197-197q-54-54-85.5-127T80-480q0-83 31.5-156T197-763q54-54 127-85.5T480-880q83 0 156 31.5T763-763q54 54 85.5 127T880-480q0 83-31.5 156T763-197q-54 54-127 85.5T480-80Zm0-80q134 0 227-93t93-227q0-134-93-227t-227-93q-134 0-227 93t-93 227q0 134 93 227t227 93Zm0-320Z"}; 
function $1(a,b,c,d){a=X1(a,"0 -960 960 960",b,b,Y1[d]);v(a,{fill:c,cursor:"inherit"});return a}function X1(a,b,c,d,e){const f=a.document.createElementNS("http://www.w3.org/2000/svg","path");f.setAttribute("d",e);e=a.document.createElementNS("http://www.w3.org/2000/svg","svg");$u(a,e);e.setAttribute("viewBox",b);e.setAttribute("width",c);e.setAttribute("height",d);T1(e);e.appendChild(f);return e};function b2(a,b,c,d){const e=document.createElement("SPAN");$u(a,e);e.id="gda";e.appendChild(a2(a,E(b.R,18)));V1(e);c2(b,1064,e,f=>{d?.();km(c);f.preventDefault();f.stopImmediatePropagation();return!1});return e} 
function d2(a,b,c,d,e){const f=document.createElement("SPAN");$u(a,f);T1(f);v(f,{position:"absolute",top:"2.5px",bottom:"2.5px",left:(b.aa(),"50px"),right:b.aa()?"24px":"12px",display:"flex","flex-direction":"row",color:"#1A73E8",cursor:"pointer",transition:"width 5s"});b.qa||v(f,{"justify-content":""});const g=Z1(a,b.g.get(d.za)||0),h=document.createElement("SPAN");v(h,{display:"inline-block",cursor:"inherit"});v(h,{"margin-left":b.aa()?"6px":"4px","margin-right":b.aa()?"4px":"6px","margin-top":"12px"}); 
f.appendChild(h);h.appendChild(g);c.classList?.add("google-anno-sa-qtx","google-anno-skip");c.tabIndex=0;c.role="link";c.ariaLive="polite";e2(c,d.za,E(b.R,19));v(c,{height:"40px","align-items":"center","line-height":"44px","font-size":"16px","font-weight":"400","font-style":"normal","font-family":"Roboto","text-overflow":"ellipsis","white-space":"nowrap",overflow:"hidden","-webkit-tap-highlight-color":"transparent",color:"#1A73E8"});V1(f);c2(b,999,f,k=>{k.preventDefault();if((d.Rg??0)+800<=b.ka(26)){k= 
d.za;const m=b.A.get(k)||"";var l=Fp(Dp(k),d.gd);l=Mh(l,3,Hg(d.ee));l=b.U.Jc(l);f2(e,a,b,l,k,m,2,b.K.wd?b.j.get(k)||"":null)}return!1});f.appendChild(c);return f} 
function g2(a,b,c,d,e,f){const g=document.createElement("div");$u(a,g);g.id="google-anno-sa";g.dir=b.aa()?"rtl":"ltr";g.tabIndex=0;v(g,{background:"#FFFFFF","border-style":"solid",bottom:`${d.ha}px`,"border-radius":"16px",height:"50px",position:"fixed","text-align":"center",border:"0px",left:`${d.Da}px`,right:`${d.Ua}px`,"box-shadow":"0px 1px 2px rgba(0, 0, 0, 0.3), 0px 1px 3px 1px rgba(0, 0, 0, 0.15)","z-index":"1000"});v(g,{fill:"white"});d=document.createElement("SPAN");$u(a,d);v(d,{cursor:"inherit"}); 
g.appendChild(d2(a,b,d,c,f));g.appendChild(b2(a,b,g,e));return g}function h2(a,b,c,d,e){var f=c.getElementsByClassName("google-anno-sa-qtx")[0];f instanceof HTMLElement&&(f.innerText=a.za);if((d.g.get(e)||0)!==(d.g.get(a.za)||0)){b=Z1(b,d.g.get(a.za)||0);for(const g of c.getElementsByClassName("google-anno-sa-intent-icon"))g.replaceWith(b)}e2(f,a.za,E(d.R,19));c=d.U;d=c.bf;f=new gp;f=Mh(f,2,Wg(a.gd));a=Ri(f,4,a.za);return d.call(c,a)} 
function i2(a,b,c,d,e){if(N1(b,d))return null;a.Rg=c.ka(25);d=g2(b,c,a,d,()=>{a.g=!0;var f=c.U,g=f.We;var h=new ep;h=di(h,3,Wg(a.gd),"0");h=Ri(h,2,a.za);g.call(f,h)},e);e=h2(a,b,d,c,a.za);b.document.body.appendChild(d);return e}function j2(a,b,c,d,e,f,g,h){if(!(a.g||a.za===e&&a.gd===d&&a.i===f)){if(a.ee!==null){var k=a.ee,l=c.U,m=l.af,n=new fp;k=Pi(n,1,k);m.call(l,k)}l=a.za;a.za=e;a.gd=d;a.i=f;C(c.R,17)||(d=b.document.getElementById("google-anno-sa"),a.ee=d?h2(a,b,d,c,l):i2(a,b,c,g,h))}} 
var k2=class{constructor(){this.za="";this.gd=null;this.i="";this.ee=null;this.g=!1;this.Rg=null}};function e2(a,b,c){a.ariaLabel=c.replace("TERM",b)};function l2(a,b){a.g>=a.j.length&&(a.g=0,a.l++);if(!(a.config.K.me&&a.l>=a.config.K.me))if(a.i.g)a.i.Ta(()=>void l2(a,b));else{var c=a.j[a.g++];a.A=!1;j2(a.H,a.B,a.config,c.g,c.sa,c.i,a.C,a.i);m2(a.config,898,a.B,()=>void l2(a,b),a.Hg)}}var n2=class{constructor(a,b,c,d){var e=new k2;this.B=a;this.config=b;this.H=e;this.C=c;this.i=d;this.j=[];this.A=!0;this.l=this.g=0;this.Hg=b.params.Hg}};class o2{constructor(a,b,c){this.g=a;this.sa=b;this.i=c}};function p2(a){return a.ne>0&&a.i.j>=a.ne}var r2=class{constructor(a,b,c,d){this.xg=b;this.gf=c;this.ne=d;this.g=0;this.i=new q2(a)}};function s2(a,b){b-=a.A;for(const c of a.g.keys()){const d=a.g.get(c);let e=0;for(;e<d.length&&d[e]<b;)e++;a.i-=e;e>0&&a.g.set(c,d.slice(e))}}function t2(a,b,c){let d=[];a.g.has(b)&&(d=a.g.get(b));d.push(c);a.i++;a.g.set(b,d)}class q2{constructor(a){this.A=a;this.g=new Map;this.i=0}get j(){return this.i}};function u2(a,b,c,d){const e=U1(a,"div");e.classList.add("google-anno-skip","google-anno-sc");d=a.getComputedStyle(d).fontSize||"16px";var f=e.appendChild;var g=b.g.get(c)||0;g=$1(a,d,"#FFFFFF",g);v(g,{position:"relative",top:"3px"});const h=U1(a,"span");v(h,{display:"inline-block","padding-left":b.aa()?"":"3px","padding-right":b.aa()?"3px":""});h.appendChild(g);f.call(e,h);f=e.appendChild;g=U1(a,"span");g.appendChild(a.document.createTextNode(c));v(g,{position:"relative",left:b.aa()?"":"3px",right:b.aa()? 
"3px":"","padding-left":b.aa()?"6px":"","padding-right":b.aa()?"":"6px"});f.call(e,g);v(e,{display:"inline-block","border-radius":"20px","padding-left":b.aa()?"7px":"6px","padding-right":b.aa()?"6px":"7px","padding-top":"3px","padding-bottom":"3px","border-width":"1px","border-style":"solid",color:"#FFFFFF","font-family":"Roboto","font-weight":"500","font-size":d,"border-color":"#D7D7D7",background:"#0B57D0",cursor:"pointer","margin-top":"-3px"});e.tabIndex=0;e.role="link";e.ariaLabel=c;return e} 
;const v2=["BTN","BUTTON"]; 
function w2(a){switch(a.tagName?.toUpperCase?.()){case "IFRAME":case "A":case "AUDIO":case "BUTTON":case "CANVAS":case "CITE":case "CODE":case "EMBED":case "FOOTER":case "FORM":case "KBD":case "LABEL":case "OBJECT":case "PRE":case "SAMP":case "SCRIPT":case "SELECT":case "STYLE":case "SUB":case "SUPER":case "SVG":case "TEXTAREA":case "TIME":case "VAR":case "VIDEO":case null:return!1}return(a.className.toUpperCase?.()?.includes("CRUMB")||a.id.toUpperCase?.()?.includes("CRUMB"))&&a.offsetHeight<=50|| 
x2(a)?!1:!a.classList?.contains("google-anno-skip")&&!a.classList?.contains("adsbygoogle")}function x2(a){return v2.some(b=>a.className.toUpperCase?.()?.includes(b)||a.id.toUpperCase?.()?.includes(b))};function y2(a,b,c){b=b.getBoundingClientRect();a=xp(wp(new yp,a),3);c=Ri(a,4,c);c=Oi(c,6,Math.round(b.x));return Oi(c,7,Math.round(b.y))}function z2(a){a=H1(a);var b=new up;b=Oi(b,1,a[0]);b=Oi(b,2,a[1]);b=Oi(b,3,a[2]);return di(b,4,pg(a[3]),0)};const A2=/[\s!'",:;\\(\\)\\?\\.\u00bf\u00a1\u30a0\uff1d\u037e\u061f\u3002\uff1f\uff1b\uff1a\u2014\u2014\uff5e\u300a\u300b\u3008\u3009\uff08\uff09\u300c\u300d\u3001\u00b7\u2026\u2025\uff01\uff0c\u00b7\u2019\u060c\u061b\u060d\u06d4\u0648]/;function B2(a,b){switch(b){case 1:return!0;default:return a===""||A2.test(a)}};function C2(a,b){const c=new D2(b);for(const d of a)E(d,5)&&yi(d,3).forEach(e=>{E2(c,e,e)});F2(c);return new G2(c)}function H2(a,b){b=a.match(b);a=new Map;for(const c of b)if(b=c.j,a.has(b)){const d=a.get(b);c.length>d.length&&a.set(b,c)}else a.set(b,c);return[...a.values()]}var G2=class{constructor(a){this.g=a}isEmpty(){return this.g.isEmpty()}match(a){return this.g.match(a)}}; 
function E2(a,b,c){const d=a.i.has(c)?a.i.get(c):a.A++;a.i.set(c,d);a.l.set(d,c);c=0;for(let e=0;e<b.length;e++){const f=b.charCodeAt(e);a.g[c].contains(f)||(a.g.push(new I2),a.g[a.size].l=c,a.g[a.size].H=f,a.g[c].j.set(f,a.size),a.size++);c=a.g[c].j.get(f)}a.g[c].A=!0;a.g[c].C=d;a.g[c].D=a.j.length;a.j.push(b.length)} 
function F2(a){const b=[];for(b.push(0);b.length>0;){const f=b.shift();var c=a,d=c.g[f];if(f===0)d.g=0,d.i=0;else if(d.l===0)d.g=0,d.i=d.A?f:c.g[c.g[f].g].i;else{d=c.g[c.g[f].l].g;for(var e=c.g[f].H;;){if(c.g[d].contains(e)){c.g[f].g=c.g[d].j.get(e);break}if(d===0){c.g[f].g=0;break}d=c.g[d].g}c.g[f].i=c.g[f].A?f:c.g[c.g[f].g].i}for(const g of a.g[f].nb)b.push(g)}} 
class D2{constructor(a){this.C=a;this.size=1;this.g=[new I2];this.j=[];this.i=new Map;this.l=new Map;this.A=0}isEmpty(){return this.A===0}match(a){let b=0;const c=[];for(let g=0;g<a.length;g++){for(;;){var d=a.charCodeAt(g),e=this.g[b];if(e.contains(d)){b=e.j.get(d);break}if(b===0)break;b=e.g}let h=b;for(;;){h=this.g[h].i;if(h===0)break;const k=g+1-this.j[this.g[h].D],l=g;d=a;e=l;var f=this.C;B2(d.charAt(k-1),f)&&B2(d.charAt(e+1),f)&&c.push(new J2(k,l,this.l.get(this.g[h].C)));h=this.g[h].g}}return c}} 
class I2{constructor(){this.j=new Map;this.M=!1;this.Ka=this.J=this.G=this.pa=this.V=this.X=-1}contains(a){return this.j.has(a)}set l(a){this.X=a}get l(){return this.X}set H(a){this.V=a}get H(){return this.V}set A(a){this.M=a}get A(){return this.M}set C(a){this.J=a}get C(){return this.J}set g(a){this.pa=a}get g(){return this.pa}set i(a){this.G=a}get i(){return this.G}set D(a){this.Ka=a}get D(){return this.Ka}get nb(){return this.j.values()}} 
var J2=class{constructor(a,b,c){this.i=a;this.g=b;this.l=c}get j(){return this.i}get A(){return this.g}get sa(){return this.l}get length(){return this.g-this.i}};async function K2(a,b,c,d,e,f){const g=C2(k1(b.R),b.i);if(!g.isEmpty()){var h=new Map;for(const k of k1(b.R).filter(l=>E(l,5)))yi(k,3).forEach(l=>{h.set(l,E(k,1))});q(await q(L2(a,a.document.body,b,g,h,new Set,c,d,b.K.pe?new r2(0,0,0,b.K.pe):null,b.K.te||b.K.Lb?new M2(b.K.te,b.K.Lb):null,e,f)))}} 
async function L2(a,b,c,d,e,f,g,h,k,l,m,n){g.g.ka(9)>=g.i&&q(await q(C1(g,10)));if(b.nodeType!==Node.ELEMENT_NODE||w2(b))if(c.K.Uf&&f.size&&b.nodeType===Node.ELEMENT_NODE&&N2(a,b)&&b.parentElement&&!O2(a,c,b.parentElement)&&P2(a,l,b.getBoundingClientRect().top)&&Q2(a,f,c,h,b.parentElement,b,k,l,n),b.nodeType===Node.TEXT_NODE&&b.textContent)H2(d,b.textContent).map(p=>e.get(p.sa)).filter(p=>!!p).forEach(p=>void f.add(p));else{for(const p of b.childNodes)q(await q(L2(a,p,c,d,e,f,g,h,k,l,m,n)));f.size&& 
b.nodeType===Node.ELEMENT_NODE&&["block","table-cell"].includes(a.getComputedStyle(b).display)&&!O2(a,c,b)&&P2(a,l,b.getBoundingClientRect().bottom)&&Q2(a,f,c,h,b,null,k,l,n)}else c.K.Lb&&l&&b.classList?.contains("adsbygoogle")&&b.dataset.adStatus==="filled"&&R2(l,b.getBoundingClientRect().bottom+a.scrollY)} 
function Q2(a,b,c,d,e,f,g,h,k){for(const [m,n]of[...b].entries()){var l=m;const p=n;if(g&&p2(g)||c.K.yc&&l===c.K.yc)return;c.K.yc&&b.delete(p);const w=y2(c.U.Ed(),f??e,p);d.entries.push(Fh(w));g&&t2(g.i,p,g.g);if(C(c.R,17))continue;l=u2(a,c,p,e);const u=S2(l,c,Xg(y(w,10))??"0");V1(l);c2(c,999,l,t=>{try{var B=Fp(Dp(p),Xg(y(w,10))??"0");var I=Mh(B,7,Hg(u.j));const U=c.U.Jc(I);f2(k,a,c,U,p,c.l.get(p)||"",3);return!1}finally{t.preventDefault(),t.stopImmediatePropagation()}});e.insertBefore(l,f);!T2(l, 
c)&&h&&U2(h,l.getBoundingClientRect().bottom+window.scrollY)}c.K.yc||b.clear()}function N2(a,b){return["BR","IMG","TABLE"].includes(b.tagName)||a.getComputedStyle(b).display==="block"}function O2(a,b,c){if(!b.K.je)return!1;a=oe(a.getComputedStyle(c).fontSize);return a!==null&&a>b.K.je}function P2(a,b,c){b?(a=c+a.scrollY,b=(b.g===void 0||a-b.g>b.j)&&(b.i===void 0||a-b.i>b.Lb)):b=!0;return b}class V2{constructor(a){this.K=a;this.i=this.g=null}get j(){return this.g}} 
function S2(a,b,c){const d=new V2(b.K);W2(b,e=>{for(const l of e)if(l.isIntersecting){if(!T2(a,b)){var f=d,g=b.U;e=c;if(!f.K.md||!f.g){var h=g.pf,k=new dp;e=di(k,1,Wg(e),"0");f.g=h.call(g,e)}}}else!d.g||d.K.md&&d.i||(e=b.U,f=e.nf,g=new cp,g=Pi(g,1,d.g),d.i=f.call(e,g),d.K.md||(d.g=null))}).observe(a);return d} 
function T2(a,b){return b.K.rg&&(b=document.elementFromPoint(a.getBoundingClientRect().x+a.getBoundingClientRect().width/2,a.getBoundingClientRect().y+a.getBoundingClientRect().height/2))&&!a.contains(b)?(a.remove(),!0):!1}function R2(a,b){a.i=b}function U2(a,b){a.g=b}class M2{constructor(a,b){this.j=a;this.Lb=b;this.i=this.g=void 0}};function X2(a,b,c,d,e,f,g){if(!a.g){var h=b.document.createElement("span");h.appendChild(W1(b,"12px",f));h.appendChild(b.document.createTextNode(d));HH(b,c||null,{informationText:h},e,g?k=>{g.Df(k)}:g);a.g=!0}}var Y2=class{constructor(){this.g=!1}};const Z2=[{yg:"1907259590",we:480,Ue:220},{yg:"2837189651",we:400,Ue:180},{yg:"9211025045",we:360,Ue:160},{yg:"6584860439",we:-Infinity,Ue:150}];function $2(a){Z2.find(b=>b.we<=a)};var a3=class{constructor(){this.g=[]}};function b3(a){m2(a.config,1065,a.B,()=>{if(!a.g){var b=new Mp;b=Pi(b,1,a.i);var c=new Lp;b=oi(b,2,Np,c);a.config.U.De(b)}},1E4)}class c3{constructor(a,b,c){this.B=a;this.config=b;this.i=c;this.g=!1}cancel(a){this.B.clearTimeout(a)}} 
function f2(a,b,c,d,e,f,g,h=null){$2(b.document.body.clientWidth);e=c.qa?d3(a,b,c,e,f,h,g):e3(a,b,c,e,f,h,g);rs(e.isVisible(),!1,()=>{a.g=!1;var l=a.i;for(const m of l.g)m();l.g.length=0});e.show({hh:!0});a.g=!0;const k=new c3(b,c,d);b3(k);a.Ta(()=>{var l=c.U,m=l.De;var n=new Mp;n=Pi(n,1,d);var p=new Kp;n=oi(n,3,Np,p);m.call(l,n);k.g=!0})} 
function d3(a,b,c,d,e,f,g){e=c.Od.kf({B:b,sa:d,wg:e,K:c.K,qa:c.qa,aa:c.aa(),R:c.R,lh:f,Pd:c.qa?b.innerWidth:Math.min(b.document.body.clientWidth,c.K.qe),Bb:c.Bb.bind(c),Tb:c.Tb.bind(c),Ta:h=>void a.Ta(h),format:g});return PF(b,e,{Wh:.95,sh:.95,zIndex:2147483647,Tc:!0,yf:"adpub-drawer-root",xf:!1,bb:!0,Ef:new R(q1(c.R,d))})} 
function e3(a,b,c,d,e,f,g){const h=c.qa?b.innerWidth:Math.min(b.document.body.clientWidth,c.K.qe);e=c.Od.kf({B:b,sa:d,wg:e,K:c.K,qa:c.qa,aa:c.aa(),R:c.R,lh:f,Pd:h,Bb:c.Bb.bind(c),Tb:c.Tb.bind(c),Ta:k=>void a.Ta(k),format:g});return YE(b,e,{Vd:`${h}px`,Sd:c.aa(),Ld:E(c.R,14),zIndex:2147483647,Tc:!0,jh:!0,yf:"adpub-drawer-root",xf:!1,bb:!0,Ef:new R(q1(c.R,d))})}var f3=class{constructor(){this.g=!1;this.i=new a3}Ta(a){this.i.g.push(a)}};const g3=["block","inline","inline-block","list-item","table-cell"];async function h3(a,b,c,d,e){d.g.ka(5)>=d.i&&q(await q(C1(d,6)));const f=new f3;c.K.Af||i3(a,b,c,e,k1(c.R),f);c.K.Bf&&!j3(a)||q(await q(c.Sa(898,K2(a,c,d,e,b,f))));c.K.Cf||q(await q(k3(a,c,()=>new Y2,d,e,f)))}function j3(a){try{const b=a.location?.href?.match(/goog_fac=1/);return b!==null&&b!==void 0}catch(b){return!1}} 
async function k3(a,b,c,d,e,f){var g=k1(b.R);var h=new D2(b.i);for(const k of g)E(k,6)!==""&&(g=E(k,1),E2(h,g,g));F2(h);h=new G2(h);h.isEmpty()||q(await q(b.Sa(898,l3(a,b,d,e,h,new r2(b.params.Bl,b.params.xg,b.params.gf,b.params.ne),c(),f))))} 
async function l3(a,b,c,d,e,f,g,h){let k=a.document.body;if(C(b.R,17)||z(b.R,ou,21))for(;k;){c.g.ka(7)>=c.i&&q(await q(C1(c,8)));if(k.nodeType===Node.TEXT_NODE&&k.textContent!==""&&k.parentElement){const mc=k.parentElement;a:{var l=a,m=b,n=mc,p=k.textContent,w=d,u=e,t=f,B=h;const nc=[];b:{var I=p;switch(m.i){case 1:var U=I;const Hc=Array(U.length);let D=0;for(let Ga=0;Ga<U.length;Ga++)A2.test(U[Ga])||D++,Hc[Ga]=D;var M=Hc;break b;default:var P=I;const da=Array(P.length);let qa=0,ya=0;for(;ya<P.length;){for(;/\s/.test(P[ya]);)da[ya]= 
qa,ya++;let Ga=!1;for(;ya<P.length&&!/\s/.test(P[ya]);)Ga=!0,da[ya]=qa,ya++;Ga&&(qa++,da[ya-1]=qa)}M=da}}const Kb=M,Fi=p.includes("\u00bb")?[]:H2(u,p);let Od=-1;for(const Hc of Fi){const D=Hc.j,da=Hc.A;if(D<Od)continue;var V=t,Pa=Hc.sa;s2(V.i,V.g+Kb[D]);var Ra=V,la=Ra.i,Ca=Pa;if(!((la.g.has(Ca)?la.g.get(Ca).length:0)<Ra.xg&&V.i.j<V.gf))continue;const qa=l.getComputedStyle(n),ya=qa.fontSize.match(/\d+/);if(!(ya&&Number(ya[0])>=12&&Number(ya[0])<=22&&Za(g3,qa.display))){t.g+=Kb[Kb.length-1];var ic= 
[];break a}const Ga=Od+1;Ga<D&&nc.push(l.document.createTextNode(p.substring(Ga,D)));const Sb=p.substring(D,da+1);var Ib=p,zb=D,Aa=da+1;const If=Ib.substring(Math.max(zb-30,0),zb)+"~~"+Ib.substring(Aa,Math.min(Aa+30,Ib.length));var ma=l,jc=m.U.Ed(),od=n,je=Sb,Ng=If,Ld=Hc.sa,Oe=Kb[D];const me=od.getBoundingClientRect();var zf=xp(wp(new yp,jc),2);var Af=Ri(zf,2,je);var Jb=Ri(Af,3,Ng);var Pe=Ri(Jb,4,Ld);var pd=Oi(Pe,5,Oe);var ke=Oi(pd,6,Math.round(me.x));var Bf=Oi(ke,7,Math.round(me.y));const Ic=ma.getComputedStyle(od); 
var Cf=new vp;var Md=Ri(Cf,1,Ic.fontFamily);var Qe=z2(Ic.color);var Df=A(Md,7,Qe);var Og=z2(Ic.backgroundColor);var Ef=A(Df,8,Og);const Jf=Ic.fontSize.match(/^(\d+(\.\d+)?)px$/);var db=Oi(Ef,4,Jf?Math.round(Number(Jf[1])):0);const Te=Math.round(Number(Ic.fontWeight));isNaN(Te)||Te===400||Oi(db,5,Te);Ic.textDecorationLine!=="none"&&Ri(db,6,Ic.textDecorationLine);var qd=A(Bf,8,db);const ne=[];let Pd=od;for(;Pd&&ne.length<20;){var Ff=ne,Re=Ff.push,Nd=Pd,Gf=new tp;const Qg=Ri(Gf,1,Nd.tagName);Nd.className!== 
""&&ci(Qg,2,Nd.className.split(" "),$g);Re.call(Ff,Qg);if(Pd.tagName==="BODY")break;Pd=Pd.parentElement}var Fa=ne.reverse();const Kf=pi(qd,9,Fa);w.entries.push(Fh(Kf));nc.push(m3(l,m,Xg(y(Kf,10))??"0",Hc.sa,Sb,n,B));t2(t.i,Hc.sa,t.g+Kb[D]);Od=da;if(p2(t))break}const Hf=Od+1;Hf!==0&&Hf<p.length&&nc.push(l.document.createTextNode(p.substring(Hf)));t.g+=Kb[Kb.length-1];ic=nc}const Tc=ic;if(Tc.length&&!C(b.R,17)){!b.K.wd&&X2(g,a,b.params.gh?B1(a,b.params.gh):void 0,E(b.R,3),Vi(z(b.R,ou,21)),b.params.Jh, 
b.U);for(const nc of Tc)mc.insertBefore(nc,k),n3(nc);mc.removeChild(k);for(k=Tc[Tc.length-1];k.lastChild;)k=k.lastChild;if(p2(f))break}}a:{var Ei=a,kc=k,Pg=f,rd=b.i;if(kc.firstChild&&(kc.nodeType!==Node.ELEMENT_NODE?0:!kc.classList?.contains("google-anno-skip")&&(kc.offsetHeight||Ei.getComputedStyle(kc).display==="contents"))){if(w2(kc)){k=kc.firstChild;break a}if(kc.textContent?.length){var le=Pg;b:{var sd=kc.textContent;switch(rd){case 1:var Se=sd;let Tc=0;for(let Kb=Se.length-1;Kb>=0;Kb--)A2.test(Se[Kb])|| 
Tc++;var lc=Tc;break b;default:const nc=sd.trim();lc=nc===""?0:nc.split(/\s+/).length}}s2(le.i,le.g+lc)}}let mc=kc;for(;;){if(mc.nextSibling){k=mc.nextSibling;break a}if(!mc.parentNode){k=null;break a}mc=mc.parentNode}k=void 0}}} 
function o3(a,b){b={aa:b.aa(),qa:b.qa,T:Br(a),W:Cr(a)};if(b.W>=400){var c;if((c=K1(a,b))!=null)var d=c;else a:{c=b.T;var e=S1(a,b);a=16;for(d of e){e=d.start;const f=d.end;if(e>a){if(e-a-16>=200){d=Q1(b,e,a);break a}a=f+16}else f>=a&&(a=f+16)}d=c-a-16>=200?Q1(b,c,a):null}}else d=null;return d} 
function i3(a,b,c,d,e,f){function g(){return k??(k=c.Tb(898,a,()=>{if(!h){var m=c.ka(12);a.clearInterval(k);h=!0;var n=o3(a,c);n&&p3(a,b,c,d,m,e,n,f)}},c.K.Ye))}if(e.filter(m=>E(m,7).length).length){var h=!1,k=void 0,l=q3(c,a,()=>{if(!(a.scrollY<=c.K.Ze||h)){var m=c.ka(12),n=o3(a,c);n?(h=!0,a.removeEventListener("scroll",l),p3(a,b,c,d,m,e,n,f)):k=g()}});m2(c,898,a,()=>{if(!h){var m=c.ka(12),n=o3(a,c);n?(h=!0,p3(a,b,c,d,m,e,n,f)):k=g()}},c.K.Xe)}} 
function p3(a,b,c,d,e,f,g,h){const k=new n2(a,c,g,h);f.filter(l=>E(l,7).length).forEach(l=>{var m=c.U.Ed();var n=E(l,1);m=xp(wp(new yp,m),1);n=Ri(m,4,n);d.entries.push(Fh(n));n=Xg(y(n,10))??"0";m=E(l,1);l=E(l,1);k.j.push(new o2(n,m,l));k.A&&l2(k,b)});c.U.Zf(r3(d,c.ka(13)-e))} 
function n3(a){if(a.nodeType===Node.ELEMENT_NODE){if(a.tagName==="A"){var b=J1(H1(getComputedStyle(a.parentElement).color)),c=J1(H1(getComputedStyle(a).color));var d=I1(a);if(d=b&&c&&d?CQ(c,d)<Math.min(CQ(b,d),2.5)?b:null:b){b=d[0];c=d[1];d=d[2];b=Number(b);c=Number(c);d=Number(d);if(b!=(b&255)||c!=(c&255)||d!=(d&255))throw Error('"('+b+","+c+","+d+'") is not a valid RGB color');c=b<<16|c<<8|d;b=b<16?"#"+(16777216|c).toString(16).slice(1):"#"+c.toString(16);v(a,{color:b})}}for(b=0;b<a.childElementCount;b++)n3(a.children[b])}} 
class s3{constructor(a){this.K=a;this.i=this.g=null}get j(){return this.g}} 
function m3(a,b,c,d,e,f,g){const h=a.document.createElement("SPAN");h.className="google-anno-t";T1(h);v(h,{"text-decoration":"underline"});v(h,{"text-decoration-style":"dotted"});v(h,{"-webkit-text-decoration-line":"underline","-webkit-text-decoration-style":"dotted"});v(h,{color:"inherit","font-family":"inherit","font-size":"inherit","font-style":"inherit","font-weight":"inherit"});h.appendChild(a.document.createTextNode(e));e=a.document.createElement("A");T1(e);v(e,{"text-decoration":"none",fill:"currentColor"}); 
$c(e);e.className="google-anno";V1(e);e.appendChild(t3(a,b,f));e.appendChild(a.document.createTextNode("\u00a0"));e.appendChild(h);const k=u3(b,c,e);c2(b,999,e,l=>{try{var m=Fp(Dp(d),c);var n=Mh(m,2,Hg(k.j));const p=b.U.Jc(n);f2(g,a,b,p,d,b.C.get(d)||"",1,b.K.wd?b.j.get(d)||"":null);return!1}finally{l.preventDefault(),l.stopImmediatePropagation()}});return e}function t3(a,b,c){return W1(a,a.getComputedStyle(c).fontSize,b.params.Jh)} 
function u3(a,b,c){const d=new s3(a.K);W2(a,e=>{for(const l of e)if(l.isIntersecting){var f=d,g=a.U;e=b;if(!f.K.nd||!f.g){var h=g.cg,k=new Jp;e=di(k,2,Wg(e),"0");f.g=h.call(g,e)}}else!d.g||d.K.nd&&d.i||(e=a.U,f=e.bg,g=new Ip,g=Pi(g,1,d.g),d.i=f.call(e,g),d.K.nd||(d.g=null))}).observe(c);return d};function r3(a,b){const c=a.g;a.g=a.entries.length;var d=new Hp,e=new zp;a=pi(e,2,a.entries.slice(c));d=A(d,1,a);b!==0&&Pi(d,2,Math.round(b));return d}var v3=class{constructor(){this.entries=[];this.language=null;this.g=0}};function w3(a){return a?1:0};function x3(a,b,c){y3(a);var d=new Map;for(const e of b)b=z3(e),d.set(b,(d.get(b)??0)+1);for(const [e,f]of d)d=e,A3(a,f,d,c),B3(a,d)}function C3(a,b,c,d){a.i.forEach(e=>{D3(e,{...a.g,outcome:b,cd:c,Kf:d})})}function E3(a,b,c,d,e){a.i.forEach(f=>{f.df(b,{...a.g,outcome:c,cd:d,Kf:e})})}function y3(a){a.A||(a.A=!0,a.i.forEach(b=>{F3(b,a.g)}))}function A3(a,b,c,d){a.i.forEach(e=>{e.ff(b,{...a.g,format:c,cd:d})})}function B3(a,b){a.C.has(b)||(a.C.add(b),a.i.forEach(c=>{G3(c,{...a.g,format:b})}))} 
function H3(a,b){a.i.forEach(c=>{I3(c,{...a.g,reason:J3(b)})})} 
var R3=class{constructor(a,b,c){this.H=this.j=1;this.l=this.A=!1;this.g={language:a.eg.has(b)?b:"other",ya:bc()?2:$b()?4:ac()?7:10};this.C=new Set;this.i=[...c]}Ed(){return this.H++}ef(a){a:switch(hi(a,Bp)){case 4:var b=1;break a;case 5:b=2;break a;default:b=0}const c=K3(a);var d=Uu(vi(a,3)),e=c.length>0;C3(this,b,!1,e);E3(this,d,b,!1,e);a.g()&&c.length>0&&x3(this,c,!1);if(Qh(a,sp,5,Bp)){a=Ai(a,sp,5,Bp);for(const f of mi(a,np,1,Th()))H3(this,f)}this.j++}Zf(a){const b=a.g()?1:0,c=K3(a);var d=Uu(vi(a, 
2)),e=c.length>0;C3(this,b,!0,e);E3(this,d,b,!0,e);a.g()&&c.length>0&&x3(this,c,!0);this.j++}cg(){this.i.forEach(a=>{L3(a,{...this.g,format:2})});return this.j++}bg(){this.i.forEach(a=>{M3(a,{...this.g,format:2})});return this.j++}bf(){this.i.forEach(a=>{L3(a,{...this.g,format:1})});return this.j++}af(){this.i.forEach(a=>{M3(a,{...this.g,format:1})});this.j++}pf(){this.i.forEach(a=>{L3(a,{...this.g,format:3})});return this.j++}nf(){this.i.forEach(a=>{M3(a,{...this.g,format:3})});return this.j++}Jc(a){let b= 
0;Tg(y(a,2))!=null?b=2:Tg(y(a,3))!=null?b=1:Tg(y(a,7))!=null&&(b=3);this.i.forEach(c=>{c.click({...this.g,format:b})});return this.j++}De(a){let b=0;Qh(a,Lp,2,Np)?b=1:Qh(a,Kp,3,Np)&&(b=2);this.i.forEach(c=>{N3(c,{...this.g,type:b})});this.j++}Df(a){a:switch(F(a,1)){case 1:a=1;break a;case 2:a=2;break a;default:a=0}const b=a;this.i.forEach(c=>{O3(c,{...this.g,type:b})});this.l||(this.l=!0,this.i.forEach(c=>{P3(c,this.g)}));this.j++}We(){this.i.forEach(a=>{Q3(a,this.g)});this.j++}}; 
function K3(a){a.g()?(a=a.i(),a=[...mi(a,yp,2,Th())]):a=[];return a}function J3(a){switch(hi(a,op)){case 1:return 1;case 2:return 2;case 3:return 3;case 9:return 4;case 11:return 5;case 12:return 6;case 13:return 7;default:return 0}}function z3(a){switch(F(a,1)){case 1:return 1;case 2:return 2;case 3:return 3;default:return 0}};function S3(a,b){var c=new Op;var d=a.j++;c=Pi(c,1,d);b=Pi(c,2,Math.round(a.A.ka(b)-a.C));b=A(b,10,a.H);return Li(b,15,a.D?!0:void 0)} 
var T3=class{constructor(a,b,c,d,e,f,g,h){this.A=b;this.C=c;this.H=d;this.D=f;this.i=this.j=1;this.l=[...g];this.g=h.length?new R3(e,a,h):null}Ed(){return this.i++}ef(a){this.g?.ef(a);var b=this.handle,c=S3(this,11);a=oi(c,3,Pp,a);b.call(this,a)}Zf(a){this.g?.Zf(a);var b=this.handle,c=S3(this,11);a=oi(c,14,Pp,a);b.call(this,a)}cg(a){this.g?.cg(a);var b=this.handle,c=S3(this,15);a=oi(c,4,Pp,a);return b.call(this,a)}bg(a){this.g?.bg(a);var b=this.handle,c=S3(this,16);a=oi(c,5,Pp,a);return b.call(this, 
a)}bf(a){this.g?.bf(a);var b=this.handle,c=S3(this,17);a=oi(c,6,Pp,a);return b.call(this,a)}af(a){this.g?.af(a);var b=this.handle,c=S3(this,18);a=oi(c,7,Pp,a);b.call(this,a)}pf(a){this.g?.pf(a);var b=this.handle,c=S3(this,19);a=oi(c,16,Pp,a);return b.call(this,a)}nf(a){this.g?.nf(a);var b=this.handle,c=S3(this,20);a=oi(c,17,Pp,a);return b.call(this,a)}Jc(a){this.g?.Jc(a);var b=this.handle,c=S3(this,14);a=oi(c,8,Pp,a);return b.call(this,a)}De(a){this.g?.De(a);var b=this.handle,c=S3(this,21);a=oi(c, 
9,Pp,a);b.call(this,a)}Df(a){this.g?.Df(a);var b=this.handle,c=S3(this,22);a=oi(c,11,Pp,a);b.call(this,a)}We(a){this.g?.We(a);var b=this.handle,c=S3(this,24);a=oi(c,12,Pp,a);b.call(this,a)}handle(a){for(const b of this.l)b(a);return Uu(vi(a,1))}};function c2(a,b,c,d){c.addEventListener("click",U3(a,b,d))}function m2(a,b,c,d,e){c.setTimeout(U3(a,b,d),e)}function W2(a,b){return new IntersectionObserver(U3(a,1065,b),{threshold:.98})}function q3(a,b,c){a=U3(a,898,c);b.addEventListener("scroll",a,{passive:!0});return a}function U3(a,b,c){return a.Aa.tb(b,c,void 0,d=>{d.es=a.K.Kb.join(",")})} 
var W3=class{constructor(a,b,c,d,e,f,g,h){this.qa=a;this.R=b;this.Aa=c;this.U=d;this.H=e;this.params=f;this.K=g;this.Od=h;this.C=new Map;this.A=new Map;this.l=new Map;this.g=new Map;this.j=new Map;this.i=Za(V3,E(b,7))?1:0;for(const k of k1(this.R))ti(k,6)!=null&&this.C.set(E(k,1),E(k,6)),ti(k,7)!=null&&this.A.set(E(k,1),E(k,7)),ti(k,5)!=null&&this.l.set(E(k,1),E(k,5)),wg(y(k,10))!=null&&this.g.set(E(k,1),F(k,10)),ti(k,11)!=null&&this.j.set(E(k,1),E(k,11))}Bb(a,b,c){a=U3(this,a,c);b.addEventListener("message", 
a);return a}Tb(a,b,c,d){return b.setInterval(U3(this,a,c),d)}Sa(a,b){this.Aa.Sa(a,b,c=>{c.es=this.K.Kb.join(",")});return b}ka(a){return this.H.ka(a)}aa(){return F(this.R,12)===2}};const V3=["ja","zh_CN","zh_TW"];const X3=new Map([[1,1],[2,2]]); 
async function Y3(a,b,c,d,e,f,g,h){var k=eC;h=a?(h=HR(new LR(a),"__gads",h))?de(h+"t2Z7mVic")%20:null:null;var l=h??Math.floor(ae()*20),m=f.ka(0),n=!!a&&Br(a)<488;h=c.R;var p;p=(p=E(h,7))?(p=p.match(/^[a-z]{2,3}/i))?p[0].toLowerCase():"":"";var w=c.K,u=new Gp;l=Oi(u,2,l);l=qi(l,3,Bg,w.Kb,Tg,void 0,void 0,!0);d=new T3(p,f,m,l,w,C(h,17),d,e);e=new W3(n,h,k,d,f,c.params,c.K,c.Od);k=new v3;k.language=p;g=q(await q(Z3(a,b,e,g,k)));b=d.ef;e=c.ic;a=a?.location?.hostname||"";c=c.Uj;f=f.ka(11)-m;m=new Cp; 
p=new hp;e=Ri(p,1,e);a=Ri(e,2,a);n=Mi(a,3,n);n=A(m,1,n);a=new ip;a=Ri(a,2,k.language);c=Ri(a,3,c);n=A(n,2,c);n=Pi(n,3,Math.round(f));e=k1(h);h=f=c=a=m=0;for(t of e)m+=w3(E(t,6)!=="")+w3(E(t,7)!=="")+w3(E(t,5)!==""),a+=w3(E(t,6)!=="")+w3(E(t,7)!=="")+w3(E(t,5)!==""),c+=w3(E(t,6)!==""),f+=w3(E(t,7)!==""),h+=w3(E(t,5)!=="");var t=new Ap;t=Ni(t,1,e.length);t=Ni(t,2,m);t=Mh(t,3,a==null?a:zg(a));t=Mh(t,4,c==null?c:zg(c));t=Mh(t,5,f==null?f:zg(f));t=Ni(t,6,h);t=A(n,6,t);if(g.length){var B=new sp;B=pi(B, 
1,g);oi(t,5,Bp,B)}else{k.g=k.entries.length;h=new zp;f=k.entries;Ih(h);k=h.P;k=li(h,k,k[x]|0,yp,2,2,void 0,!0);n=g=0;if(Array.isArray(f))for(B=f.length,c=0;c<B;c++)a=f[c],k.push(a),(a=Rf(a))&&!g++&&(k[x]&=-9),a||n++||yf(k,8192);else for(B of f)f=B,k.push(f),(f=Rf(f))&&!g++&&(k[x]&=-9),f||n++||yf(k,8192);oi(t,4,Bp,h)}b.call(d,t)} 
async function Z3(a,b,c,d,e){if(!a)return[pp()];var f=a.document.body;if(!f||!$3(f))return[mp()];d.g.ka(3)>=d.i&&q(await q(C1(d,4)));f=[];(c.K.ve&&Br(a)<c.K.ve||c.K.ue&&Cr(a)<c.K.ue)&&f.push(mp());if(zi(c.R,1).length){const g=zi(c.R,1).map(h=>X3.get(h)??0);f.push(rp(new np,jp(g)))}fe()&&f.push(qp());f.length||q(await q(h3(a,b,c,d,e)));return f}function $3(a){try{(new ResizeObserver(()=>{})).disconnect()}catch{return!1}return a.classList&&a.classList.contains!==void 0&&a.attachShadow!==void 0};async function a4(a,b,c,d,e,f,g){const h=a.performance?.now?new E1(a.performance):new F1,k=new D1(a,h);if(typeof e!=="string")throw Error(`Invalid config string ${e}`);e=p1(e);var l=ki(e,l1,1),m=c.google_ad_client;if(typeof m!=="string")throw new bC(`Invalid property code ${m}`);if(!O(Lw)||m===E(e,5)){c=c.google_page_url;c=typeof c==="string"?c:"";if(F(e,4)===2){var n=pI(a)?.head_tag_slot_vars?.google_ad_host??a.document?.querySelector('meta[name="google-adsense-platform-account"]')?.getAttribute("content")?? 
null;a=new A1(m,n,Ge(a),c)}else a=new u1(m);m=a;a=N(EI);l=b4(l);a:{try{n=b?.location?.hash?.match(/\bgoog_cpmi=([^&]*)/);if(!n){var p=null;break a}var w=decodeURIComponent(n[1]);p=m1(w);break a}catch(u){p=null;break a}p=void 0}p=p||ki(e,l1,1);w=F(e,4);n=e.P;n=li(e,n,n[x]|0,vu,3,1);w={Bl:W(Tw),xg:W(cx),gf:W(ax),ne:W(bx),gh:n,Hg:W(gx),Jh:w===2?10:2};g={R:p,ic:c,Uj:g,params:w,K:new r1({Kb:l,Af:O(rw),Cf:O(tw),ve:W(ex),ue:W(dx),Ye:W(Rw),Xe:W(Qw),Ze:W(Sw),me:W(Xw),Bf:O(sw),qe:W(zw),wd:F(e,4)===2,Jf:Nx(xw), 
Ge:Mx(vw),He:O(ww),Uf:O(yw),md:O(Dw),nd:O(Ew),pe:W(Yw),sg:O(Kw),je:W(Aw),Zh:O(Hw),te:W(fx),yc:W($w),eg:N(Lx).g(Bw.g,Bw.defaultValue),rg:O(Iw),Lb:W(Cw)}),Od:m};q(await q(c4(b,d,a,g,h,k,f)))}}function b4(a){const b=N(kr).g();a&&b.push(...xi(a,24));b.push(...Nx(Mw).map(Number));b.push(42);b.sort((c,d)=>c-d);return b} 
async function c4(a,b,c,d,e,f,g){if(a){const h=kE(a);if(h.wasReactiveAdConfigReceived[42])return;h.wasReactiveAdConfigReceived[42]=!0}q(await q(Y3(a,b,d,[h=>{eC.Sa(1214,II(c,h,e.ka(23)),k=>{k.es=b4(d.R)})}],[new d4(c,d.R)],e,f,g)))}function F3(a,b){e4(a,c=>c.Mi,{ea:1,...b})}function G3(a,b){e4(a,c=>c.Oj,{ea:1,...b})}function D3(a,b){e4(a,c=>c.Ni,{ea:1,...b})}function I3(a,b){e4(a,c=>c.Oi,{ea:1,...b})}function L3(a,b){e4(a,c=>c.Qi,{ea:1,...b})}function M3(a,b){e4(a,c=>c.Pi,{ea:1,...b})} 
function N3(a,b){e4(a,c=>c.Qk,{ea:1,...b})}function O3(a,b){e4(a,c=>c.wj,{ea:1,...b})}function P3(a,b){e4(a,c=>c.vj,{ea:1,...b})}function Q3(a,b){e4(a,c=>c.Li,{ea:1,...b})}function e4(a,b,c){a.g&&a.Aa.Sa(1214,JI(a.g,b,c),d=>{d.es=b4(a.i)})}function f4(a,b,c){a.g&&a.Aa.Sa(1214,KI(a.g,b,c),d=>{d.es=b4(a.i)})}class d4{constructor(a,b){var c=eC;this.g=a;this.Aa=c;this.i=b}df(a,b){f4(this,c=>c.df,{ud:a,...b})}ff(a,b){e4(this,c=>c.ff,{ea:a,...b})}click(a){e4(this,b=>b.ij,{ea:1,...a})}};function g4(a,b){const c=Zd("STYLE",a);c.textContent=ld(Ad`* { pointer-events: none; }`);a?.head.appendChild(c);setTimeout(()=>{a?.head.removeChild(c)},b)}function h4(a,b,c){if(!a.body)return null;const d=new i4;d.apply(a,b);return()=>{var e=c||0;e>0&&g4(b.document,e);tm(a.body,{filter:d.g,webkitFilter:d.g,overflow:d.j,position:d.A,top:d.l});b.scrollTo(0,d.i)}} 
class i4{constructor(){this.g=this.l=this.A=this.j=null;this.i=0}apply(a,b){this.j=a.body.style.overflow;this.A=a.body.style.position;this.l=a.body.style.top;this.g=a.body.style.filter?a.body.style.filter:a.body.style.webkitFilter;this.i=Lr(b);tm(a.body,"top",`${-this.i}px`)}};function j4(a,b){var c;if(!a.j)for(a.j=[],c=a.i.parentElement;c;){a.j.push(c);if(a.J(c))break;c=c.parentNode&&c.parentNode.nodeType===1?c.parentNode:null}c=a.j.slice();let d,e;for(d=0;d<c.length;++d)(e=c[d])&&b.call(a,e,d,c)}var k4=class extends Q{constructor(a,b,c){super();this.i=a;this.V=b;this.D=c;this.j=null;fs(this,()=>this.j=null)}J(a){return this.D===a}};function l4(a,b){const c=a.D;c&&(b?(tE(a.G),v(c,{display:"block"}),a.C.body&&!a.l&&(a.l=h4(a.C,a.V,a.X)),c.setAttribute("tabindex","0"),c.setAttribute("aria-hidden","false"),a.C.body.setAttribute("aria-hidden","true")):(uE(a.G),v(c,{display:"none"}),a.l&&(a.l(),a.l=null),a.C.body.setAttribute("aria-hidden","false"),c.setAttribute("aria-hidden","true")))} 
function m4(a){l4(a,!1);const b=a.D;if(b){var c=n4(a.M);j4(a,d=>{v(d,c);Pr(d)});a.i.setAttribute("width","");a.i.setAttribute("height","");tm(a.i,c);tm(a.i,o4);tm(b,p4);tm(b,{background:"transparent"});v(b,{display:"none",position:"fixed"});Pr(b);Pr(a.i);(bc()&&wc()?Ke(a.M):1)<=1||(tm(b,{overflow:"scroll","max-width":"100vw"}),ye(b))}} 
var q4=class extends k4{constructor(a,b,c){var d=W(zx);super(a,b,c);this.M=b;this.X=d;this.l=null;this.C=b.document;this.G=nE(new sE(b),2147483646)}},p4={backgroundColor:"white",opacity:"1",position:"fixed",left:"0px",top:"0px",margin:"0px",padding:"0px",display:"none",zIndex:"2147483647"},o4={left:"0",position:"absolute",top:"0"};function n4(a){a=bc()&&wc()?Ke(a):1;a=100*(a<1?1:a);return{width:`${a}vw`,height:`${a}vh`}};var r4=class extends q4{constructor(a,b,c){super(b,a,c);m4(this)}J(a){return a.classList?a.classList.contains("adsbygoogle"):Za(a.classList?a.classList:(typeof a.className=="string"?a.className:a.getAttribute&&a.getAttribute("class")||"").match(/\S+/g)||[],"adsbygoogle")}};const s4={[1]:"closed",[2]:"viewed",[3]:"dismissed"};async function t4(a,b,c,d,e){a=new u4(a,b,c,d,e);q(await q(a.init()));return a}function v4(a){return setTimeout(iC(728,()=>{w4(()=>{a.C.reject()});a.dispose()}),W(sx)*1E3)}function x4(a,b){var c=JQ(a.i).then(()=>{clearTimeout(b);a.C.resolve()});jC(1005,c);c=KQ(a.i).then(d=>{y4(a,s4[d.status],d.payload)});jC(1006,c);c=LQ(a.i).then(()=>{y4(a,"error")});jC(1004,c)} 
function z4(a){if(O(tx)){a.B.location.hash!==""&&kC("pub_hash",{o_url:a.B.location.href},.1);a.B.location.hash="goog_fullscreen_ad";var b=iC(950,c=>{c.oldURL.endsWith("#goog_fullscreen_ad")&&(a.j===10?(y4(a,"closed"),a.B.removeEventListener("hashchange",b)):(a.B.location.hash="goog_fullscreen_ad",FQ(a.i.sf,"fullscreen",{eventType:"backButton"},"*")))});a.B.addEventListener("hashchange",b);fs(a,()=>{a.B.removeEventListener("hashchange",b);a.B.location.hash==="#goog_fullscreen_ad"&&a.B.history.back()})}} 
function w4(a){try{a()}catch(b){}}function y4(a,b,c){l4(a.G,!1);a.l&&(c&&b==="viewed"?w4(()=>{a.l({status:b,reward:c})}):w4(()=>{a.l({status:b})}));a.j===11&&kC("fs_ad",{tgorigin:a.F.google_tag_origin,client:a.F.google_ad_client,url:a.F.google_page_url??"",slot:a.F.google_ad_slot??"0",ratype:a.j,clostat:b},1);a.dispose()} 
var u4=class extends Q{constructor(a,b,c,d,e){super();this.B=a;this.D=b;this.J=c;this.j=d;this.F=e;this.l=null;this.G=new r4(a,c,b);a=new NQ(this.j===10?1:2,this.B,this.J.contentWindow);a.init();this.i=a;this.C=new IQ;this.D.dataset["slotcar"+(this.j===10?"Interstitial":"Rewarded")]="true"}async init(){const a=v4(this);x4(this,a);fs(this,()=>{this.i.dispose();clearTimeout(a);km(this.D)});q(await this.C.promise)}show(a){this.A||(this.l=a,l4(this.G,!0),r.IntersectionObserver||FQ(this.i.sf,"fullscreen", 
{eventType:"visible"},"*"),z4(this))}disposeAd(){this.dispose()}};function A4(a,b,c,d){const e=new IQ;let f="";const g=k=>{try{const l=typeof k.data==="object"?k.data:JSON.parse(k.data);f===l.paw_id&&(Mk(a,"message",g),l.error?e.reject(Error(l.error)):e.resolve(d(l)))}catch(l){}};var h=typeof a.gmaSdk?.getQueryInfo==="function"?a.gmaSdk:void 0;if(h)return Lk(a,"message",g),f=c(h),e.promise;c=typeof a.webkit?.messageHandlers?.getGmaQueryInfo?.postMessage==="function"||typeof a.webkit?.messageHandlers?.getGmaSig?.postMessage==="function"?a.webkit.messageHandlers: 
void 0;return c?(f=String(Math.floor(ae()*2147483647)),Lk(a,"message",g),b(c,f),e.promise):null}function B4(a){return A4(a,(b,c)=>void(b.getGmaQueryInfo??b.getGmaSig)?.postMessage(c),b=>b.getQueryInfo(),b=>b.signal)}(function(a){return rb(b=>{if(!vb(b))return!1;for(const [c,d]of Object.entries(a)){const e=c,f=d;if(!(e in b)){if(f.lk===!0)continue;return!1}if(!f(b[e]))return!1}return!0})})({vc:pb,pn:pb,eid:wb(),vnm:wb(),js:pb},"RawGmaSdkStaticSignalObject");const C4=(a,b)=>{try{const m=C(b,6)===void 0?!0:C(b,6);var c=cl(F(b,2)),d=E(b,3);a:switch(F(b,4)){case 1:var e="pt";break a;case 2:e="cr";break a;default:e=""}var f=new el(c,d,e),g=z(b,Wk,5)?.g()??"";f.dd=g;f.g=m;var h=!!C(b,7);f.lb=h;var k=!!C(b,8);f.i=k;f.B=a;var l=f.build();Uk(l)}catch{}};function D4(a,b){var c=C4;a.goog_sdr_l||(Object.defineProperty(a,"goog_sdr_l",{value:!0}),a.document.readyState==="complete"?c(a,b):Lk(a,"load",()=>void c(a,b)))};function E4(a){const b=RegExp("^https?://[^/#?]+/?$");return!!a&&!b.test(a)} 
function F4(a){if(a===a.top||Ud(a.top))return Promise.resolve({status:4});a:{try{var b=(a.top?.frames??{}).google_ads_top_frame;break a}catch(d){}b=null}if(!b)return Promise.resolve({status:2});if(a.parent===a.top&&E4(a.document.referrer))return Promise.resolve({status:3});const c=new IQ;a=new MessageChannel;a.port1.onmessage=d=>{d.data.msgType==="__goog_top_url_resp"&&c.resolve({Uc:d.data.topUrl,status:d.data.topUrl?0:1})};b.postMessage({msgType:"__goog_top_url_req"},"*",[a.port2]);return c.promise} 
;function G4(){return navigator.cookieDeprecationLabel?Promise.race([navigator.cookieDeprecationLabel.getValue().then(a=>({status:1,label:a})).catch(()=>({status:2})),Ie(W(qx),{status:5})]):Promise.resolve({status:3})}function H4(a){a=a.innerInsElement;if(!a)throw Error("no_wrapper_element_in_loader_provided_slot");return a} 
async function RT({Z:a,Ma:b,Ea:c,slot:d,pageState:e}){const f=d.vars,g=Xd(d.pubWin);var h=H4(d);const k=new PW({L:g,pubWin:d.pubWin,F:f,Z:a,Ma:b,Ea:c,ba:h,pageState:e});k.G=Date.now();Kl(1,[k.F]);hC(1032,()=>{if(g&&O(Ix)){var m=k.F;iI(dI(),32,!1)||(jI(dI(),32,!0),F0(g,m.google_loader_used==="sd"?5:9))}});try{q(await q(I4(k)))}catch(m){if(!lC(159,m))throw m;}hC(639,()=>{var m;var n=k.F;(m=k.L)&&n.google_responsive_auto_format===1&&n.google_full_width_responsive_allowed===!0?(n=(n=m.document.getElementById(n.google_async_iframe_id))? 
pm(n,"INS","adsbygoogle"):null)?((new OW(m,n)).run(),m=!0):m=!1:m=!1;return m});if(g?.location?.hash?.match(/\bgoog_cpmi=([^&]*)/)){b=d.pubWin;c=k.i;h=n1();try{var l=!!g?.location?.hash?.match(/\bgoog_aidd/)}catch(m){l=!1}jC(1008,J4(b,g,f,c,Ui(Si(h,4,l?2:1)),k.j,DT(e.g())||E(a,8)),m=>{m.es=b4(null)})}else EQ(k.pubWin,"affa",m=>{jC(1008,J4(d.pubWin,g,f,k.i,m.config,k.j,DT(e.g())||E(a,8)),n=>{n.es=b4(null)});return!0});K4(k);return k}async function J4(a,b,c,d,e,f,g){q(await q(a4(a,b,c,d,e,f,g)))} 
function I4(a){if(/_sdo/.test(a.F.google_ad_format))return Promise.resolve();var b=a.pubWin;MR(13,b);MR(11,b);a.H=Ai(a.Z,IT,28,vR)?.i()??!0;b=dI();var c=iI(b,23,!1);c||jI(b,23,!0);if(!c){var d=Ai(a.Z,IT,28,vR)?.g()??null,e=O(yx)?!!z(a.Z,ji,26)?.g():C(a.Z,6);b=a.pubWin;c=a.F.google_ad_client;var f=C(a.Z,20);b=new jO(b,c,d,e,f);b.i=!0;b.run()}O(iw)&&(a.pubWin.googFloatingToolbarManagerAsyncPositionUpdate=!0,a.L&&a.L!==a.pubWin&&(a.L.googFloatingToolbarManagerAsyncPositionUpdate=!0));O(kw)&&(FE(a.pubWin).dontOverrideDocumentOverflowUnlessNeeded= 
!0,a.L&&a.L!==a.pubWin&&(FE(a.L).dontOverrideDocumentOverflowUnlessNeeded=!0));b=!Yl()&&!Zb();return!b||b&&!L4(a)?M4(a):Promise.resolve()}function L4(a){return N4(a)||O4(a)} 
function N4(a){const b=a.F;if(!b.google_pause_ad_requests)return!1;const c=r.setTimeout(()=>{kC("abg:cmppar",{client:a.F.google_ad_client,url:a.F.google_page_url})},1E4),d=iC(450,()=>{b.google_pause_ad_requests=!1;r.clearTimeout(c);a.pubWin.removeEventListener("adsbygoogle-pub-unpause-ad-requests-event",d);if(!L4(a)){const e=M4(a);jC(1222,e)}});a.pubWin.addEventListener("adsbygoogle-pub-unpause-ad-requests-event",d);return!0} 
function O4(a){const b=a.pubWin.document,c=a.ba;if(VU(b)===3)return YU(iC(332,()=>{if(!P4(a,Q4().visible,c)){const g=M4(a);jC(1222,g)}}),b),!0;const d=Q4();if(d.hidden<0&&d.visible<0)return!1;const e=WU(b);if(!e)return!1;if(!XU(b))return P4(a,d.visible,c);if(DU(a.L,a.pubWin,c)<=d.hidden)return!1;let f=iC(332,()=>{if(!XU(b)&&f){Mk(b,e,f);if(!P4(a,d.visible,c)){const g=M4(a);jC(1222,g)}f=null}});return Lk(b,e,f)} 
function Q4(){var a=W(Fv);const b=W(Gv);return b===3&&a===6?(a={hidden:0,visible:3},r.IntersectionObserver||(a.visible=-1),wc()&&(a.visible*=2),a):{hidden:0,visible:r.IntersectionObserver?wc()?a:b:-1}} 
function P4(a,b,c){if(!c||b<0)return!1;var d=a.F;if(!Jr(d.google_reactive_ad_format)&&(GV(d)||d.google_reactive_ads_config)||!BU(c)||DU(a.L,a.pubWin,c)<=b)return!1;var e=dI(),f=iI(e,8,{});e=iI(e,9,{});d=d.google_ad_section||d.google_ad_region||"";const g=!!a.pubWin.google_apltlad;if(!f[d]&&!e[d]&&!g)return!1;f=new Promise(h=>{const k=new r.IntersectionObserver((l,m)=>{Na(l,n=>{n.intersectionRatio<=0||(m.unobserve(n.target),h(void 0))})},{rootMargin:`${b*100}%`});a.M=k;k.observe(c)});e=new Promise(h=> 
{c.addEventListener("adsbygoogle-close-to-visible-event",()=>{h(void 0)})});ja(Promise,"any").call(Promise,[f,e]).then(()=>{hC(294,()=>{const h=M4(a);jC(1222,h)})});return!0} 
function M4(a){hC(326,()=>{var c=a.pubWin,d=a.L,e=a.Z,f=a.pageState,g=a.Ma;if(Gm(a.F)===1){var h=O(Jx);if((h||O(Hx))&&c===d){var k=new El;d=new Fl;var l=k.setCorrelator(Ge(c));var m=NR(c);l=Ri(l,5,m);G(l,2,1);k=A(d,1,k);l=new Dl;l=Mi(l,10,!0);m=O(Cx);l=Mi(l,8,m);m=O(Dx);l=Mi(l,12,m);m=O(Gx);l=Mi(l,7,m);m=O(Fx);l=Mi(l,13,m);A(k,2,l);c.google_rum_config=th(d);e=(CT(f.g())?BT(f.g()):C(e,9))&&h?g.Sk:g.Tk;Yd(c.document,e)}else an(fC)}});a.F.google_ad_channel=R4(a,a.F.google_ad_channel);a.F.google_tag_partner= 
S4(a,a.F.google_tag_partner);PR(a.L,a.F);const b=a.F.google_start_time;typeof b==="number"&&(rr=b,a.F.google_start_time=null);TT(a);a.L&&wV(a.L,Sd(a.Ma.qj,new Map(Object.entries(RU()))));GV(a.F)&&(hR()&&(a.F.google_adtest=a.F.google_adtest||"on"),a.F.google_pgb_reactive=a.F.google_pgb_reactive||3);return T4(a)}function R4(a,b){return(b?[b]:[]).concat(pI(a.pubWin).ad_channels||[]).join("+")}function S4(a,b){return(b?[b]:[]).concat(pI(a.pubWin).tag_partners||[]).join("+")} 
function U4(a){const b=Zd("IFRAME");be(a,(c,d)=>{c!=null&&b.setAttribute(d,c)});return b}function V4(a,b,c){return a.F.google_reactive_ad_format===9&&pm(a.ba,null,"fsi_container")?(a.ba.appendChild(b),Promise.resolve(b)):DV(a.Ma.bi,525,d=>{a.ba.appendChild(b);d.createAdSlot(a.L,a.F,b,a.ba.parentElement,Ui(c),a.pubWin);return b})} 
function W4(a,b,c,d){DI();N(EI).ic=a.F.google_page_url;d=al(Zk(Yk($k(Xk(new bl,Vk(new Wk,String(Ge(a.pubWin)))))),a.pageState.g().g()||E(a.Z,2)),d.g());O(tv)&&Mi(d,7,!0);O(ux)&&Mi(d,8,!0);D4(a.pubWin,d);const e=a.L;if(a.F.google_acr)if(a.F.google_wrap_fullscreen_ad){const g=a.F.google_acr;t4(a.pubWin,a.ba.parentElement,b,a.F.google_reactive_ad_format,a.F).then(g).catch(()=>{g(null)})}else a.F.google_acr(b);Lk(b,"load",()=>{b&&b.setAttribute("data-load-complete",!0);const g=e?pI(e).enable_overlap_observer|| 
!1:!1;(a.F.ovlp||g)&&e&&e===a.pubWin&&X4(e,a,a.ba,b)});d=g=>{g&&a.i.push(()=>{g.dispose()})};const f=FU(a,b);EU(a.pubWin,a.j,b.contentWindow,a.i);!e||GV(a.F)&&!HV(a.F)||(a.F.no_resize||d(new $_(e,b,a.ba)),d(new oX(a,b)),d(e.IntersectionObserver?null:new qX(e,b,a.ba)),e.IntersectionObserver&&d(g0(e,b,a.F,a.ba,iC(1225,()=>{f();for(const g of a.i)g();a.i.length=0}))));e&&(d(iX(e,b,zT(a.pageState.g()),a.j)),a.i.push(IW(e,a.F)),N(NW).init(e,a.Z),a.i.push(aX(e,a.ba,b)));b&&b.setAttribute("data-google-container-id", 
c);c=a.F.iaaso;if(c!=null){d=a.ba;const g=d.parentElement;(g&&py.test(g.className)?g:d).setAttribute("data-auto-ad-size",c)}b.setAttribute("tabindex","0");b.setAttribute("title","Advertisement");b.setAttribute("aria-label","Advertisement");Y4(a);O(sv)&&ZS(a,b)} 
function Y4(a){const b=Yl(a.pubWin);if(b)if(b.container==="AMP-STICKY-AD"){const c=d=>{d.data==="fill_sticky"&&b.renderStart&&b.renderStart()};Lk(a.pubWin,"message",iC(616,c));a.i.push(()=>{Mk(a.pubWin,"message",c)})}else b.renderStart&&b.renderStart()} 
function X4(a,b,c,d){WR(new gS(a),c).then(e=>{Kl(8,[e]);return e}).then(e=>{const f=c.parentElement;(f&&py.test(f.className)?f:c).setAttribute("data-overlap-observer-io",String(e.Fh));return e}).then(e=>{const f=b.F.armr||"",g=e.Gj||"",h=b.F.iaaso==null?"":Number(b.F.iaaso),k=Number(e.Fh),l=Va(e.entries,B=>`${B.Ob}:${B.mg}:${B.Vh}`),m=Number(e.Ek.toFixed(2)),n=d.dataset.googleQueryId||"",p=m*e.Gc.width*e.Gc.height,w=`${e.hi.scrollX},${e.hi.scrollY}`,u=Hm(e.target),t=[e.Gc.left,e.Gc.top,e.Gc.right, 
e.Gc.bottom].join();e=`${e.wi.width}x${e.wi.height}`;kC("ovlp",{adf:b.F.google_ad_dom_fingerprint,armr:f,client:b.F.google_ad_client,eid:NR(b.F),et:g,fwrattr:b.F.google_full_width_responsive,iaaso:h,io:k,saldr:b.F.google_loader_used,oa:m,oe:l.join(","),qid:n,rafmt:b.F.google_responsive_auto_format,roa:p,slot:b.F.google_ad_slot,sp:w,tgt:u,tr:t,url:b.F.google_page_url,vp:e,pvc:Ge(a)},1)}).catch(e=>{Kl(8,["Error:",e.message,c]);kC("ovlp-err",{err:e.message},1)})} 
function Z4(a,b){b.allow=b.allow&&b.allow.length>0?b.allow+("; "+a):a} 
async function $4(a){const b=a.F,c=a.pubWin,d=a.j;a5(a);if(d.g()){var e=new LR(a.pubWin),f=a.j,g=a.pubWin.location.hostname;const D=HR(e,"__gpi_opt_out",f);if(D){var h=gl(D);var k=Mh(h,2,Hg(2147483647));var l=Qi(k,3,"/");var m=Qi(l,4,g);IR(e,"__gpi_opt_out",m,f)}}if(!d.g()&&!a.H)return kC("afc_noc_req",{client:a.F.google_ad_client,isGdprCountry:(AT(a.pageState.g())?zT(a.pageState.g()):O(yx)?!!z(a.Z,ji,26)?.g():C(a.Z,6)).toString()},W(Ev)),Promise.resolve();var n=a.Ma;b:{const D=[r.top],da=[];let qa= 
0,ya;for(;ya=D[qa++];){da.push(ya);try{if(ya.frames)for(let Ga=0;Ga<ya.frames.length&&D.length<1024;++Ga)D.push(ya.frames[Ga])}catch{}}var p=da;for(let Ga=0;Ga<p.length;Ga++)try{const Sb=p[Ga].frames.google_esf;if(Sb){Bl=Sb;break b}}catch(Sb){}Bl=null}if(Bl)var w=null;else{var u=Zd("IFRAME");u.id="google_esf";u.name="google_esf";var t=d.g()?n.Bi:n.Ai;u.src=Oc(t).toString();u.style.display="none";w=u}const B=w;B&&document.documentElement.appendChild(B);if(O(ox)&&d.g()){var I=dI();var U=gI(I,39,G4); 
a.C=q(await U)}a:{var M=a.pubWin;if(!O(xx)&&d.g()){const D=yR("shared-storage",M.document),da=yR("browsing-topics",M.document);if(D||da)try{var P=VW(M);break a}catch(qa){lC(984,qa)}}P=null}a.J=P;var V=a.pubWin;MR(20,V,d);MR(17,V,d);const Pa=a.F.google_reactive_ads_config;if(Pa){CV(a.L,Pa);JV(Pa,a,d);const D=Pa.page_level_pubvars;ta(D)&&Fc(a.F,D)}O(hx)&&ZT(a.pubWin,a.H);q(await q(ZW(a,a.pubWin,d,a.F,a.J,a.C)));q(await a.l?.tj);let Ra="";if(IV(b))Ra=(d.g()?a.Ma.Bi:a.Ma.Ai).toString()+"#"+(encodeURIComponent("RS-"+ 
b.google_reactive_sra_index+"-")+"&"+Dm({adk:b.google_ad_unit_key,client:b.google_ad_client,fa:b.google_reactive_ad_format})),Q0(b,dI()),b5(b);else{var la,Ca;if(!(Ca=b.google_pgb_reactive===5&&!!b.google_reactive_ads_config)){const D=b.google_reactive_ad_format;Ca=!(!b.google_reactive_ads_config&&GV(b)&&D!==16&&D!==10&&D!==11&&D!==40&&D!==41&&D!==42)}if(!(la=Ca)){{const D=b.google_reactive_ad_format;if(Ac(D)){var ic=Xd(c);if(ic&&sV(ic,b,D,d)){var Ib=kE(ic);Hr(Ib,D)?la=!1:(Ib.adCount[D]||(Ib.adCount[D]= 
0),Ib.adCount[D]++,la=!0)}else la=!1}else la=!1}}if(la&&b5(b)){const D=a.F;var zb=D,Aa=a.pubWin;const da={},qa=Aa.document;const ya={si:Je(Aa),Qf:!1,zh:"",Hf:1};a:{const Lb=zb.google_ad_width||Aa.google_ad_width,Rg=zb.google_ad_height||Aa.google_ad_height;if(Aa&&Aa.top===Aa)var ma=!1;else{var jc=Aa.document,od=jc.documentElement;if(Lb&&Rg){let Lf=1,Mf=1;Aa.innerHeight?(Lf=Aa.innerWidth,Mf=Aa.innerHeight):od&&od.clientHeight?(Lf=od.clientWidth,Mf=od.clientHeight):jc.body&&(Lf=jc.body.clientWidth,Mf= 
jc.body.clientHeight);if(Mf>2*Rg||Lf>2*Lb){ma=!1;break a}}ma=!0}}ya.Qf=ma;var je=ya.Qf,Ng=aI(ya.si).Yf;const Ga=Aa.top==Aa?0:Ud(Aa.top)?1:2;let Sb=4;je||Ga!==1?je||Ga!==2?je&&Ga===1?Sb=7:je&&Ga===2&&(Sb=8):Sb=6:Sb=5;Ng&&(Sb|=16);ya.zh=String(Sb);ya.Hf=cI(Aa);var Ld=ya;const If=Ld.si,me=Ld.Qf;let Ic=!!zb.google_page_url;da.google_iframing=Ld.zh;Ld.Hf!==0&&(da.google_iframing_environment=Ld.Hf);if(!Ic&&qa.domain==="ad.yieldmanager.com"){let Lb=qa.URL.substring(qa.URL.lastIndexOf("http"));for(;Lb.indexOf("%")> 
-1;)try{Lb=decodeURIComponent(Lb)}catch(Rg){break}zb.google_page_url=Lb;Ic=!!Lb}Ic?(da.google_page_url=zb.google_page_url,da.google_page_location=(me?qa.referrer:qa.URL)||"EMPTY"):(me&&Ud(Aa.top)&&qa.referrer&&Aa.top.document.referrer===qa.referrer?da.google_page_url=Aa.top.document.URL:da.google_page_url=me?qa.referrer:qa.URL,da.google_page_location=null);if(qa.URL===da.google_page_url)try{var Oe=Math.round(Date.parse(qa.lastModified)/1E3)||null}catch{Oe=null}else Oe=null;da.google_last_modified_time= 
Oe;if(If===If.top)var zf=If.document.referrer;else{var Af=Yl();zf=Af&&Af.referrer||""}da.google_referrer_url=zf;bI(da,D);if(d.g()){{let Lb=D.google_page_location||D.google_page_url;"EMPTY"===Lb&&(Lb=D.google_page_url);if(Lb){var Jb=Lb.toString();Jb.indexOf("http://")==0?Jb=Jb.substring(7,Jb.length):Jb.indexOf("https://")==0&&(Jb=Jb.substring(8,Jb.length));var Pe=Jb.indexOf("/");Pe===-1&&(Pe=Jb.length);var pd=Jb.substring(0,Pe).split("."),ke=!1;pd.length>=3&&(ke=pd[pd.length-3]in UT);pd.length>=2&& 
(ke=ke||pd[pd.length-2]in UT);var Bf=ke}else Bf=!1}var Cf=Bf?"pagead2.googlesyndication.com":"googleads.g.doubleclick.net"}else Cf="pagead2.googlesyndication.com";const Jf=Cf,Te=h1(a,d),ne=a.F,Pd=ne.google_ad_channel;let Kf="/pagead/ads?";ne.google_ad_client==="ca-pub-6219811747049371"&&i1.test(Pd)&&(Kf="/pagead/lopri?");const Qg=`https://${Jf}${Kf}`,gq=(CT(a.pageState.g())?BT(a.pageState.g()):C(a.Z,9))&&D.google_debug_params?D.google_debug_params:"",ol=Em(Te,Qg+gq);Ra=D.google_ad_url=ol}}Kl(2,[b, 
Ra]);if(!Ra)return Promise.resolve();if(!b.google_async_iframe_id){var Md=c;Md=cm(Yl(Md))||Md;Md.google_unique_id=(Md.google_unique_id||0)+1}const Qe=Gm(b),Df=a.pubWin===a.L?"a!"+Qe.toString(36):`${Qe.toString(36)}.${Math.floor(Math.random()*2147483648).toString(36)+Math.abs(Math.floor(Math.random()*2147483648)^Date.now()).toString(36)}`,Og=DU(a.L,a.pubWin,a.ba,!0)>0,Ef={ifi:Qe,uci:Df};if(Og){const D=dI();Ef.btvi=iI(D,21,1);kI(D,21)}Ra=Em(Ef,Ra);var db=Ra;const qd=a.F,Ff=qd.google_async_iframe_id, 
Re=qd.google_ad_width,Nd=qd.google_ad_height,Gf=IV(qd),Fa={id:Ff,name:Ff};var Ei=a.F,kc=a.C;yR("browsing-topics",a.pubWin.document)&&$W(d,Ei)&&!O(vx)&&!YW(kc?.label)&&(Fa.browsingTopics="true");Fa.style=Gf?[`width:${Re}px !IMPORTANT`,`height:${Nd}px !IMPORTANT`].join(";"):"left:0;position:absolute;top:0;border:0;"+`width:${Re}px;`+`height:${Nd}px;`;const Pg=se();if(Pg["allow-top-navigation-by-user-activation"]&&Pg["allow-popups-to-escape-sandbox"]){if(!Gf){var rd=db;var le="fsb="+encodeURIComponent("1"); 
if(le){let D=rd.indexOf("#");D<0&&(D=rd.length);let da=rd.indexOf("?"),qa;da<0||da>D?(da=D,qa=""):qa=rd.substring(da+1,D);var sd=[rd.slice(0,da),qa,rd.slice(D)];var Se=sd[1];sd[1]=le?Se?Se+"&"+le:le:Se;db=sd[0]+(sd[1]?"?"+sd[1]:"")+sd[2]}else db=rd}Fa.sandbox=re().join(" ")}qd.google_video_play_muted===!1&&Z4("autoplay",Fa);var lc=db;lc.length>61440&&(lc=lc.substring(0,61432),lc=lc.replace(/%\w?$/,""),lc=lc.replace(/&[^=]*=?$/,""),lc+="&trunc=1");var mc=lc;if(mc!==db){let D=db.lastIndexOf("&",61432); 
D===-1&&(D=db.lastIndexOf("?",61432));kC("trn",{ol:db.length,tr:D===-1?"":db.substring(D+1),url:db},.01)}db=mc;Re!=null&&(Fa.width=String(Re));Nd!=null&&(Fa.height=String(Nd));Fa.frameborder="0";Fa.marginwidth="0";Fa.marginheight="0";Fa.vspace="0";Fa.hspace="0";Fa.allowtransparency="true";Fa.scrolling="no";qd.dash&&(Fa.srcdoc=qd.dash);xR("attribution-reporting",a.pubWin.document)&&Z4("attribution-reporting",Fa);xR("run-ad-auction",a.pubWin.document)&&Z4("run-ad-auction",Fa);if(O(ix)){const D=a.pubWin; 
D.credentialless!==void 0&&(O(jx)||D.crossOriginIsolated)&&(Fa.credentialless="true")}if(Gf){Fa.src=db;const D=U4(Fa);var Tc=V4(a,D,d)}else{const D={};D.dtd=QW((new Date).getTime(),rr);var nc=Em(D,db);Fa.src=nc;var Kb=a.pubWin;var Fi=Kb==Kb.top;const da=U4(Fa);Fi&&a.i.push(dm(a.pubWin,da));a.ba.style.visibility="visible";var Od=a.ba;let qa;for(;qa=Od.firstChild;)Od.removeChild(qa);Od.appendChild(da);Tc=Promise.resolve(da)}var Hf=Tc;a.F.rpe&&O_(new Y_(a.pubWin,a.ba,void 0,{height:a.F.google_ad_height, 
Gg:"force",Dd:!0,ug:!0,Re:a.F.google_ad_client},null,null,!0));const Hc=q(await Hf);try{W4(a,Hc,Df,d)}catch(D){lC(223,D)}}function c5(a){const b=W(Bv);if(b<=0)return null;const c=Um(),d=B4(a.pubWin);if(!d)return null;a.D="0";return Promise.race([d,Ie(b,"0")]).then(e=>{kC("adsense_paw",{time:Um()-c});e?.length>1E4?lC(809,Error(`ML:${e.length}`)):a.D=e}).catch(e=>{lC(809,e)})} 
function a5(a){var b=a.pubWin;const c=a.ba;var d=a.F;const e=a.Ea,f=W(Ax);d=!Jr(d.google_reactive_ad_format)&&(GV(d)||d.google_reactive_ads_config);if(!(a.l?.Of||f<=0||Xd(b)||!r.IntersectionObserver||d)){a.l={};var g=new dr(e),h=Um();b=new Promise(k=>{let l=0;const m=a.l,n=new r.IntersectionObserver(iC(1236,p=>{if(p=p.find(w=>w.target===c))g.se.Ne.Cd.g.g.pd({ud:Um()-h,vl:++l}),m.hk=p.isIntersecting&&p.intersectionRatio>=.8,k()}),{threshold:[.8]});n.observe(c);m.Of=n});a.l.tj=Promise.race([b,Ie(f, 
null)]).then(k=>{g.se.Ne.Cd.g.i.pd({ud:Um()-h,status:k===null?"TIMEOUT":"OK"})})}}function d5(a){const b=Um();return Promise.race([hC(832,()=>F4(a)),Ie(200)]).then(c=>{kC("afc_etu",{etus:c?.status??100,sig:Um()-b,tms:200});return c?.Uc})}async function e5(a){const b=c5(a),c=hC(868,()=>d5(a.pubWin));q(await q(uU(a)));q(await b);a.Uc=q(await c)||"";q(await q($4(a)))} 
function T4(a){Je(a.pubWin)!==a.pubWin&&(a.g|=4);VU(a.pubWin.document)===3&&(a.g|=32);var b;if(b=a.L){b=a.L;const c=Br(b);b=!(Gr(b).scrollWidth<=c)}b&&(a.g|=1024);a.pubWin.Prototype?.Version&&(a.g|=16384);a.F.google_loader_features_used&&(a.g|=a.F.google_loader_features_used);return e5(a)}function b5(a){const b=dI(),c=a.google_ad_section;GV(a)&&kI(b,15);if(Im(a)){if(kI(b,5)>100)return!1}else if(kI(b,6)-iI(b,15,0)>100&&c==="")return!1;return!0} 
function K4(a){mI()&&r.setTimeout(iC(1244,()=>void LS(a.L||a.pubWin,{Ia:AT(a.pageState.g())?zT(a.pageState.g()):O(yx)?!!z(a.Z,ji,26)?.g():C(a.Z,6)})),1E3)};(function(a,b,c){hC(843,()=>{r.google_sa_impl||QT(a,b,c)})})(KT,vq(),function(a,b,c,d,e){c=c>2012?`_fy${c}`:"";e||(e=E(b,3));d||(d=E(b,2));return{Tk:Rd`https://pagead2.googlesyndication.com/pagead/js/${d}/${e}/rum${c}.js`,Sk:Rd`https://pagead2.googlesyndication.com/pagead/js/${d}/${e}/rum_debug${c}.js`,bi:Rd`https://pagead2.googlesyndication.com/pagead/managed/js/adsense/${a}/reactive_library${c}.js`,qj:Rd`https://pagead2.googlesyndication.com/pagead/managed/js/adsense/${a}/debug_card_library${c}.js`, 
wp:Rd`https://pagead2.googlesyndication.com/pagead/managed/js/adsense/${a}/slotcar_library${c}.js`,Bi:Rd`https://googleads.g.doubleclick.net/pagead/html/${d}/${e}/zrt_lookup${c}.html`,Ai:Rd`https://pagead2.googlesyndication.com/pagead/html/${d}/${e}/zrt_lookup${c}.html`}}); 
}).call(this,"");
