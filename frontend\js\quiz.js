// Quiz JavaScript for EduVerse Learning Hub

document.addEventListener('DOMContentLoaded', function() {
    // Check authentication state
    updateAuthUI();

    // Setup quiz selection
    setupQuizSelection();

    // Setup logout functionality
    setupLogout();
});

// Update UI based on authentication state
function updateAuthUI() {
    const isAuthenticated = AuthService.isAuthenticated();
    const loginBtn = document.querySelector('.login-btn');
    const userInfo = document.querySelector('.user-info');

    if (loginBtn) {
        if (isAuthenticated) {
            // User is logged in
            loginBtn.style.display = 'none';

            // Show user info if element exists
            if (userInfo) {
                const user = JSON.parse(localStorage.getItem('user'));
                userInfo.style.display = 'block';
                userInfo.querySelector('.username').textContent = user.fullName || user.username;
            }
        } else {
            // User is not logged in
            loginBtn.style.display = 'block';

            // Hide user info if element exists
            if (userInfo) {
                userInfo.style.display = 'none';
            }
        }
    }
}

// Setup quiz selection
function setupQuizSelection() {
    // Set up the initial quiz container with welcome message
    const quizContainer = document.getElementById('quiz-container');
    if (quizContainer) {
        quizContainer.innerHTML = `
            <div class="quiz-frame main-frame">
                <h2>Welcome to EduVerse Quizzes</h2>
                <p>Select a topic from the left menu to start a quiz</p>
                <p>Test your knowledge and improve your skills!</p>
                <div class="quiz-info">
                    <p><strong>Instructions:</strong></p>
                    <ul>
                        <li>Choose a topic from the left sidebar</li>
                        <li>Click the "Take Quiz" button to open the quiz in Google Forms</li>
                        <li>Complete all questions in the form</li>
                        <li>Submit your answers to see your score</li>
                    </ul>
                </div>
            </div>
        `;
    }

    // Set up click handlers for quiz items
    const quizItems = document.querySelectorAll('.left-side li');
    quizItems.forEach(item => {
        if (item.getAttribute('onclick')) {
            const originalOnClick = item.getAttribute('onclick');

            // Keep the original onclick attribute for compatibility
            // but also add our direct handler
            item.addEventListener('click', function() {
                // Extract quiz type from the original onclick attribute
                const match = originalOnClick.match(/quizt\((\d+)\)/);
                if (match && match[1]) {
                    const quizType = parseInt(match[1]);
                    loadQuiz(quizType);
                }
            });
        }
    });
}

// Quiz questions for each subject
const quizQuestions = {
    java: [
        {
            question: "What is the output of the following code?\n\nclass Test {\n    public static void main(String[] args) {\n        String s1 = \"Hello\";\n        String s2 = new String(\"Hello\");\n        System.out.println(s1 == s2);\n    }\n}",
            options: ["true", "false", "Compilation Error", "Runtime Error"],
            correctAnswer: 1
        },
        {
            question: "Which of the following is NOT a feature of Java?",
            options: ["Platform Independence", "Automatic Garbage Collection", "Operator Overloading", "Dynamic Binding"],
            correctAnswer: 2
        },
        {
            question: "What is the time complexity of adding an element to a LinkedList in Java?",
            options: ["O(1) for all operations", "O(1) for add at end, O(n) for add at specific position", "O(n) for all operations", "O(log n) for all operations"],
            correctAnswer: 1
        },
        {
            question: "Which of the following statements about Java interfaces is false?",
            options: ["Interfaces can have default methods", "Interfaces can have private methods", "Interfaces can extend multiple interfaces", "Interfaces can have static final variables"],
            correctAnswer: 1
        },
        {
            question: "What happens when you try to compile and run this code?\n\npublic class Test {\n    public static void main(String[] args) {\n        try {\n            throw new Exception();\n        } finally {\n            System.out.println(\"Finally\");\n        }\n    }\n}",
            options: ["Prints 'Finally' and terminates normally", "Prints 'Finally' and throws an exception", "Compilation error: Unhandled exception", "Runtime error"],
            correctAnswer: 2
        }
    ],
    python: [
        {
            question: "What is the output of the following code?\n\ndef func(a, b=[]):\n    b.append(a)\n    return b\n\nprint(func(1))\nprint(func(2))\nprint(func(3, []))",
            options: ["[1], [2], [3]", "[1], [1, 2], [3]", "[1], [2], [1, 2, 3]", "[1, 2, 3], [1, 2, 3], [3]"],
            correctAnswer: 1
        },
        {
            question: "Which of the following is NOT a valid way to copy a list in Python?",
            options: ["new_list = old_list.copy()", "new_list = list(old_list)", "new_list = old_list[:]", "new_list = old_list"],
            correctAnswer: 3
        },
        {
            question: "What is the output of the following code?\n\nclass A:\n    def __init__(self):\n        self.x = 1\n\nclass B(A):\n    def __init__(self):\n        super().__init__()\n        self.y = 2\n\nb = B()\nprint(hasattr(b, 'x'), hasattr(b, 'y'))",
            options: ["True True", "True False", "False True", "False False"],
            correctAnswer: 0
        },
        {
            question: "What is the time complexity of the 'in' operator when used with a Python set?",
            options: ["O(1)", "O(n)", "O(log n)", "O(n log n)"],
            correctAnswer: 0
        },
        {
            question: "What will be the output of the following code?\n\nimport threading\n\ndef print_numbers():\n    for i in range(5):\n        print(i)\n\nthreads = [threading.Thread(target=print_numbers) for _ in range(3)]\nfor thread in threads:\n    thread.start()",
            options: ["Numbers 0-4 printed in sequence three times", "Numbers 0-4 printed in a random interleaved order", "Error: threading is not supported in Python", "Numbers 0-4 printed exactly once"],
            correctAnswer: 1
        }
    ],
    javascript: [
        {
            question: "What is the output of the following code?\n\nconsole.log(typeof typeof 1);",
            options: ["number", "string", "undefined", "NaN"],
            correctAnswer: 1
        },
        {
            question: "Which of the following is NOT a valid way to create an object in JavaScript?",
            options: ["let obj = {}", "let obj = new Object()", "let obj = Object.create(null)", "let obj = Object()"],
            correctAnswer: 3
        },
        {
            question: "What is the output of the following code?\n\nconst arr = [1, 2, 3, 4, 5];\nconst [x, ...y, z] = arr;\nconsole.log(z);",
            options: ["5", "undefined", "SyntaxError", "[3, 4, 5]"],
            correctAnswer: 2
        },
        {
            question: "What is the difference between '==' and '===' in JavaScript?",
            options: ["They are identical in functionality", "'==' compares values, '===' compares values and types", "'===' compares values, '==' compares values and types", "'==' is deprecated in modern JavaScript"],
            correctAnswer: 1
        },
        {
            question: "What is the output of the following code?\n\nfunction createCounter() {\n    let count = 0;\n    return function() {\n        return ++count;\n    };\n}\n\nconst counter1 = createCounter();\nconst counter2 = createCounter();\n\nconsole.log(counter1());\nconsole.log(counter1());\nconsole.log(counter2());",
            options: ["1, 2, 1", "1, 1, 1", "1, 2, 3", "Error: count is not defined"],
            correctAnswer: 0
        }
    ],
    dataStructures: [
        {
            question: "Which data structure would be most efficient for implementing a priority queue?",
            options: ["Array", "Linked List", "Binary Search Tree", "Heap"],
            correctAnswer: 3
        },
        {
            question: "What is the worst-case time complexity for searching in a balanced binary search tree?",
            options: ["O(1)", "O(log n)", "O(n)", "O(n log n)"],
            correctAnswer: 1
        },
        {
            question: "Which of the following data structures allows O(1) access to elements by index?",
            options: ["Linked List", "Array", "Binary Tree", "Hash Table"],
            correctAnswer: 1
        },
        {
            question: "What is the space complexity of a recursive implementation of binary search?",
            options: ["O(1)", "O(log n)", "O(n)", "O(n log n)"],
            correctAnswer: 1
        },
        {
            question: "Which of the following is NOT a balanced binary search tree implementation?",
            options: ["AVL Tree", "Red-Black Tree", "B-Tree", "Binary Heap"],
            correctAnswer: 3
        }
    ],
    algorithms: [
        {
            question: "What is the worst-case time complexity of quicksort?",
            options: ["O(n)", "O(n log n)", "O(n²)", "O(2ⁿ)"],
            correctAnswer: 2
        },
        {
            question: "Which algorithm is used to find the shortest path in a weighted graph with negative edge weights?",
            options: ["Dijkstra's Algorithm", "Bellman-Ford Algorithm", "A* Search Algorithm", "Breadth-First Search"],
            correctAnswer: 1
        },
        {
            question: "What is the time complexity of the best algorithm for matrix multiplication of two n×n matrices?",
            options: ["O(n²)", "O(n²log n)", "O(n³)", "O(n^2.373) (Coppersmith–Winograd algorithm)"],
            correctAnswer: 3
        },
        {
            question: "Which of the following problems is NP-complete?",
            options: ["Finding the shortest path in an unweighted graph", "Sorting an array", "The traveling salesman problem", "Binary search"],
            correctAnswer: 2
        },
        {
            question: "What is the primary advantage of a greedy algorithm over dynamic programming?",
            options: ["Greedy algorithms always find the optimal solution", "Greedy algorithms are typically simpler and more efficient", "Greedy algorithms work for all optimization problems", "Greedy algorithms never get stuck in local optima"],
            correctAnswer: 1
        }
    ],
    interview: [
        {
            question: "Given an array of integers, write a function to find the two numbers that add up to a specific target. What is the most efficient approach?",
            options: ["Brute force with nested loops - O(n²)", "Sort and use two pointers - O(n log n)", "Use a hash table - O(n)", "Binary search for each element - O(n log n)"],
            correctAnswer: 2
        },
        {
            question: "How would you detect a cycle in a linked list?",
            options: ["Use a hash table to track visited nodes", "Use Floyd's Cycle-Finding Algorithm (tortoise and hare)", "Sort the linked list", "Count the number of nodes"],
            correctAnswer: 1
        },
        {
            question: "What is the time complexity of inserting n elements into a binary heap?",
            options: ["O(n)", "O(n log n)", "O(n²)", "O(log n)"],
            correctAnswer: 1
        },
        {
            question: "How would you implement a LRU (Least Recently Used) cache?",
            options: ["Using an array", "Using a hash table", "Using a linked list", "Using a hash table and a doubly linked list"],
            correctAnswer: 3
        },
        {
            question: "What approach would you use to find the kth largest element in an unsorted array?",
            options: ["Sort the array and return the kth element - O(n log n)", "Use a min-heap of size k - O(n log k)", "Use QuickSelect algorithm - O(n) average case", "Use counting sort - O(n+m) where m is the range of values"],
            correctAnswer: 2
        }
    ]
};

// Load quiz based on selected subject
function loadQuiz(quizType) {
    console.log(`Loading quiz for type: ${quizType}`);

    // Map quiz type to course name and questions
    let courseName;
    let description;
    let questions;

    switch (quizType) {
        case 3:
            courseName = 'Java';
            description = 'Test your knowledge of Java programming concepts, syntax, and best practices.';
            questions = quizQuestions.java;
            break;
        case 4:
            courseName = 'Python';
            description = 'Challenge yourself with Python programming questions covering basics to advanced topics.';
            questions = quizQuestions.python;
            break;
        case 5:
            courseName = 'JavaScript';
            description = 'Test your JavaScript knowledge with DOM manipulation, ES6 features, and more.';
            questions = quizQuestions.javascript;
            break;
        case 6:
            courseName = 'Data Structures';
            description = 'Evaluate your understanding of arrays, linked lists, trees, graphs, and other data structures.';
            questions = quizQuestions.dataStructures;
            break;
        case 7:
            courseName = 'Algorithm';
            description = 'Test your knowledge of sorting, searching, and other fundamental algorithms.';
            questions = quizQuestions.algorithms;
            break;
        case 8:
            courseName = 'Interview Questions';
            description = 'Practice with common coding interview questions from top tech companies.';
            questions = quizQuestions.interview;
            break;
        default:
            courseName = 'Java';
            description = 'Test your knowledge of Java programming concepts, syntax, and best practices.';
            questions = quizQuestions.java;
    }

    // Get the quiz container
    const quizContainer = document.getElementById('quiz-container');

    // Generate quiz HTML
    let quizHTML = `
        <div class="quiz-frame main-frame">
            <div class="quiz-header">
                <h2>${courseName} Quiz</h2>
                <p>${description}</p>
                <button onclick="window.location.reload()" class="quiz-btn secondary">Back to Topics</button>
            </div>

            <form id="quiz-form" class="scrollable-form">
    `;

    // Add questions
    questions.forEach((q, index) => {
        quizHTML += `
            <div class="question">
                <h3>Question ${index + 1}:</h3>
                <p>${q.question.replace(/\n/g, '<br>')}</p>
                <div class="options">
        `;

        q.options.forEach((option, optionIndex) => {
            quizHTML += `
                <label>
                    <input type="radio" name="q${index}" value="${optionIndex}" required>
                    ${option}
                </label>
            `;
        });

        quizHTML += `
                </div>
            </div>
        `;
    });

    // Add submit button
    quizHTML += `
            <button type="submit" class="submit-btn">Submit Answers</button>
        </form>

        <div id="quiz-results" style="display: none;">
            <h3>Your Results</h3>
            <p>Score: <span id="score">0</span> out of 5</p>
            <div id="result-details"></div>
            <button onclick="window.location.reload()" class="quiz-btn">Try Another Quiz</button>
        </div>

        <div class="quiz-info">
            <p><strong>Instructions:</strong></p>
            <ul>
                <li>Answer all 5 questions in the quiz</li>
                <li>Each question has only one correct answer</li>
                <li>Submit your answers to see your score immediately</li>
                <li>Challenge yourself with different subjects to improve your skills</li>
            </ul>
        </div>
    </div>
    `;

    // Set the HTML
    quizContainer.innerHTML = quizHTML;

    // Add event listener for form submission
    const quizForm = document.getElementById('quiz-form');
    quizForm.addEventListener('submit', function(e) {
        e.preventDefault();
        submitQuiz(questions);
    });
}

// Submit quiz and show results
function submitQuiz(questions) {
    let score = 0;
    let resultDetails = '';

    // Check each answer
    questions.forEach((q, index) => {
        const selectedOption = document.querySelector(`input[name="q${index}"]:checked`);

        if (selectedOption) {
            const userAnswer = parseInt(selectedOption.value);
            const isCorrect = userAnswer === q.correctAnswer;

            if (isCorrect) {
                score++;
            }

            // Add to result details
            resultDetails += `
                <div class="result-item ${isCorrect ? 'correct' : 'incorrect'}">
                    <p><strong>Question ${index + 1}:</strong> ${isCorrect ? 'Correct' : 'Incorrect'}</p>
                    <p>Your answer: ${q.options[userAnswer]}</p>
                    ${!isCorrect ? `<p>Correct answer: ${q.options[q.correctAnswer]}</p>` : ''}
                </div>
            `;
        }
    });

    // Show results
    document.getElementById('quiz-form').style.display = 'none';
    document.getElementById('quiz-results').style.display = 'block';
    document.getElementById('score').textContent = score;
    document.getElementById('result-details').innerHTML = resultDetails;

    // Add pass/fail message
    const passThreshold = 3; // 60% passing score
    const resultMessage = score >= passThreshold ?
        `<p class="pass-message">Congratulations! You passed the quiz.</p>` :
        `<p class="fail-message">You didn't pass this time. Keep studying and try again!</p>`;

    document.getElementById('result-details').innerHTML += resultMessage;
}

// Note: The createQuizUI and submitQuiz functions have been removed
// as we're now using Google Forms directly for quizzes

// Function to start the quiz from the main page
function startquiz() {
    // Hide the title section
    const titleSection = document.getElementById('title');
    if (titleSection) {
        titleSection.style.display = 'none';
    }

    // Show the panel with quiz options
    const panel = document.getElementById('panel');
    if (panel) {
        panel.style.display = 'flex';

        // Ensure the right side is scrollable
        const rightSide = document.getElementById('right');
        if (rightSide) {
            rightSide.scrollTop = 0;
        }
    }
}

// Function to handle quiz type selection (called from HTML)
function quizt(n) {
    console.log(`Loading quiz type: ${n}`);
    // Call loadQuiz with the selected quiz type
    loadQuiz(n);
}

// Setup logout functionality
function setupLogout() {
    const logoutBtn = document.querySelector('.logout-btn');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', function(event) {
            event.preventDefault();
            AuthService.logout();
            window.location.href = 'index.html';
        });
    }
}

// Initialize when document is ready
$(document).ready(function() {
    console.log("Quiz page initialized");

    // Setup logout if user is logged in
    setupLogout();

    // Update UI based on authentication state
    const isAuthenticated = localStorage.getItem('user') !== null;
    const loginBtn = document.querySelector('.login-btn');
    const userInfo = document.querySelector('.user-info');

    if (loginBtn && userInfo) {
        if (isAuthenticated) {
            // User is logged in
            loginBtn.style.display = 'none';

            // Show user info
            const user = JSON.parse(localStorage.getItem('user'));
            userInfo.style.display = 'block';
            userInfo.querySelector('.username').textContent = user.fullName || user.username;
        } else {
            // User is not logged in
            loginBtn.style.display = 'block';
            userInfo.style.display = 'none';
        }
    }
});
