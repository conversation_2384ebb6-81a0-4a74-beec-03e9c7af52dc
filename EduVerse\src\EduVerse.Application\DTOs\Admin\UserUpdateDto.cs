using System.ComponentModel.DataAnnotations;

namespace EduVerse.Application.DTOs.Admin
{
    /// <summary>
    /// DTO for updating a user
    /// </summary>
    public class UserUpdateDto
    {
        /// <summary>
        /// User ID
        /// </summary>
        [Required]
        public int Id { get; set; }

        /// <summary>
        /// Full name
        /// </summary>
        [Required]
        public string FullName { get; set; }

        /// <summary>
        /// Email address
        /// </summary>
        [Required]
        [EmailAddress]
        public string Email { get; set; }

        /// <summary>
        /// Username
        /// </summary>
        [Required]
        public string Username { get; set; }

        /// <summary>
        /// User role
        /// </summary>
        [Required]
        public string Role { get; set; }
    }
}
