using AutoMapper;
using EduVerse.Application.DTOs;
using EduVerse.Core.Entities;

namespace EduVerse.API.Mapping
{
    /// <summary>
    /// AutoMapper profile for discussion entities
    /// </summary>
    public class DiscussionMappingProfile : Profile
    {
        /// <summary>
        /// Constructor
        /// </summary>
        public DiscussionMappingProfile()
        {
            // Discussion mappings
            CreateMap<Discussion, DiscussionDto>()
                .ForMember(dest => dest.UserName, opt => opt.MapFrom(src => src.User.FullName))
                .ForMember(dest => dest.CourseName, opt => opt.MapFrom(src => src.Course.Title))
                .ForMember(dest => dest.ReplyCount, opt => opt.MapFrom(src => src.Replies.Count));
            
            CreateMap<CreateDiscussionDto, Discussion>();
            
            // DiscussionReply mappings
            CreateMap<DiscussionReply, DiscussionReplyDto>()
                .ForMember(dest => dest.UserName, opt => opt.MapFrom(src => src.User.FullName));
            
            CreateMap<CreateDiscussionReplyDto, DiscussionReply>();
        }
    }
}
