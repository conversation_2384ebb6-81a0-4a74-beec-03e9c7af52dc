// Admin Navigation JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Setup menu navigation
    setupMenuNavigation();
});

// Setup menu navigation
function setupMenuNavigation() {
    // Fix for menu links not working
    // Remove any existing click event listeners
    const menuLinks = document.querySelectorAll('.menu a');
    menuLinks.forEach(link => {
        const oldLink = link.cloneNode(true);
        link.parentNode.replaceChild(oldLink, link);

        // Add direct click handler that forces navigation
        oldLink.onclick = function(event) {
            event.preventDefault();
            const href = this.getAttribute('href');
            console.log('Menu link clicked:', href);

            if (href && href !== '#') {
                // Force navigation by setting location directly
                window.location.href = href;
                return false;
            }
        };
    });

    // Add click event listener to logout button
    const logoutBtn = document.getElementById('admin-logout');
    if (logoutBtn) {
        // Remove any existing event listeners
        const oldLogoutBtn = logoutBtn.cloneNode(true);
        logoutBtn.parentNode.replaceChild(oldLogoutBtn, logoutBtn);

        // Add direct click handler
        oldLogoutBtn.onclick = function(event) {
            event.preventDefault();

            // Clear localStorage
            localStorage.removeItem('token');
            localStorage.removeItem('user');

            // Redirect to login page
            window.location.href = 'admin-login.html';
            return false;
        };
    }

    // Add click event listener to toggle menu button
    const toggleMenu = document.querySelector('.toggle-menu');
    if (toggleMenu) {
        // Remove any existing event listeners
        const oldToggleMenu = toggleMenu.cloneNode(true);
        toggleMenu.parentNode.replaceChild(oldToggleMenu, toggleMenu);

        // Add direct click handler
        oldToggleMenu.onclick = function() {
            document.querySelector('.admin-container').classList.toggle('sidebar-collapsed');
        };
    }
}
