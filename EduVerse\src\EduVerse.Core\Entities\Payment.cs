using System;

namespace EduVerse.Core.Entities
{
    /// <summary>
    /// Payment entity
    /// </summary>
    public class Payment
    {
        /// <summary>
        /// Payment ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// User ID
        /// </summary>
        public int UserId { get; set; }

        /// <summary>
        /// Course ID
        /// </summary>
        public int CourseId { get; set; }

        /// <summary>
        /// Payment amount
        /// </summary>
        public decimal Amount { get; set; }

        /// <summary>
        /// Payment date
        /// </summary>
        public DateTime Date { get; set; }

        /// <summary>
        /// Payment method
        /// </summary>
        public string PaymentMethod { get; set; }

        /// <summary>
        /// Payment status
        /// </summary>
        public string Status { get; set; }

        /// <summary>
        /// Transaction ID
        /// </summary>
        public string TransactionId { get; set; }

        /// <summary>
        /// Razorpay Order ID
        /// </summary>
        public string RazorpayOrderId { get; set; }

        /// <summary>
        /// Billing address
        /// </summary>
        public string BillingAddress { get; set; }

        /// <summary>
        /// Navigation property for user
        /// </summary>
        public User User { get; set; }

        /// <summary>
        /// Navigation property for course
        /// </summary>
        public Course Course { get; set; }

        /// <summary>
        /// Navigation property for enrollment
        /// </summary>
        public Enrollment Enrollment { get; set; }
    }
}
