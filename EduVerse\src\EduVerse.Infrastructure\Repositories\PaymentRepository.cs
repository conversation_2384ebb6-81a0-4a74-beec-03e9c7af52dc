using EduVerse.Core.Entities;
using EduVerse.Core.Interfaces;
using EduVerse.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace EduVerse.Infrastructure.Repositories
{
    /// <summary>
    /// Repository for payments
    /// </summary>
    public class PaymentRepository : Repository<Payment>, IPaymentRepository
    {
        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="context">Database context</param>
        public PaymentRepository(ApplicationDbContext context) : base(context)
        {
        }

        /// <summary>
        /// Get all payments with pagination
        /// </summary>
        /// <param name="page">Page number</param>
        /// <param name="pageSize">Items per page</param>
        /// <returns>Paginated list of payments</returns>
        public async Task<(IEnumerable<Payment> Items, int TotalCount)> GetAllPaymentsPaginatedAsync(int page, int pageSize)
        {
            var totalCount = await _context.Payments.CountAsync();
            var items = await _context.Payments
                .Include(p => p.User)
                .Include(p => p.Course)
                .OrderByDescending(p => p.Date)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            return (items, totalCount);
        }

        /// <summary>
        /// Get payment by ID with related entities
        /// </summary>
        /// <param name="id">Payment ID</param>
        /// <returns>Payment with related entities</returns>
        public async Task<Payment> GetPaymentByIdWithDetailsAsync(int id)
        {
            return await _context.Payments
                .Include(p => p.User)
                .Include(p => p.Course)
                .FirstOrDefaultAsync(p => p.Id == id);
        }

        /// <summary>
        /// Get total revenue
        /// </summary>
        /// <returns>Total revenue</returns>
        public async Task<decimal> GetTotalRevenueAsync()
        {
            return await _context.Payments
                .Where(p => p.Status == "completed")
                .SumAsync(p => p.Amount);
        }

        /// <summary>
        /// Get revenue by period
        /// </summary>
        /// <param name="period">Period (daily, weekly, monthly, yearly)</param>
        /// <returns>Revenue data by period</returns>
        public async Task<IEnumerable<(string Label, decimal Value)>> GetRevenueByPeriodAsync(string period)
        {
            var completedPayments = _context.Payments
                .Where(p => p.Status == "completed");

            switch (period.ToLower())
            {
                case "daily":
                    // Last 7 days
                    var dailyData = await completedPayments
                        .Where(p => p.Date >= DateTime.Now.AddDays(-7))
                        .GroupBy(p => p.Date.Date)
                        .Select(g => new { Date = g.Key, Revenue = g.Sum(p => p.Amount) })
                        .OrderBy(x => x.Date)
                        .ToListAsync();

                    return dailyData.Select(x => (x.Date.ToString("MM/dd"), x.Revenue));

                case "weekly":
                    // Last 8 weeks
                    var startDate = DateTime.Now.AddDays(-(int)DateTime.Now.DayOfWeek).AddDays(-49); // 7 weeks ago, starting from Sunday
                    var weeklyData = await completedPayments
                        .Where(p => p.Date >= startDate)
                        .GroupBy(p => new { Year = p.Date.Year, Week = (p.Date.DayOfYear / 7) + 1 })
                        .Select(g => new { g.Key.Year, g.Key.Week, Revenue = g.Sum(p => p.Amount) })
                        .OrderBy(x => x.Year).ThenBy(x => x.Week)
                        .ToListAsync();

                    return weeklyData.Select(x => ($"Week {x.Week}", x.Revenue));

                case "yearly":
                    // Last 5 years
                    var yearlyData = await completedPayments
                        .Where(p => p.Date.Year >= DateTime.Now.Year - 5)
                        .GroupBy(p => p.Date.Year)
                        .Select(g => new { Year = g.Key, Revenue = g.Sum(p => p.Amount) })
                        .OrderBy(x => x.Year)
                        .ToListAsync();

                    return yearlyData.Select(x => (x.Year.ToString(), x.Revenue));

                case "monthly":
                default:
                    // Last 12 months
                    var monthlyData = await completedPayments
                        .Where(p => p.Date >= DateTime.Now.AddMonths(-12))
                        .GroupBy(p => new { p.Date.Year, p.Date.Month })
                        .Select(g => new { g.Key.Year, g.Key.Month, Revenue = g.Sum(p => p.Amount) })
                        .OrderBy(x => x.Year).ThenBy(x => x.Month)
                        .ToListAsync();

                    return monthlyData.Select(x => (new DateTime(x.Year, x.Month, 1).ToString("MMM yyyy"), x.Revenue));
            }
        }
    }
}
