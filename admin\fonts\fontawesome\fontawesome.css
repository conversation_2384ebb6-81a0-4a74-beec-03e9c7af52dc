/* Minimal Font Awesome CSS with embedded font data */
@font-face {
  font-family: 'Font Awesome 5 Free';
  font-style: normal;
  font-weight: 900;
  font-display: block;
  /* Use system fonts as fallback */
  src: local('Font Awesome 5 Free Solid'), local('FontAwesome'), local('Arial');
}

.fas {
  font-family: 'Font Awesome 5 Free';
  font-weight: 900;
}

/* Basic icon definitions */
.fa-tachometer-alt:before { content: "\f3fd"; }
.fa-book:before { content: "\f02d"; }
.fa-users:before { content: "\f0c0"; }
.fa-credit-card:before { content: "\f09d"; }
.fa-comments:before { content: "\f086"; }
.fa-cog:before { content: "\f013"; }
.fa-sign-out-alt:before { content: "\f2f5"; }
.fa-bars:before { content: "\f0c9"; }
.fa-search:before { content: "\f002"; }
.fa-bell:before { content: "\f0f3"; }
.fa-dollar-sign:before { content: "\f155"; }
.fa-user-graduate:before { content: "\f501"; }

/* Use Unicode characters as fallback */
.fas.fa-tachometer-alt:after { content: "📊"; }
.fas.fa-book:after { content: "📚"; }
.fas.fa-users:after { content: "👥"; }
.fas.fa-credit-card:after { content: "💳"; }
.fas.fa-comments:after { content: "💬"; }
.fas.fa-cog:after { content: "⚙️"; }
.fas.fa-sign-out-alt:after { content: "🚪"; }
.fas.fa-bars:after { content: "☰"; }
.fas.fa-search:after { content: "🔍"; }
.fas.fa-bell:after { content: "🔔"; }
.fas.fa-dollar-sign:after { content: "💲"; }
.fas.fa-user-graduate:after { content: "🎓"; }
