<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - EduVerse</title>
    <link rel="shortcut icon" type="png" href="../images/icon/favicon.png">
    <link rel="stylesheet" href="admin-style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.4.1/jquery.min.js"></script>
    <script src="admin-navigation.js"></script>
    <script src="menu-fix.js"></script>
    <script>
        // Define API_BASE_URL directly to ensure it's available
        const API_BASE_URL = 'http://localhost:5217';

        // Mock data for offline use
        const mockData = {
            dashboardStats: {
                totalUsers: 120,
                totalCourses: 25,
                totalRevenue: 15750,
                totalEnrollments: 350
            },
            recentEnrollments: [
                { userName: 'Sush', courseName: 'Web Development', enrollmentDate: '2023-05-01', amount: 667, status: 'Completed' },
                { userName: 'Hrushi', courseName: 'Data Science', enrollmentDate: '2023-05-02', amount: 129, status: 'Pending' },
                { userName: 'Gaurav', courseName: 'Mobile App Development', enrollmentDate: '2023-05-03', amount: 149, status: 'Completed' },
                { userName: 'Sujal', courseName: 'UI/UX Design', enrollmentDate: '2023-05-04', amount: 89, status: 'Pending' },
                { userName: 'Pooja', courseName: 'Python Programming', enrollmentDate: '2023-05-05', amount: 79, status: 'Completed' }
            ],
            recentFeedback: [
                { name: 'Sush', message: 'Great platform for learning!', createdAt: '2023-05-01' },
                { name: 'Gaurav', message: 'The courses are very informative.', createdAt: '202-05-02' },
                { name: 'Sujal', message: 'I love the UI/UX Design course. Very comprehensive!', createdAt: '2023-05-03' }
            ],
           
        };
    </script>
</head>
<body>
    <div class="admin-container">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="logo">
                <img src="../images/icon/logo.png" alt="EduVerse Logo">
                <h2>EduVerse</h2>
            </div>
            <div class="menu">
                <ul>
                    <li class="active">
                        <a href="dashboard.html">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>Dashboard</span>
                        </a>
                    </li>
                    <li>
                        <a href="users.html">
                            <i class="fas fa-users"></i>
                            <span>Users</span>
                        </a>
                    </li>
                    <li>
                        <a href="courses.html">
                            <i class="fas fa-book"></i>
                            <span>Courses</span>
                        </a>
                    </li>
                    <li>
                        <a href="payments.html">
                            <i class="fas fa-credit-card"></i>
                            <span>Payments</span>
                        </a>
                    </li>
                    <li>
                        <a href="feedback.html">
                            <i class="fas fa-comments"></i>
                            <span>Feedback</span>
                        </a>
                    </li>
                    
                    </li>
                </ul>
            </div>
            <div class="logout">
                <a href="#" id="admin-logout">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Logout</span>
                </a>
            </div>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Top Bar -->
            <div class="top-bar">
                <div class="toggle-menu">
                    <i class="fas fa-bars"></i>
                </div>
                <div class="search-box">
                    <input type="text" placeholder="Search...">
                    <i class="fas fa-search"></i>
                </div>
                <div class="user-info">
                    <div class="notifications">
                        <i class="fas fa-bell"></i>
                        <span class="badge">3</span>
                    </div>
                    <div class="profile">
                        <img src="../images/icon/user.png" alt="Admin" id="admin-avatar">
                        <span class="admin-name">Admin User</span>
                    </div>
                </div>
            </div>

            <!-- Dashboard Content -->
            <div class="dashboard-content">
                <h1>Dashboard</h1>
                <div class="stats-cards">
                    <div class="card">
                        <div class="card-icon users">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="card-info">
                            <h3>Total Users</h3>
                            <h2 id="total-users">0</h2>
                            <p><span class="positive">+5%</span> from last month</p>
                        </div>
                    </div>
                    <div class="card">
                        <div class="card-icon courses">
                            <i class="fas fa-book"></i>
                        </div>
                        <div class="card-info">
                            <h3>Total Courses</h3>
                            <h2 id="total-courses">80</h2>
                            <p><span class="positive">+2%</span> from last month</p>
                        </div>
                    </div>
                    <div class="card">
                        <div class="card-icon revenue">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                        <div class="card-info">
                            <h3>Total Revenue</h3>
                            <h2 id="total-revenue">$8</h2>
                            <p><span class="positive">+8%</span> from last month</p>
                        </div>
                    </div>
                    <div class="card">
                        <div class="card-icon enrollments">
                            <i class="fas fa-user-graduate"></i>
                        </div>
                        <div class="card-info">
                            <h3>Enrollments</h3>
                            <h2 id="total-enrollments">90</h2>
                            <p><span class="positive">+12%</span> from last month</p>
                        </div>
                    </div>
                </div>


                <div class="recent-section">
                    <div class="recent-enrollments">
                        <h3>Recent Enrollments</h3>
                        <table>
                            <thead>
                                <tr>
                                    <th>User</th>
                                    <th>Course</th>
                                    <th>Date</th>
                                    <th>Amount</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody id="recent-enrollments-table">
                                <!-- Will be populated by JavaScript -->
                                <tr><td colspan="5">Loading enrollments...</td></tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="recent-feedback">
                        <h3>Recent Feedback</h3>
                        <div id="recent-feedback-list">
                            <!-- Will be populated by JavaScript -->
                            <p>Loading feedback...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize dashboard
            initDashboard();

            // Setup event listeners
            setupEventListeners();
        });

        // Initialize dashboard
        async function initDashboard() {
            try {
                // Update admin info
                updateAdminInfo();

                // Load dashboard statistics
                await loadDashboardStats();


                // Load recent data
                await loadRecentEnrollments();
                await loadRecentFeedback();
            } catch (error) {
                console.error('Error initializing dashboard:', error);
            }
        }

        // Update admin info
        function updateAdminInfo() {
            const user = JSON.parse(localStorage.getItem('user') || '{}');
            const adminNameElement = document.querySelector('.admin-name');

            if (adminNameElement && user.fullName) {
                adminNameElement.textContent = user.fullName;
            }
        }

        // Load dashboard statistics
        async function loadDashboardStats() {
            try {
                // Use mock data for now
                const stats = mockData.dashboardStats;

                // Update stats cards
                document.getElementById('total-users').textContent = stats.totalUsers;
                document.getElementById('total-courses').textContent = stats.totalCourses;
                document.getElementById('total-revenue').textContent = `$${stats.totalRevenue}`;
                document.getElementById('total-enrollments').textContent = stats.totalEnrollments;
            } catch (error) {
                console.error('Error loading dashboard stats:', error);
            }
        }

        
       

        // Load recent enrollments
        async function loadRecentEnrollments() {
            try {
                // Use mock data for now
                const enrollments = mockData.recentEnrollments;

                const tableBody = document.getElementById('recent-enrollments-table');
                tableBody.innerHTML = '';

                enrollments.forEach(enrollment => {
                    const row = document.createElement('tr');

                    // Determine status class
                    let statusClass = '';
                    switch (enrollment.status.toLowerCase()) {
                        case 'completed':
                            statusClass = 'completed';
                            break;
                        case 'pending':
                            statusClass = 'pending';
                            break;
                        case 'failed':
                            statusClass = 'failed';
                            break;
                        default:
                            statusClass = '';
                    }

                    row.innerHTML = `
                        <td>${enrollment.userName}</td>
                        <td>${enrollment.courseName}</td>
                        <td>${enrollment.enrollmentDate}</td>
                        <td>$${enrollment.amount}</td>
                        <td><span class="status ${statusClass}">${enrollment.status}</span></td>
                    `;

                    tableBody.appendChild(row);
                });
            } catch (error) {
                console.error('Error loading recent enrollments:', error);
            }
        }

        // Load recent feedback
        async function loadRecentFeedback() {
            try {
                // Use mock data for now
                const feedbackList = mockData.recentFeedback;

                const feedbackContainer = document.getElementById('recent-feedback-list');
                feedbackContainer.innerHTML = '';

                feedbackList.forEach(feedback => {
                    const feedbackItem = document.createElement('div');
                    feedbackItem.className = 'feedback-item';

                    feedbackItem.innerHTML = `
                        <div class="feedback-header">
                            <div class="feedback-user">${feedback.name}</div>
                            <div class="feedback-date">${feedback.createdAt}</div>
                        </div>
                        <div class="feedback-message">${feedback.message}</div>
                    `;

                    feedbackContainer.appendChild(feedbackItem);
                });
            } catch (error) {
                console.error('Error loading recent feedback:', error);
            }
        }

        // Setup event listeners
        function setupEventListeners() {
            // Toggle sidebar
            const toggleMenu = document.querySelector('.toggle-menu');
            if (toggleMenu) {
                toggleMenu.addEventListener('click', function() {
                    document.querySelector('.admin-container').classList.toggle('sidebar-collapsed');
                });
            }

            // Admin logout
            const logoutBtn = document.getElementById('admin-logout');
            if (logoutBtn) {
                logoutBtn.addEventListener('click', function(event) {
                    event.preventDefault();

                    // Clear localStorage
                    localStorage.removeItem('token');
                    localStorage.removeItem('user');

                    // Redirect to login page
                    window.location.href = '../admin-login.html';
                });
            }

            // Add click event listeners to all menu links
            const menuLinks = document.querySelectorAll('.menu a');
            menuLinks.forEach(link => {
                link.addEventListener('click', function(event) {
                    console.log('Menu link clicked:', this.getAttribute('href'));
                    const href = this.getAttribute('href');
                    if (href && href !== '#') {
                        window.location.href = href;
                    }
                });
            });
        }
    </script>
</body>
</html>
