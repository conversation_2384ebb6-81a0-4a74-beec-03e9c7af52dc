using EduVerse.Core.Entities;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace EduVerse.Core.Interfaces
{
    /// <summary>
    /// Interface for enrollment repository
    /// </summary>
    public interface IEnrollmentRepository : IRepository<Enrollment>
    {
        /// <summary>
        /// Get recent enrollments
        /// </summary>
        /// <param name="limit">Number of enrollments to return</param>
        /// <returns>List of recent enrollments</returns>
        Task<IEnumerable<Enrollment>> GetRecentEnrollmentsAsync(int limit);

        /// <summary>
        /// Get total enrollments
        /// </summary>
        /// <returns>Total number of enrollments</returns>
        Task<int> GetTotalEnrollmentsAsync();

        /// <summary>
        /// Get enrollments by course ID
        /// </summary>
        /// <param name="courseId">Course ID</param>
        /// <returns>Number of enrollments for the course</returns>
        Task<int> GetEnrollmentsByCourseIdAsync(int courseId);
    }
}
