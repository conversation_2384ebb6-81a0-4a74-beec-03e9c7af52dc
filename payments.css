/* Payments History Styles */

.payments-content {
    padding: 20px;
}

.payments-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.payments-header h1 {
    color: var(--primary-color);
    margin: 0;
}

.export-options {
    display: flex;
    gap: 10px;
}

.export-btn {
    background-color: var(--card-bg);
    border: 1px solid #ddd;
    border-radius: 5px;
    padding: 8px 15px;
    display: flex;
    align-items: center;
    gap: 5px;
    cursor: pointer;
    transition: var(--transition);
}

.export-btn:hover {
    background-color: #f5f5f5;
}

.payments-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.summary-card {
    background-color: var(--card-bg);
    border-radius: 10px;
    padding: 20px;
    box-shadow: var(--shadow);
    display: flex;
    align-items: center;
}

.summary-icon {
    width: 50px;
    height: 50px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    background-color: rgba(223, 39, 113, 0.1);
    color: var(--primary-color);
}

.summary-icon i {
    font-size: 24px;
}

.summary-info h3 {
    font-size: 14px;
    color: var(--light-text);
    margin-bottom: 5px;
}

.summary-info h2 {
    font-size: 24px;
    margin: 0;
}

.payments-filters {
    background-color: var(--card-bg);
    border-radius: 10px;
    padding: 20px;
    box-shadow: var(--shadow);
    margin-bottom: 20px;
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    align-items: center;
}

.filter-group {
    display: flex;
    align-items: center;
    gap: 8px;
}

.filter-group label {
    font-weight: 500;
}

.filter-group select,
.filter-group input {
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 5px;
    outline: none;
}

.filter-btn {
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 5px;
    padding: 8px 15px;
    cursor: pointer;
    transition: var(--transition);
    margin-left: auto;
}

.filter-btn:hover {
    background-color: var(--secondary-color);
}

.payments-table-container {
    background-color: var(--card-bg);
    border-radius: 10px;
    box-shadow: var(--shadow);
    overflow: hidden;
    margin-bottom: 20px;
}

.payments-table {
    width: 100%;
    border-collapse: collapse;
}

.payments-table th,
.payments-table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.payments-table th {
    background-color: #f9f9f9;
    font-weight: 600;
    color: var(--text-color);
}

.payments-table tbody tr:hover {
    background-color: #f5f5f5;
}

.payment-status {
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 12px;
    text-align: center;
    display: inline-block;
}

.status-completed {
    background-color: rgba(76, 175, 80, 0.1);
    color: #4CAF50;
}

.status-pending {
    background-color: rgba(255, 152, 0, 0.1);
    color: #FF9800;
}

.status-failed {
    background-color: rgba(244, 67, 54, 0.1);
    color: #F44336;
}

.status-refunded {
    background-color: rgba(33, 150, 243, 0.1);
    color: #2196F3;
}

.payment-actions {
    display: flex;
    gap: 10px;
}

.action-btn {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
}

.view-btn {
    background-color: rgba(33, 150, 243, 0.1);
    color: #2196F3;
}

.view-btn:hover {
    background-color: #2196F3;
    color: white;
}

.receipt-btn {
    background-color: rgba(76, 175, 80, 0.1);
    color: #4CAF50;
}

.receipt-btn:hover {
    background-color: #4CAF50;
    color: white;
}

.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 15px;
}

.pagination-btn {
    padding: 8px 15px;
    background-color: var(--card-bg);
    border: 1px solid #ddd;
    border-radius: 5px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 5px;
    transition: var(--transition);
}

.pagination-btn:hover:not(:disabled) {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

#page-info {
    font-weight: 500;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    overflow: auto;
}

.modal-content {
    background-color: var(--card-bg);
    margin: 50px auto;
    width: 70%;
    max-width: 600px;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    animation: modalFadeIn 0.3s;
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-header {
    padding: 15px 20px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h2 {
    margin: 0;
    color: var(--primary-color);
}

.close {
    font-size: 28px;
    font-weight: bold;
    color: var(--light-text);
    cursor: pointer;
}

.close:hover {
    color: var(--text-color);
}

.modal-body {
    padding: 20px;
}

.payment-details {
    margin-bottom: 20px;
}

.detail-row {
    display: flex;
    margin-bottom: 10px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.detail-row:last-child {
    border-bottom: none;
}

.detail-label {
    width: 150px;
    font-weight: 600;
    color: var(--text-color);
}

.detail-value {
    flex: 1;
}

.payment-actions {
    display: flex;
    gap: 10px;
    margin-top: 20px;
}

#update-status {
    flex: 1;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
    outline: none;
}

.update-btn {
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 5px;
    padding: 10px 15px;
    cursor: pointer;
    transition: var(--transition);
}

.update-btn:hover {
    background-color: var(--secondary-color);
}

/* Responsive Styles */
@media (max-width: 992px) {
    .payments-filters {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .filter-btn {
        margin-left: 0;
    }
    
    .modal-content {
        width: 90%;
    }
}

@media (max-width: 768px) {
    .payments-table th:nth-child(2),
    .payments-table td:nth-child(2),
    .payments-table th:nth-child(6),
    .payments-table td:nth-child(6) {
        display: none;
    }
}

@media (max-width: 576px) {
    .payments-table th:nth-child(1),
    .payments-table td:nth-child(1),
    .payments-table th:nth-child(3),
    .payments-table td:nth-child(3) {
        display: none;
    }
    
    .export-options {
        display: none;
    }
}
