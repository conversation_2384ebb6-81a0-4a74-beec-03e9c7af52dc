using Microsoft.AspNetCore.Mvc;

namespace EduVerse.API.Controllers
{
    /// <summary>
    /// Controller for health checks
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    [Produces("application/json")]
    [Tags("Health")]
    public class HealthController : ControllerBase
    {
        /// <summary>
        /// Check if the API is running
        /// </summary>
        /// <returns>Health status</returns>
        [HttpGet]
        [ProducesResponseType(typeof(object), 200)]
        public IActionResult Get()
        {
            return Ok(new { status = "healthy", timestamp = DateTime.UtcNow });
        }
    }
}
