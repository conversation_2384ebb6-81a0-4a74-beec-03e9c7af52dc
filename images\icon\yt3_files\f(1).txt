(function(sttc){'use strict';var l,aa=Object.defineProperty,ca=globalThis,da=typeof Symbol==="function"&&typeof Symbol("x")==="symbol",ea={},fa={};function ha(a,b,c){if(!c||a!=null){c=fa[b];if(c==null)return a[b];c=a[c];return c!==void 0?c:a[b]}} 
function ia(a,b,c){if(b)a:{var d=a.split(".");a=d.length===1;var e=d[0],f;!a&&e in ea?f=ea:f=ca;for(e=0;e<d.length-1;e++){var g=d[e];if(!(g in f))break a;f=f[g]}d=d[d.length-1];c=da&&c==="es6"?f[d]:null;b=b(c);b!=null&&(a?aa(ea,d,{configurable:!0,writable:!0,value:b}):b!==c&&(fa[d]===void 0&&(a=Math.random()*1E9>>>0,fa[d]=da?ca.Symbol(d):"$jscp$"+a+"$"+d),aa(f,fa[d],{configurable:!0,writable:!0,value:b})))}}function q(a){return a} 
ia("Symbol.dispose",function(a){return a?a:Symbol("Symbol.dispose")},"es_next");/* 
 
 Copyright The Closure Library Authors. 
 SPDX-License-Identifier: Apache-2.0 
*/ 
var t=this||self;function ja(a){var b=typeof a;return b=="object"&&a!=null||b=="function"}function ka(a,b,c){return a.call.apply(a.bind,arguments)}function la(a,b,c){if(!a)throw Error();if(arguments.length>2){var d=Array.prototype.slice.call(arguments,2);return function(){var e=Array.prototype.slice.call(arguments);Array.prototype.unshift.apply(e,d);return a.apply(b,e)}}return function(){return a.apply(b,arguments)}} 
function ma(a,b,c){ma=Function.prototype.bind&&Function.prototype.bind.toString().indexOf("native code")!=-1?ka:la;return ma.apply(null,arguments)}function na(a,b){var c=Array.prototype.slice.call(arguments,1);return function(){var d=c.slice();d.push.apply(d,arguments);return a.apply(this,d)}}function oa(a,b){a=a.split(".");for(var c=t||t,d;a.length&&(d=a.shift());)a.length||b===void 0?c[d]&&c[d]!==Object.prototype[d]?c=c[d]:c=c[d]={}:c[d]=b} 
function pa(a,b){function c(){}c.prototype=b.prototype;a.ca=b.prototype;a.prototype=new c;a.prototype.constructor=a;a.Ab=function(d,e,f){for(var g=Array(arguments.length-2),h=2;h<arguments.length;h++)g[h-2]=arguments[h];return b.prototype[e].apply(d,g)}};var qa;function ra(a){t.setTimeout(()=>{throw a;},0)};var sa,ta;a:{for(var ua=["CLOSURE_FLAGS"],va=t,wa=0;wa<ua.length;wa++)if(va=va[ua[wa]],va==null){ta=null;break a}ta=va}var xa=ta&&ta[610401301];sa=xa!=null?xa:!1;function ya(){var a=t.navigator;return a&&(a=a.userAgent)?a:""}var u;const za=t.navigator;u=za?za.userAgentData||null:null;function Aa(){if(!sa||!u)return!1;for(let a=0;a<u.brands.length;a++){const {brand:b}=u.brands[a];if(b&&b.indexOf("Chromium")!=-1)return!0}return!1}function v(a){return ya().indexOf(a)!=-1};function Ba(){return sa?!!u&&u.brands.length>0:!1}function Ca(){return Ba()?Aa():(v("Chrome")||v("CriOS"))&&!(Ba()?0:v("Edge"))||v("Silk")};function Da(a,b){if(typeof a==="string")return typeof b!=="string"||b.length!=1?-1:a.indexOf(b,0);for(let c=0;c<a.length;c++)if(c in a&&a[c]===b)return c;return-1}function Ea(a,b){const c=a.length,d=typeof a==="string"?a.split(""):a;for(let e=0;e<c;e++)e in d&&b.call(void 0,d[e],e,a)}function Fa(a,b){var c=a.length;const d=typeof a==="string"?a.split(""):a;for(--c;c>=0;--c)c in d&&b.call(void 0,d[c],c,a)}function Ga(a,b){b=Da(a,b);let c;(c=b>=0)&&Array.prototype.splice.call(a,b,1);return c} 
function Ha(a,b){let c=0;Fa(a,function(d,e){b.call(void 0,d,e,a)&&Array.prototype.splice.call(a,e,1).length==1&&c++})};function Ia(a){Ia[" "](a);return a}Ia[" "]=function(){};var Ka=v("Gecko")&&!(ya().toLowerCase().indexOf("webkit")!=-1&&!v("Edge"))&&!(v("Trident")||v("MSIE"))&&!v("Edge"),La=ya().toLowerCase().indexOf("webkit")!=-1&&!v("Edge");function Ma(a,b){a.__closure__error__context__984382||(a.__closure__error__context__984382={});a.__closure__error__context__984382.severity=b};let Na=void 0,Oa;function Pa(a){if(Oa)throw Error("");Oa=b=>{t.setTimeout(()=>{a(b)},0)}}function Qa(a){if(Oa)try{Oa(a)}catch(b){throw b.cause=a,b;}}function Ra(a){a=Error(a);Ma(a,"warning");Qa(a);return a};function Sa(a,b=!1){return b&&Symbol.for&&a?Symbol.for(a):a!=null?Symbol(a):Symbol()}var Ta=Sa(),Ua=Sa(),Va=Sa(),Wa=Sa("m_m",!0);const w=Sa("jas",!0);var Xa;const Ya=[];Ya[w]=7;Xa=Object.freeze(Ya);var Za={};function $a(a,b){return b===void 0?a.g!==ab&&!!(2&(a.G[w]|0)):!!(2&b)&&a.g!==ab}const ab={};function bb(a){var b=cb;if(!x(a))throw b=(typeof b==="function"?b():b)?.concat("\n")??"",Error(b+String(a));}function db(a){a.Db=!0;return a}let cb=void 0;var eb=db(a=>typeof a==="number"),x=db(a=>typeof a==="string"),fb=db(a=>Array.isArray(a));function gb(){return db(a=>fb(a)?a.every(b=>eb(b)):!1)};function y(a){if(x(a)){if(!/^\s*(?:-?[1-9]\d*|0)?\s*$/.test(a))throw Error(String(a));}else if(eb(a)&&!Number.isSafeInteger(a))throw Error(String(a));return BigInt(a)}var jb=db(a=>a>=hb&&a<=ib);const hb=BigInt(Number.MIN_SAFE_INTEGER),ib=BigInt(Number.MAX_SAFE_INTEGER);let kb=0,lb=0;function mb(a){const b=a>>>0;kb=b;lb=(a-b)/4294967296>>>0}function nb(a){if(a<0){mb(-a);a=kb;var b=lb;b=~b;a?a=~a+1:b+=1;const [c,d]=[a,b];kb=c>>>0;lb=d>>>0}else mb(a)}function ob(a,b){b>>>=0;a>>>=0;var c;b<=2097151?c=""+(4294967296*b+a):c=""+(BigInt(b)<<BigInt(32)|BigInt(a));return c}function qb(){var a=kb,b=lb,c;b&2147483648?c=""+(BigInt(b|0)<<BigInt(32)|BigInt(a>>>0)):c=ob(a,b);return c};const rb=typeof BigInt==="function"?BigInt.asIntN:void 0,sb=Number.isSafeInteger,tb=Number.isFinite,ub=Math.trunc,vb=/^-?([1-9][0-9]*|0)(\.[0-9]+)?$/;function wb(a){switch(typeof a){case "bigint":return!0;case "number":return tb(a);case "string":return vb.test(a);default:return!1}}function xb(a){if(!tb(a))throw Ra("enum");return a|0}function yb(a){return a==null?a:tb(a)?a|0:void 0}function zb(a){if(typeof a!=="number")throw Ra("int32");if(!tb(a))throw Ra("int32");return a|0} 
function Ab(a){if(a!=null)a:{if(!wb(a))throw Ra("int64");switch(typeof a){case "string":var b=ub(Number(a));sb(b)?a=String(b):(b=a.indexOf("."),b!==-1&&(a=a.substring(0,b)),Bb(a)||(a.length<16?nb(Number(a)):(a=BigInt(a),kb=Number(a&BigInt(4294967295))>>>0,lb=Number(a>>BigInt(32)&BigInt(4294967295))),a=qb()));break a;case "bigint":a=y(rb(64,a));break a;default:a=Cb(a)}}return a} 
function Bb(a){const b=a.length;return a[0]==="-"?b<20?!0:b===20&&Number(a.substring(0,7))>-922337:b<19?!0:b===19&&Number(a.substring(0,6))<922337}function Cb(a){a=ub(a);if(!sb(a)){nb(a);var b=kb,c=lb;if(a=c&2147483648)b=~b+1>>>0,c=~c>>>0,b==0&&(c=c+1>>>0);const d=c*4294967296+(b>>>0);b=Number.isSafeInteger(d)?d:ob(b,c);a=typeof b==="number"?a?-b:b:a?"-"+b:b}return a}function Db(a){if(typeof a!=="string")throw Error();return a} 
function Eb(a){if(a!=null&&typeof a!=="string")throw Error();return a};function Fb(a){return a};function Gb(a,b,c,d){var e=d!==void 0;d=!!d;const f=[];var g=a.length;let h,k=4294967295,m=!1;const n=!!(b&64),p=n?b&128?0:-1:void 0;b&1||(h=g&&a[g-1],h!=null&&typeof h==="object"&&h.constructor===Object?(g--,k=g):h=void 0,!n||b&128||e||(m=!0,k=(Hb??Fb)(k-p,p,a,h)+p));b=void 0;for(e=0;e<g;e++){let r=a[e];if(r!=null&&(r=c(r,d))!=null)if(n&&e>=k){const A=e-p;(b??(b={}))[A]=r}else f[e]=r}if(h)for(let r in h){if(!Object.prototype.hasOwnProperty.call(h,r))continue;a=h[r];if(a==null||(a=c(a,d))==null)continue; 
g=+r;let A;n&&!Number.isNaN(g)&&(A=g+p)<k?f[A]=a:(b??(b={}))[r]=a}b&&(m?f.push(b):f[k]=b);return f}function Ib(a){switch(typeof a){case "number":return Number.isFinite(a)?a:""+a;case "bigint":return jb(a)?Number(a):""+a;case "boolean":return a?1:0;case "object":if(Array.isArray(a)){const b=a[w]|0;return a.length===0&&b&1?void 0:Gb(a,b,Ib)}if(a[Wa]===Za)return Jb(a);return}return a}let Hb;function Jb(a){a=a.G;return Gb(a,a[w]|0,Ib)};function Kb(a){if(a==null){var b=32;a=[]}else{if(!Array.isArray(a))throw Error("narr");b=a[w]|0;4096&b&&!(2&b)&&Lb();if(b&256)throw Error("farr");if(b&64)return b&4096||(a[w]=b|4096),a;var c=a;b|=64;var d=c.length;if(d){var e=d-1;d=c[e];if(d!=null&&typeof d==="object"&&d.constructor===Object){const f=b&128?0:-1;e-=f;if(e>=1024)throw Error("pvtlmt");for(const g in d){if(!Object.prototype.hasOwnProperty.call(d,g))continue;const h=+g;if(h<e)c[h+f]=d[g],delete d[g];else break}b=b&-16760833|(e&1023)<< 
14}}}a[w]=b|4160;return a}function Lb(){if(Va!=null){var a=Na??(Na={});var b=a[Va]||0;b>=5||(a[Va]=b+1,a=Error(),Ma(a,"incident"),Oa?Qa(a):ra(a))}};function Mb(a,b){if(typeof a!=="object")return a;if(Array.isArray(a)){var c=a[w]|0;a.length===0&&c&1?a=void 0:c&2||(!b||8192&c||16&c?a=Nb(a,c,!1,b&&!(c&16)):(a[w]|=34,c&4&&Object.freeze(a)));return a}if(a[Wa]===Za)return b=a.G,c=b[w]|0,$a(a,c)?a:Nb(b,c)}function Nb(a,b,c,d){d??(d=!!(34&b));a=Gb(a,b,Mb,d);d=32;c||(d|=2);b=b&16761025|d;a[w]=b;return a}function Ob(a){const b=a.G,c=b[w]|0;return $a(a,c)?new a.constructor(Nb(b,c,!0)):a} 
function Pb(a){if(a.g!==ab)return!1;let b=a.G;b=Nb(b,b[w]|0,!0);a.G=b;a.g=void 0;a.o=void 0;return!0}function Qb(a){if(!Pb(a)&&$a(a,a.G[w]|0))throw Error();};const Rb=y(0);function Sb(a,b){a=Tb(a.G,b);if(a!==null)return a}function Tb(a,b,c,d){if(b===-1)return null;const e=b+(c?0:-1),f=a.length-1;let g,h;if(!(f<1+(c?0:-1))){if(e>=f)if(g=a[f],g!=null&&typeof g==="object"&&g.constructor===Object)c=g[b],h=!0;else if(e===f)c=g;else return;else c=a[e];if(d&&c!=null){d=d(c);if(d==null)return d;if(!Object.is(d,c))return h?g[b]=d:a[e]=d,d}return c}}function Ub(a,b,c){Qb(a);const d=a.G;z(d,d[w]|0,b,c);return a} 
function z(a,b,c,d){const e=c+-1;var f=a.length-1;if(f>=0&&e>=f){const g=a[f];if(g!=null&&typeof g==="object"&&g.constructor===Object)return g[c]=d,b}if(e<=f)return a[e]=d,b;d!==void 0&&(f=(b??(b=a[w]|0))>>14&1023||536870912,c>=f?d!=null&&(a[f+-1]={[c]:d}):a[e]=d);return b}function Vb(a){return!!(2&a)&&!!(4&a)||!!(256&a)} 
function Wb(a,b,c,d){Qb(a);const e=a.G;let f=e[w]|0;if(c==null)return z(e,f,b),a;let g=c===Xa?7:c[w]|0,h=g;var k=Vb(g);let m=k||Object.isFrozen(c);k||(g=0);m||(c=[...c],h=0,g=Xb(g,f),m=!1);g|=5;k=(4&g?512&g?512:1024&g?1024:0:void 0)??0;for(let n=0;n<c.length;n++){const p=c[n],r=d(p,k);Object.is(p,r)||(m&&(c=[...c],h=0,g=Xb(g,f),m=!1),c[n]=r)}g!==h&&(m&&(c=[...c],g=Xb(g,f)),c[w]=g);z(e,f,b,c);return a} 
function C(a,b,c,d){Qb(a);const e=a.G;z(e,e[w]|0,b,(d==="0"?Number(c)===0:c===d)?void 0:c);return a}function Yb(a,b,c,d){let e=a.get(d);if(e!=null)return e;e=0;for(let f=0;f<d.length;f++){const g=d[f];Tb(b,g)!=null&&(e!==0&&(c=z(b,c,e)),e=g)}a.set(d,e);return e}function Zb(a,b){var c=$b;a=Tb(a,1,void 0,d=>{if(d==null||typeof d!=="object"||d[Wa]!==Za)if(Array.isArray(d)){const e=d[w]|0;let f;f=e|b&32;f|=b&2;f!==e&&(d[w]=f);d=new c(d)}else d=void 0;return d});if(a!=null)return a} 
function ac(a){a==null&&(a=void 0);return a}function bc(a,b,c){c=ac(c);Ub(a,b,c);return a}function cc(a,b,c,d){d=ac(d);a:{Qb(a);const g=a.G;var e=g[w]|0;if(d==null){var f=g[Ua]??(g[Ua]=new Map);if(Yb(f,g,e,c)===b)f.set(c,0);else break a}else{f=g;const h=f[Ua]??(f[Ua]=new Map),k=Yb(h,f,e,c);k!==b&&(k&&(e=z(f,e,k)),h.set(c,b))}z(g,e,b,d)}return a}function Xb(a,b){return a=(2&b?a|2:a&-3)&-273}function dc(a,b){a=Sb(a,b);return(a==null||typeof a==="string"?a:void 0)??""} 
function D(a,b,c){return C(a,b,Eb(c),"")}function ec(a,b,c){return C(a,b,c==null?c:xb(c),0)};function fc(a){const b=a.G,c=b[w]|0;return $a(a,c)?a:new a.constructor(Nb(b,c))}var E=class{constructor(a){this.G=Kb(a)}toJSON(){return Jb(this)}};E.prototype[Wa]=Za;function ic(a){return()=>{var b;if(!(b=a[Ta])){const c=new a;b=c.G;b[w]|=34;b=a[Ta]=c}return b}};let jc,kc=64;function lc(){try{return jc??(jc=new Uint32Array(64)),kc>=64&&(crypto.getRandomValues(jc),kc=0),jc[kc++]}catch(a){return Math.floor(Math.random()*2**32)}};function mc(a){if(!eb(a.goog_pvsid))try{const b=lc()+(lc()&2**21-1)*2**32;Object.defineProperty(a,"goog_pvsid",{value:b,configurable:!1})}catch(b){}return Number(a.goog_pvsid)||-1};function nc(a){let b=!1,c;return function(){b||(c=a(),b=!0);return c}}function oc(a){let b=0;return function(c){t.clearTimeout(b);const d=arguments;b=t.setTimeout(function(){a.apply(void 0,d)},100)}};function pc(){return sa&&u?u.mobile:!qc()&&(v("iPod")||v("iPhone")||v("Android")||v("IEMobile"))}function qc(){return sa&&u?!u.mobile&&(v("iPad")||v("Android")||v("Silk")):v("iPad")||v("Android")&&!v("Mobile")||v("Silk")};function rc(a,b,c){for(const d in a)b.call(c,a[d],d,a)}function sc(a){let b=0;for(const c in a)b++}function tc(a){const b={};for(const c in a)b[c]=a[c];return b}const uc="constructor hasOwnProperty isPrototypeOf propertyIsEnumerable toLocaleString toString valueOf".split(" ");function vc(a,b){let c,d;for(let e=1;e<arguments.length;e++){d=arguments[e];for(c in d)a[c]=d[c];for(let f=0;f<uc.length;f++)c=uc[f],Object.prototype.hasOwnProperty.call(d,c)&&(a[c]=d[c])}};/* 
 
 Copyright Google LLC 
 SPDX-License-Identifier: Apache-2.0 
*/ 
let wc=globalThis.trustedTypes,xc;function yc(){let a=null;if(!wc)return a;try{const b=c=>c;a=wc.createPolicy("goog#html",{createHTML:b,createScript:b,createScriptURL:b})}catch(b){}return a}function zc(){xc===void 0&&(xc=yc());return xc};var Ac=class{constructor(a){this.g=a}toString(){return this.g+""}};function Bc(a){const b=zc();a=b?b.createScriptURL(a):a;return new Ac(a)}function Cc(a){if(a instanceof Ac)return a.g;throw Error("");};var Dc=class{constructor(a){this.g=a}toString(){return this.g+""}};function Ec(a){const b=zc();a=b?b.createHTML(a):a;return new Dc(a)}function Fc(a){if(a instanceof Dc)return a.g;throw Error("");};function Gc(a=document){a=a.querySelector?.("script[nonce]");return a==null?"":a.nonce||a.getAttribute("nonce")||""};function Hc(a,b){a.src=Cc(b);(b=Gc(a.ownerDocument))&&a.setAttribute("nonce",b)};var Ic=class{constructor(a){this.g=a}toString(){return this.g}};function Jc(a){if(a instanceof Ic)return a.g;throw Error("");};function Kc(a){return String(a).replace(/\-([a-z])/g,function(b,c){return c.toUpperCase()})}function Lc(a){return a.replace(RegExp("(^|[\\s]+)([a-z])","g"),function(b,c,d){return c+d.toUpperCase()})};var Mc=RegExp("^(?:([^:/?#.]+):)?(?://(?:([^\\\\/?#]*)@)?([^\\\\/?#]*?)(?::([0-9]+))?(?=[\\\\/?#]|$))?([^?#]+)?(?:\\?([^#]*))?(?:#([\\s\\S]*))?$");function Nc(a){return new Ic(a[0])};function Oc(a){return a instanceof Dc?a:Ec(String(a).replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;"))};function Pc(a,...b){if(b.length===0)return Bc(a[0]);let c=a[0];for(let d=0;d<b.length;d++)c+=encodeURIComponent(b[d])+a[d+1];return Bc(c)}function Qc(a,b,c,d){function e(g,h){g!=null&&(Array.isArray(g)?g.forEach(k=>e(k,h)):(b+=f+encodeURIComponent(h)+"="+encodeURIComponent(g),f="&"))}let f=b.length?"&":"?";d.constructor===Object&&(d=Object.entries(d));Array.isArray(d)?d.forEach(g=>e(g[1],g[0])):d.forEach(e);return Bc(a+b+c)};function Rc(a){try{var b;if(b=!!a&&a.location.href!=null)a:{try{Ia(a.foo);b=!0;break a}catch(c){}b=!1}return b}catch{return!1}}function Sc(){if(!globalThis.crypto)return Math.random();try{const a=new Uint32Array(1);globalThis.crypto.getRandomValues(a);return a[0]/65536/65536}catch{return Math.random()}}function Tc(a,b){if(a)for(const c in a)Object.prototype.hasOwnProperty.call(a,c)&&b(a[c],c,a)}function Uc(a){const b=[];Tc(a,function(c){b.push(c)});return b} 
var Vc=nc(()=>pc()?2:qc()?1:0),Wc=(a,b)=>{Tc(b,(c,d)=>{a.style.setProperty(d,c,"important")})},Yc=(a,b)=>{if("length"in a.style){a=a.style;const c=a.length;for(let d=0;d<c;d++){const e=a[d];b(a[e],e,a)}}else a=Xc(a.style.cssText),Tc(a,b)},Xc=a=>{const b={};if(a){const c=/\s*:\s*/;Ea((a||"").split(/\s*;\s*/),d=>{if(d){var e=d.split(c);d=e[0];e=e[1];d&&e&&(b[d.toLowerCase()]=e)}})}return b},Zc=a=>{const b=/!\s*important/i;Yc(a,(c,d)=>{b.test(c)?b.test(c):a.style.setProperty(d,c,"important")})}; 
let $c=[];const ad=()=>{const a=$c;$c=[];for(const b of a)try{b()}catch{}};var bd=a=>{$c.push(a);$c.length==1&&(window.Promise?Promise.resolve().then(ad):window.setImmediate?setImmediate(ad):setTimeout(ad,0))},cd=(a,b)=>new Promise(c=>{setTimeout(()=>void c(b),a)});function dd(a,b=document){return b.createElement(String(a).toLowerCase())} 
var ed=a=>{var b=Rc(a.top)?a.top:null;if(!b)return 1;a=Vc()===0;const c=!!b.document.querySelector('meta[name=viewport][content*="width=device-width"]'),d=b.innerWidth;b=b.outerWidth;if(d===0)return 1;const e=Math.round((b/d+Number.EPSILON)*100)/100;return e===1?1:a||c?e:Math.round((b/d/.4+Number.EPSILON)*100)/100};function F(a,b,c){typeof a.addEventListener==="function"&&a.addEventListener(b,c,!1)}function fd(a,b,c){return typeof a.removeEventListener==="function"?(a.removeEventListener(b,c,!1),!0):!1};function gd(a,b,c=null,d=!1,e=!1){hd(a,b,c,d,e)}function hd(a,b,c,d,e=!1){a.google_image_requests||(a.google_image_requests=[]);const f=dd("IMG",a.document);if(c||d){const g=h=>{c&&c(h);d&&Ga(a.google_image_requests,f);fd(f,"load",g);fd(f,"error",g)};F(f,"load",g);F(f,"error",g)}e&&(f.attributionSrc="");f.src=b;a.google_image_requests.push(f)} 
function id(a){let b="https://pagead2.googlesyndication.com/pagead/gen_204?id=rcs_internal";Tc(a,(c,d)=>{if(c||c===0)b+=`&${d}=${encodeURIComponent(String(c))}`});jd(b)}function jd(a){var b=window;b.fetch?b.fetch(a,{keepalive:!0,credentials:"include",redirect:"follow",method:"get",mode:"no-cors"}):gd(b,a,void 0,!1,!1)};function G(a,b){this.width=a;this.height=b}l=G.prototype;l.aspectRatio=function(){return this.width/this.height};l.isEmpty=function(){return!(this.width*this.height)};l.ceil=function(){this.width=Math.ceil(this.width);this.height=Math.ceil(this.height);return this};l.floor=function(){this.width=Math.floor(this.width);this.height=Math.floor(this.height);return this};l.round=function(){this.width=Math.round(this.width);this.height=Math.round(this.height);return this}; 
l.scale=function(a,b){this.width*=a;this.height*=typeof b==="number"?b:a;return this};function kd(a=t){let b=a.context||a.AMP_CONTEXT_DATA;if(!b)try{b=a.parent.context||a.parent.AMP_CONTEXT_DATA}catch{}return b?.pageViewId&&b?.canonicalUrl?b:null}function ld(){var a=kd();return a?Rc(a.master)?a.master:null:null};function md(a,b){b=String(b);a.contentType==="application/xhtml+xml"&&(b=b.toLowerCase());return a.createElement(b)}function nd(a){return a&&a.parentNode?a.parentNode.removeChild(a):null}function od(){this.g=t.document||document}od.prototype.i=function(a){var b=this.g;return typeof a==="string"?b.getElementById(a):a};od.prototype.o=od.prototype.i;function H(a,b,c){if(typeof b==="string")(b=pd(a,b))&&(a.style[b]=c);else for(const e in b){c=a;var d=b[e];const f=pd(c,e);f&&(c.style[f]=d)}}var qd={};function pd(a,b){let c=qd[b];if(!c){var d=Kc(b);c=d;a.style[d]===void 0&&(d=(La?"Webkit":Ka?"Moz":null)+Lc(d),a.style[d]!==void 0&&(c=d));qd[b]=c}return c};var rd=class{constructor(a,b){this.error=a;this.meta={};this.context=b.context;this.msg=b.message||"";this.id=b.id||"jserror"}};function sd(a){return new rd(a,{message:td(a)})}function td(a){let b=a.toString();a.name&&b.indexOf(a.name)==-1&&(b+=": "+a.name);a.message&&b.indexOf(a.message)==-1&&(b+=": "+a.message);if(a.stack)a:{a=a.stack;var c=b;try{a.indexOf(c)==-1&&(a=c+"\n"+a);let d;for(;a!=d;)d=a,a=a.replace(RegExp("((https?:/..*/)[^/:]*:\\d+(?:.|\n)*)\\2"),"$1");b=a.replace(RegExp("\n *","g"),"\n");break a}catch(d){b=c;break a}b=void 0}return b};const ud=RegExp("^https?://(\\w|-)+\\.cdn\\.ampproject\\.(net|org)(\\?|/|$)");var vd=class{constructor(a,b){this.g=a;this.i=b}},wd=class{constructor(a,b,c){this.url=a;this.D=b;this.g=!!c;this.depth=null}};let xd=null;function yd(){const a=t.performance;return a&&a.now&&a.timing?Math.floor(a.now()+a.timing.navigationStart):Date.now()}function zd(){const a=t.performance;return a&&a.now?a.now():null};var Ad=class{constructor(a,b){var c=zd()||yd();this.label=a;this.type=b;this.value=c;this.duration=0;this.taskId=this.slotId=void 0;this.uniqueId=Math.random()}};const I=t.performance,Bd=!!(I&&I.mark&&I.measure&&I.clearMarks),Cd=nc(()=>{var a;if(a=Bd){var b;a=window;if(xd===null){xd="";try{let c="";try{c=a.top.location.hash}catch(d){c=a.location.hash}c&&(xd=(b=c.match(/\bdeid=([\d,]+)/))?b[1]:"")}catch(c){}}b=xd;a=!!b.indexOf&&b.indexOf("1337")>=0}return a});function Dd(a){a&&I&&Cd()&&(I.clearMarks(`goog_${a.label}_${a.uniqueId}_start`),I.clearMarks(`goog_${a.label}_${a.uniqueId}_end`))} 
function Ed(a){a.g=!1;a.i!==a.o.google_js_reporting_queue&&(Cd()&&Ea(a.i,Dd),a.i.length=0)} 
var Fd=class{constructor(a){this.i=[];this.o=a||t;let b=null;a&&(a.google_js_reporting_queue=a.google_js_reporting_queue||[],this.i=a.google_js_reporting_queue,b=a.google_measure_js_timing);this.g=Cd()||(b!=null?b:Math.random()<1)}start(a,b){if(!this.g)return null;a=new Ad(a,b);b=`goog_${a.label}_${a.uniqueId}_start`;I&&Cd()&&I.mark(b);return a}end(a){if(this.g&&typeof a.value==="number"){a.duration=(zd()||yd())-a.value;var b=`goog_${a.label}_${a.uniqueId}_end`;I&&Cd()&&I.mark(b);!this.g||this.i.length> 
2048||this.i.push(a)}}};function Gd(a,b){const c={};c[a]=b;return[c]}function Hd(a,b,c,d,e){const f=[];Tc(a,(g,h)=>{(g=Id(g,b,c,d,e))&&f.push(`${h}=${g}`)});return f.join(b)} 
function Id(a,b,c,d,e){if(a==null)return"";b=b||"&";c=c||",$";typeof c==="string"&&(c=c.split(""));if(a instanceof Array){if(d||(d=0),d<c.length){const f=[];for(let g=0;g<a.length;g++)f.push(Id(a[g],b,c,d+1,e));return f.join(c[d])}}else if(typeof a==="object")return e||(e=0),e<2?encodeURIComponent(Hd(a,b,c,d,e+1)):"...";return encodeURIComponent(String(a))}function Jd(a){let b=1;for(const c in a.i)c.length>b&&(b=c.length);return 3997-b-a.o.length-1} 
function Kd(a,b,c){b="https://"+b+c;let d=Jd(a)-c.length;if(d<0)return"";a.g.sort((f,g)=>f-g);c=null;let e="";for(let f=0;f<a.g.length;f++){const g=a.g[f],h=a.i[g];for(let k=0;k<h.length;k++){if(!d){c=c==null?g:c;break}let m=Hd(h[k],a.o,",$");if(m){m=e+m;if(d>=m.length){d-=m.length;b+=m;e=a.o;break}c=c==null?g:c}}}a="";c!=null&&(a=`${e}${"trn"}=${c}`);return b+a}var Ld=class{constructor(){this.o="&";this.i={};this.j=0;this.g=[]}};var Od=class{constructor(a=null){this.u=Md;this.i=a;this.o=null;this.l=!1;this.C=this.V}j(a){this.o=a}H(a){this.l=a}g(a,b){let c,d;try{this.i&&this.i.g?(d=this.i.start(a.toString(),3),c=b(),this.i.end(d)):c=b()}catch(e){b=!0;try{Dd(d),b=this.C(a,sd(e),void 0,void 0)}catch(f){this.V(217,f)}if(b)window.console?.error?.(e);else throw e;}return c}B(a,b){return(...c)=>this.g(a,()=>b.apply(void 0,c))}V(a,b,c,d,e){e=e||"jserror";let f=void 0;try{const V=new Ld;var g=V;g.g.push(1);g.i[1]=Gd("context",a); 
b.error&&b.meta&&b.id||(b=sd(b));g=b;if(g.msg){b=V;var h=g.msg.substring(0,512);b.g.push(2);b.i[2]=Gd("msg",h)}var k=g.meta||{};h=k;if(this.o)try{this.o(h)}catch(B){}if(d)try{d(h)}catch(B){}d=V;k=[k];d.g.push(3);d.i[3]=k;var m;if(!(m=r)){d=t;k=[];h=null;do{var n=d;if(Rc(n)){var p=n.location.href;h=n.document&&n.document.referrer||null}else p=h,h=null;k.push(new wd(p||"",n));try{d=n.parent}catch(B){d=null}}while(d&&n!==d);for(let B=0,Zd=k.length-1;B<=Zd;++B)k[B].depth=Zd-B;n=t;if(n.location&&n.location.ancestorOrigins&& 
n.location.ancestorOrigins.length===k.length-1)for(p=1;p<k.length;++p){const B=k[p];B.url||(B.url=n.location.ancestorOrigins[p-1]||"",B.g=!0)}m=k}var r=m;let pb=new wd(t.location.href,t,!1);m=null;const gc=r.length-1;for(n=gc;n>=0;--n){var A=r[n];!m&&ud.test(A.url)&&(m=A);if(A.url&&!A.g){pb=A;break}}A=null;const pg=r.length&&r[gc].url;pb.depth!==0&&pg&&(A=r[gc]);f=new vd(pb,A);if(f.i){r=V;var ba=f.i.url||"";r.g.push(4);r.i[4]=Gd("top",ba)}var hc={url:f.g.url||""};if(f.g.url){const B=f.g.url.match(Mc); 
var Ja=B[1],$d=B[3],ae=B[4];ba="";Ja&&(ba+=Ja+":");$d&&(ba+="//",ba+=$d,ae&&(ba+=":"+ae));var be=ba}else be="";Ja=V;hc=[hc,{url:be}];Ja.g.push(5);Ja.i[5]=hc;Nd(this.u,e,V,this.l,c)}catch(V){try{Nd(this.u,e,{context:"ecmserr",rctx:a,msg:td(V),url:f?.g.url??""},this.l,c)}catch(pb){}}return!0}};var Pd=class extends E{};function Qd(a,b){try{const c=d=>[{[d.Ma]:d.Ja}];return JSON.stringify([a.filter(d=>d.ya).map(c),Jb(b),a.filter(d=>!d.ya).map(c)])}catch(c){return Rd(c,b),""}}function Rd(a,b){try{id({m:td(a instanceof Error?a:Error(String(a))),b:(yb(Sb(b,1))??0)||null,v:dc(b,2)||null})}catch(c){}}var Sd=class{constructor(a,b){var c=new Pd;a=ec(c,1,a);b=D(a,2,b);this.o=fc(b)}};var Td=class extends E{N(a){return ec(this,2,a)}};var Ud=class extends E{},Vd=[4,5,6,8,9,10,11,12,13,14,15,16,17];var Wd=class extends E{};function Xd(){var a=Yd();a=Ob(a);return D(a,1,ce())}var de=class extends E{};var ee=class extends E{};var fe=class extends E{getTagSessionCorrelator(){var a=Sb(this,1),b=typeof a;a!=null&&(b==="bigint"?a=y(rb(64,a)):wb(a)?b==="string"?(b=ub(Number(a)),sb(b)?a=y(b):(b=a.indexOf("."),b!==-1&&(a=a.substring(0,b)),a=y(rb(64,BigInt(a))))):sb(a)?a=y(Cb(a)):(a=ub(a),sb(a)?a=String(a):(b=String(a),Bb(b)?a=b:(nb(a),a=qb())),a=y(a)):a=void 0);return a??Rb}};var ge=class extends E{},he=[1,7],ie=[4,6,8];class je extends Sd{constructor(){super(...arguments)}}function ke(a,...b){le(a,...b.map(c=>({ya:!0,Ma:3,Ja:Jb(c)})))}function me(a,...b){le(a,...b.map(c=>({ya:!0,Ma:7,Ja:Jb(c)})))}var ne=class extends je{};function oe(a,b){globalThis.fetch(a,{method:"POST",body:b,keepalive:b.length<65536,credentials:"omit",mode:"no-cors",redirect:"follow"}).catch(()=>{})};function le(a,...b){try{a.B&&Qd(a.g.concat(b),a.o).length>=65536&&pe(a),a.j&&!a.l&&(a.l=!0,qe(a.j,()=>{pe(a)})),a.g.push(...b),a.g.length>=a.u&&pe(a),a.g.length&&a.i===null&&(a.i=setTimeout(()=>{pe(a)},a.C))}catch(c){Rd(c,a.o)}}function pe(a){a.i!==null&&(clearTimeout(a.i),a.i=null);if(a.g.length){var b=Qd(a.g,a.o);a.H("https://pagead2.googlesyndication.com/pagead/ping?e=1",b);a.g=[]}} 
var re=class extends ne{constructor(a,b,c,d){super(2,ce());this.H=oe;this.C=a;this.u=b;this.B=c;this.j=d;this.g=[];this.i=null;this.l=!1}},se=class extends re{constructor(a=1E3,b=100,c=!1,d){super(a,b,c&&!0,d)}};var J=a=>{var b="va";if(a.va&&a.hasOwnProperty(b))return a.va;b=new a;return a.va=b};function te(a,b,c){return b[a]||c};function ue(a,b){a.g=()=>te(3,b,()=>[])(1)}class ve{g(){return[]}};function Nd(a,b,c,d=!1,e){if((d?a.g:Math.random())<(e||.01))try{let f;c instanceof Ld?f=c:(f=new Ld,Tc(c,(h,k)=>{var m=f;const n=m.j++;h=Gd(k,h);m.g.push(n);m.i[n]=h}));const g=Kd(f,a.domain,a.path+b+"&");g&&gd(t,g)}catch(f){}}function we(a,b){b>=0&&b<=1&&(a.g=b)}var xe=class{constructor(){this.domain="pagead2.googlesyndication.com";this.path="/pagead/gen_204?id=";this.g=Math.random()}};let Md,ye;const ze=new Fd(window);(function(a){Md=a??new xe;typeof window.google_srt!=="number"&&(window.google_srt=Math.random());we(Md,window.google_srt);ye=new Od(ze);ye.j(()=>{});ye.H(!0);window.document.readyState==="complete"?window.google_measure_js_timing||Ed(ze):ze.g&&F(window,"load",()=>{window.google_measure_js_timing||Ed(ze)})})();function ce(){return"m202505060101"};var Ae=ic(Wd);var Yd=ic(de);function Be(a,b){return b(a)?a:void 0} 
function Ce(a,b,c,d,e){c=c instanceof rd?c.error:c;var f=new ge;const g=new fe;try{var h=mc(window);C(g,1,Ab(h),"0")}catch(r){}try{var k=J(ve).g();Wb(g,2,k,zb)}catch(r){}try{D(g,3,window.document.URL)}catch(r){}h=bc(f,2,g);k=new ee;b=ec(k,1,b);try{var m=x(c?.name)?c.name:"Unknown error";D(b,2,m)}catch(r){}try{var n=x(c?.message)?c.message:`Caught ${c}`;D(b,3,n)}catch(r){}try{var p=x(c?.stack)?c.stack:Error().stack;p&&Wb(b,4,p.split(/\n\s*/),Db)}catch(r){}m=cc(h,1,he,b);if(e){n=0;switch(e.errSrc){case "LCC":n= 
1;break;case "PVC":n=2}p=Xd();b=Be(e.shv,x);p=D(p,2,b);n=ec(p,6,n);p=Ae();p=Ob(p);b=Be(e.es,gb());p=Wb(p,1,b,zb);p=fc(p);n=bc(n,4,p);p=Be(e.client,x);n=Ub(n,3,Eb(p));p=Be(e.slotname,x);n=D(n,7,p);e=Be(e.tag_origin,x);e=D(n,8,e);e=fc(e)}else e=fc(Xd());e=cc(m,6,ie,e);d=C(e,5,Ab(d??1),"0");ke(a,d)};var Ee=class{constructor(){this.g=De}};function De(){return{Sa:lc()+(lc()&2**21-1)*2**32,Pa:Number.MAX_SAFE_INTEGER}};var He=class{constructor(a=!1){var b=Fe;this.u=Ge;this.o=a;this.C=b;this.i=null;this.l=this.V}j(a){this.i=a}H(){}g(a,b){let c;try{c=b()}catch(d){b=this.o;try{b=this.l(a,sd(d),void 0,void 0)}catch(e){this.V(217,e)}if(b)window.console?.error?.(d);else throw d;}return c}B(a,b){return(...c)=>this.g(a,()=>b.apply(void 0,c))}V(a,b,c,d){try{const g=c===void 0?1/this.C:c===0?0:1/c;var e=(new Ee).g();if(g>0&&e.Sa*g<=e.Pa){var f=this.u;c={};if(this.i)try{this.i(c)}catch(h){}if(d)try{d(c)}catch(h){}Ce(f,a,b, 
g,c)}}catch(g){}return this.o}};let Ge,Ie,Je,Ke,Fe;const Le=new Fd(t);(function(a,b,c=!0){({Wa:Fe,Qa:Je}=Me());Ie=a||new xe;we(Ie,Je);Ge=b||new se(1E3);Ke=new He(c);t.document.readyState==="complete"?t.google_measure_js_timing||Ed(Le):Le.g&&F(t,"load",()=>{t.google_measure_js_timing||Ed(Le)})})();function Ne(a,b){Ke.g(a,b)}function K(a,b){return Ke.B(a,b)}function Me(){let a,b;typeof t.google_srt==="number"?(b=t.google_srt,a=t.google_srt===0?1:.01):(b=Math.random(),a=.01);return{Wa:a,Qa:b}};function Oe(){var a=kd(window);if(a){if(a){var b=a.pageViewId;a=a.clientId;typeof a==="string"&&(b+=a.replace(/\D/g,"").substring(0,6))}else b=null;return+b}for(a=b=window;b&&b!=b.parent;)b=b.parent,Rc(b)&&(a=b);b=a;a=b.google_global_correlator;a||(b.google_global_correlator=a=1+Math.floor(Math.random()*8796093022208));return a} 
function Pe(){if(Qe)return Qe;var a=ld()||window;const b=a.google_persistent_state_async;return b!=null&&typeof b=="object"&&b.S!=null&&typeof b.S=="object"?Qe=b:a.google_persistent_state_async=Qe=new Re}function Se(a,b,c){b=Te[b]||`google_ps_${b}`;a=a.S;const d=a[b];return d===void 0?(a[b]=c(),a[b]):d}function Ue(a){var b=Oe();return Se(a,7,()=>b)}function Ve(){var a=Pe();return Ue(a)}var Re=class{constructor(){this.S={}}},Qe=null;const Te={[8]:"google_prev_ad_formats_by_region",[9]:"google_prev_ad_slotnames_by_region"};function We(a){a&&typeof a.dispose=="function"&&a.dispose()};function L(){this.o=this.o;this.H=this.H}L.prototype.o=!1;L.prototype.dispose=function(){this.o||(this.o=!0,this.F())};L.prototype[ha(Symbol,"dispose")]=function(){this.dispose()};function M(a,b){a.o?b():(a.H||(a.H=[]),a.H.push(b))}L.prototype.F=function(){if(this.H)for(;this.H.length;)this.H.shift()()};[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce((a,b)=>a+b);[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce((a,b)=>a+b);[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce((a,b)=>a+b);[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce((a,b)=>a+b);[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce((a,b)=>a+b);[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce((a,b)=>a+b);sc({nb:0,mb:1,jb:2,eb:3,kb:4,fb:5,lb:6,hb:7,ib:8,bb:9,gb:10,ob:11});sc({qb:0,rb:1,pb:2});var Xe=class{constructor(a,b=!1){this.g=a;this.defaultValue=b}},N=class{constructor(a,b=0){this.g=a;this.defaultValue=b}};var Ye=new N(1130,100),Ze=new Xe(10024,!0),$e=new Xe(10025),af=new Xe(10018,!0),bf=new N(10023),cf=new N(1085,5),df=new N(63,30),ef=new N(1080,5),ff=new N(10019,5),gf=new N(1027,10),hf=new N(57,120),jf=new Xe(10017),kf=new N(1050,30),lf=new N(58,120),mf=new N(10021,1.5),nf=new Xe(10026),of=new Xe(10022),pf=new N(550718588,250);function qf(a){var b=new rf;Qb(b);var c=b.G,d=c[w]|0;var e=$a(b,d)?1:2;e===2&&Pb(b)&&(c=b.G,d=c[w]|0);b=Tb(c,1);var f=Array.isArray(b)?b:Xa,g=f===Xa?7:f[w]|0;b=g;2&d&&(b|=2);var h=b|1;b=4&h?!1:!0;if(b){4&h&&(f=[...f],g=0,h=Xb(h,d),d=z(c,d,1,f));let m=0,n=0;for(;m<f.length;m++){const p=yb(f[m]);p!=null&&(f[n++]=p)}n<m&&(f.length=n);h=(h|4)&-513;h&=-1025;h&=-8193}h!==g&&(f[w]=h,2&h&&Object.freeze(f));h=g=h;e===1||(e!==4?0:2&g||!(16&g)&&32&d)?Vb(g)||(g|=!f.length||b&&!(8192&g)||32&d&&!(8192&g||16&g)? 
2:256,g!==h&&(f[w]=g),Object.freeze(f)):(e===2&&Vb(g)&&(f=[...f],h=0,g=Xb(g,d),z(c,d,1,f)),Vb(g)||g!==h&&(f[w]=g));e=f;if(Array.isArray(a)){var k=a.length;for(d=0;d<k;d++)e.push(xb(a[d]))}else for(k of a)e.push(xb(k))}var rf=class extends E{};var sf={},tf={},uf={};function O(){throw Error("Do not instantiate directly");}O.prototype.Ga=null;O.prototype.getContent=function(){return this.content};O.prototype.toString=function(){return this.content};O.prototype.za=function(){if(this.Z!==sf)throw Error("Sanitized content was not of kind HTML.");return Ec(this.toString())};function vf(){O.call(this)}pa(vf,O);vf.prototype.Z=sf;function wf(a){if(a!=null)switch(a.Ga){case 1:return 1;case -1:return-1;case 0:return 0}return null}var P=function(a){function b(c){this.content=c}b.prototype=a.prototype;return function(c,d){c=new b(String(c));d!==void 0&&(c.Ga=d);return c}}(vf);function xf(a){return yf(String(a),()=>"").replace(zf,"&lt;")}const Af=RegExp.prototype.hasOwnProperty("sticky"),Bf=new RegExp((Af?"":"^")+"(?:!|/?([a-zA-Z][a-zA-Z0-9:-]*))",Af?"gy":"g"); 
function yf(a,b){const c=[],d=a.length;let e=0,f=[],g,h,k=0;for(;k<d;){switch(e){case 0:var m=a.indexOf("<",k);if(m<0){if(c.length===0)return a;c.push(a.substring(k));k=d}else c.push(a.substring(k,m)),h=m,k=m+1,Af?(Bf.lastIndex=k,m=Bf.exec(a)):(Bf.lastIndex=0,m=Bf.exec(a.substring(k))),m?(f=["<",m[0]],g=m[1],e=1,k+=m[0].length):c.push("<");break;case 1:m=a.charAt(k++);switch(m){case "'":case '"':let n=a.indexOf(m,k);n<0?k=d:(f.push(m,a.substring(k,n+1)),k=n+1);break;case ">":f.push(m);c.push(b(f.join(""), 
g));e=0;f=[];h=g=null;break;default:f.push(m)}break;default:throw Error();}e===1&&k>=d&&(k=h+1,c.push("<"),e=0,f=[],h=g=null)}return c.join("")}function Cf(a,b){a=a.replace(/<\//g,"<\\/").replace(/\]\]>/g,"]]\\>");return b?a.replace(/{/g," \\{").replace(/}/g," \\}").replace(/\/\*/g,"/ *").replace(/\\$/,"\\ "):a}function Q(a){a!=null&&a.Z===sf?(a=xf(a.getContent()),a=String(a).replace(Df,Ef)):a=String(a).replace(Ff,Ef);return a} 
function Gf(a){a=String(a);const b=(d,e,f)=>{const g=Math.min(e.length-f,d.length);for(let k=0;k<g;k++){var h=e[f+k];if(d[k]!==("A"<=h&&h<="Z"?h.toLowerCase():h))return!1}return!0};for(var c=0;(c=a.indexOf("<",c))!=-1;){if(b("\x3c/script",a,c)||b("\x3c!--",a,c))return"zSoyz";c+=1}return a}function Hf(a){if(a==null)return" null ";if(a!=null&&a.Z===tf)return a.getContent();switch(typeof a){case "boolean":case "number":return" "+a+" ";default:return"'"+String(String(a)).replace(If,Jf)+"'"}} 
function R(a){return a!=null&&a.Z===uf?Cf(a.getContent(),!1):a==null?"":a instanceof Ic?Cf(Jc(a),!1):Cf(String(a),!0)}const Kf={"\x00":"&#0;","\t":"&#9;","\n":"&#10;","\v":"&#11;","\f":"&#12;","\r":"&#13;"," ":"&#32;",'"':"&quot;","&":"&amp;","'":"&#39;","-":"&#45;","/":"&#47;","<":"&lt;","=":"&#61;",">":"&gt;","`":"&#96;","\u0085":"&#133;","\u00a0":"&#160;","\u2028":"&#8232;","\u2029":"&#8233;"};function Ef(a){return Kf[a]} 
const Lf={"\x00":"\\x00","\b":"\\x08","\t":"\\t","\n":"\\n","\v":"\\x0b","\f":"\\f","\r":"\\r",'"':"\\x22",$:"\\x24","&":"\\x26","'":"\\x27","(":"\\x28",")":"\\x29","*":"\\x2a","+":"\\x2b",",":"\\x2c","-":"\\x2d",".":"\\x2e","/":"\\/",":":"\\x3a","<":"\\x3c","=":"\\x3d",">":"\\x3e","?":"\\x3f","[":"\\x5b","\\":"\\\\","]":"\\x5d","^":"\\x5e","{":"\\x7b","|":"\\x7c","}":"\\x7d","\u0085":"\\x85","\u2028":"\\u2028","\u2029":"\\u2029"};function Jf(a){return Lf[a]} 
const Ff=/[\x00\x22\x26\x27\x3c\x3e]/g,Df=/[\x00\x22\x27\x3c\x3e]/g,If=/[\x00\x08-\x0d\x22\x26\x27\/\x3c-\x3e\x5b-\x5d\x7b\x7d\x85\u2028\u2029]/g,Mf=/^[a-zA-Z0-9+\/_-]+={0,2}$/;function Nf(a){a=String(a);return Mf.test(a)?a:"zSoyz"}const zf=/</g;/* 
 Copyright The Closure Library Authors. 
 SPDX-License-Identifier: Apache-2.0 
*/ 
const Of={};/* 
 
Math.uuid.js (v1.4) 
http://www.broofa.com 
mailto:<EMAIL> 
Copyright (c) 2010 Robert Kieffer 
Dual licensed under the MIT and GPL licenses. 
*/ 
var Pf="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz".split("");class Qf{constructor(a){for(var b=Array(36),c=0,d,e=0;e<36;e++)e==8||e==13||e==18||e==23?b[e]="-":e==14?b[e]="4":(c<=2&&(c=33554432+Math.random()*16777216|0),d=c&15,c>>=4,b[e]=Pf[e==19?d&3|8:d]);this.uuid=b.join("");this.callback=a}} 
function Rf(a){const b=t.imalib_globalCallbacks||new Map,c=b.get("AFMA_updateActiveView")||[];if(c.length===0&&t.AFMA_updateActiveView){const d=new Qf(t.AFMA_updateActiveView);c.push(d);t.AFMA_updateActiveView=void 0}t.AFMA_updateActiveView||(t.AFMA_updateActiveView=function(){const d=b.get("AFMA_updateActiveView");for(const e of d)e.callback.apply(null,arguments)});a=new Qf(a);c.push(a);b.set("AFMA_updateActiveView",c);t.imalib_globalCallbacks=b;return a.uuid} 
function Sf(a){if(t.AFMA_updateActiveView){var b=t.imalib_globalCallbacks;if(b){var c=b.get("AFMA_updateActiveView");if(c){var d=c.findIndex(e=>e.uuid===a);d!==-1&&(c.splice(d,1),c.length===0&&(t.AFMA_updateActiveView=void 0),b.set("AFMA_updateActiveView",c),t.imalib_globalCallbacks=b)}}}};var google={};qf([1,8,9,10,11,12,2,3,4,5,15,16,19,20,21,23]);qf([1,6,7,9,10,11,12,2,3,4,5,13,14,18,19,20,21,23]);qf([1,6,7,9,10,11,12,22,2,3,4,5,13,14,17,18,19,20,21,23]);new rf;var Tf=(t.navigator?t.navigator.userAgent:"").indexOf("Android")!=-1;function Uf(a,b){this.type=a;this.currentTarget=this.target=b;this.defaultPrevented=!1}Uf.prototype.preventDefault=function(){this.defaultPrevented=!0};var S=class{constructor(a,b){this.messageName=a;this.parameters=b||{}}},Vf=class extends Uf{constructor(a,b){super(a.messageName,b);this.params=a.parameters||{}}};function Wf(a,{data:b,source:c}){if(c&&b){var d=a.o,e=b.messageName;b=b.parameters;if(a.j)switch(e){case "mraid_loaded":e=b.is_top_win;e===!1&&(a.l=!0,a.i=Rf(f=>{a.j&&Xf(a,new S("update_activeview_action",f))}),d.indexOf(c)===-1&&(d.push(c),typeof c.postMessage!=="undefined"&&c.postMessage(new S("mraid_env_obj",window.MRAID_ENV),"*")));break;case "start_tracking_action":a.g==0&&window.AFMA_SendMessage("trackActiveViewUnit");a.g+=1;break;case "stop_tracking_action":--a.g;a.g==0&&(window.AFMA_SendMessage("untrackActiveViewUnit", 
{hashCode:b.hashCode}),a.i&&(Sf(a.i),a.i=null));break;case "register_iframe_window_action":e=b.is_top_win;e===!1&&d.indexOf(c)===-1&&d.push(c);break;case "receive_message_action":b.messageName=="disableMraidOpen"&&window.AFMA_ReceiveMessage(b.messageName,b.parameters)}else switch(e){case "mraid_env_obj":window.MRAID_ENV=b;break;case "update_activeview_action":window.AFMA_updateActiveView&&window.AFMA_updateActiveView(b);break;case "receive_message_action":window.AFMA_ReceiveMessage(b.messageName, 
b.parameters)}}}function Xf(a,b){a.o.forEach(c=>c.postMessage(b,"*"))}class Yf{constructor(){this.o=[];this.j=window===window.top;this.l=!1;this.g=0;this.i=null;typeof window.addEventListener!=="undefined"&&window.addEventListener("message",a=>Wf(this,a))}};function Zf(a){var b=Bc("gmsg://mobileads.google.com/"+a.messageName);a=new Map(Object.entries(a.parameters));b=Cc(b).toString();const c=b.split(/[?#]/),d=/[?]/.test(b)?"?"+c[1]:"";return Qc(c[0],d,/[#]/.test(b)?"#"+(d?c[2]:c[1]):"",a)};function $f(a,b){Uf.call(this,a?a.type:"");this.relatedTarget=this.currentTarget=this.target=null;this.button=this.screenY=this.screenX=this.clientY=this.clientX=0;this.key="";this.metaKey=this.shiftKey=this.altKey=this.ctrlKey=!1;this.state=null;this.pointerId=0;this.pointerType="";this.g=null;a&&this.init(a,b)}pa($f,Uf); 
$f.prototype.init=function(a,b){const c=this.type=a.type,d=a.changedTouches&&a.changedTouches.length?a.changedTouches[0]:null;this.target=a.target||a.srcElement;this.currentTarget=b;b=a.relatedTarget;b||(c=="mouseover"?b=a.fromElement:c=="mouseout"&&(b=a.toElement));this.relatedTarget=b;d?(this.clientX=d.clientX!==void 0?d.clientX:d.pageX,this.clientY=d.clientY!==void 0?d.clientY:d.pageY,this.screenX=d.screenX||0,this.screenY=d.screenY||0):(this.clientX=a.clientX!==void 0?a.clientX:a.pageX,this.clientY= 
a.clientY!==void 0?a.clientY:a.pageY,this.screenX=a.screenX||0,this.screenY=a.screenY||0);this.button=a.button;this.key=a.key||"";this.ctrlKey=a.ctrlKey;this.altKey=a.altKey;this.shiftKey=a.shiftKey;this.metaKey=a.metaKey;this.pointerId=a.pointerId||0;this.pointerType=a.pointerType;this.state=a.state;this.g=a;a.defaultPrevented&&$f.ca.preventDefault.call(this)};$f.prototype.preventDefault=function(){$f.ca.preventDefault.call(this);const a=this.g;a.preventDefault?a.preventDefault():a.returnValue=!1};var ag="closure_listenable_"+(Math.random()*1E6|0);var bg=0;function cg(a,b,c,d,e){this.listener=a;this.proxy=null;this.src=b;this.type=c;this.capture=!!d;this.ia=e;this.key=++bg;this.aa=this.ga=!1}function dg(a){a.aa=!0;a.listener=null;a.proxy=null;a.src=null;a.ia=null};function eg(a){this.src=a;this.g={};this.i=0}eg.prototype.add=function(a,b,c,d,e){const f=a.toString();a=this.g[f];a||(a=this.g[f]=[],this.i++);const g=fg(a,b,d,e);g>-1?(b=a[g],c||(b.ga=!1)):(b=new cg(b,this.src,f,!!d,e),b.ga=c,a.push(b));return b};function gg(a,b,c,d,e){b=b.toString();if(b in a.g){var f=a.g[b];c=fg(f,c,d,e);c>-1&&(dg(f[c]),Array.prototype.splice.call(f,c,1),f.length==0&&(delete a.g[b],a.i--))}} 
function hg(a,b){const c=b.type;c in a.g&&Ga(a.g[c],b)&&(dg(b),a.g[c].length==0&&(delete a.g[c],a.i--))}function fg(a,b,c,d){for(let e=0;e<a.length;++e){const f=a[e];if(!f.aa&&f.listener==b&&f.capture==!!c&&f.ia==d)return e}return-1};var ig="closure_lm_"+(Math.random()*1E6|0),jg={},kg=0;function lg(a,b,c,d,e){if(d&&d.once)return mg(a,b,c,d,e);if(Array.isArray(b)){for(let f=0;f<b.length;f++)lg(a,b[f],c,d,e);return null}c=ng(c);return a&&a[ag]?a.listen(b,c,ja(d)?!!d.capture:!!d,e):og(a,b,c,!1,d,e)} 
function og(a,b,c,d,e,f){if(!b)throw Error("Invalid event type");const g=ja(e)?!!e.capture:!!e;let h=qg(a);h||(a[ig]=h=new eg(a));c=h.add(b,c,d,g,f);if(c.proxy)return c;d=rg();c.proxy=d;d.src=a;d.listener=c;if(a.addEventListener)e===void 0&&(e=!1),a.addEventListener(b.toString(),d,e);else if(a.attachEvent)a.attachEvent(sg(b.toString()),d);else if(a.addListener&&a.removeListener)a.addListener(d);else throw Error("addEventListener and attachEvent are unavailable.");kg++;return c} 
function rg(){function a(c){return b.call(a.src,a.listener,c)}const b=tg;return a}function mg(a,b,c,d,e){if(Array.isArray(b)){for(let f=0;f<b.length;f++)mg(a,b[f],c,d,e);return null}c=ng(c);return a&&a[ag]?a.g.add(String(b),c,!0,ja(d)?!!d.capture:!!d,e):og(a,b,c,!0,d,e)} 
function ug(a,b,c,d,e){if(Array.isArray(b))for(let f=0;f<b.length;f++)ug(a,b[f],c,d,e);else(d=ja(d)?!!d.capture:!!d,c=ng(c),a&&a[ag])?gg(a.g,String(b),c,d,e):a&&(a=qg(a))&&(b=a.g[b.toString()],a=-1,b&&(a=fg(b,c,d,e)),(c=a>-1?b[a]:null)&&vg(c))} 
function vg(a){if(typeof a!=="number"&&a&&!a.aa){var b=a.src;if(b&&b[ag])hg(b.g,a);else{var c=a.type,d=a.proxy;b.removeEventListener?b.removeEventListener(c,d,a.capture):b.detachEvent?b.detachEvent(sg(c),d):b.addListener&&b.removeListener&&b.removeListener(d);kg--;(c=qg(b))?(hg(c,a),c.i==0&&(c.src=null,b[ig]=null)):dg(a)}}}function sg(a){return a in jg?jg[a]:jg[a]="on"+a}function tg(a,b){if(a.aa)a=!0;else{b=new $f(b,this);const c=a.listener,d=a.ia||a.src;a.ga&&vg(a);a=c.call(d,b)}return a} 
function qg(a){a=a[ig];return a instanceof eg?a:null}var wg="__closure_events_fn_"+(Math.random()*1E9>>>0);function ng(a){if(typeof a==="function")return a;a[wg]||(a[wg]=function(b){return a.handleEvent(b)});return a[wg]};function xg(a){L.call(this);this.i=a;this.g={}}pa(xg,L);var yg=[];xg.prototype.listen=function(a,b,c,d){Array.isArray(b)||(b&&(yg[0]=b.toString()),b=yg);for(let e=0;e<b.length;e++){const f=lg(a,b[e],c||this.handleEvent,d||!1,this.i||this);if(!f)break;this.g[f.key]=f}return this};function zg(a){rc(a.g,function(b,c){this.g.hasOwnProperty(c)&&vg(b)},a);a.g={}}xg.prototype.F=function(){xg.ca.F.call(this);zg(this)}; 
xg.prototype.handleEvent=function(){throw Error("EventHandler.handleEvent not implemented");};function Ag(){L.call(this);this.g=new eg(this);this.C=this;this.l=null}pa(Ag,L);Ag.prototype[ag]=!0;l=Ag.prototype;l.addEventListener=function(a,b,c,d){lg(this,a,b,c,d)};l.removeEventListener=function(a,b,c,d){ug(this,a,b,c,d)}; 
l.dispatchEvent=function(a){var b,c=this.l;if(c)for(b=[];c;c=c.l)b.push(c);c=this.C;const d=a.type||a;if(typeof a==="string")a=new Uf(a,c);else if(a instanceof Uf)a.target=a.target||c;else{var e=a;a=new Uf(d,c);vc(a,e)}e=!0;let f,g;if(b)for(g=b.length-1;g>=0;g--)f=a.currentTarget=b[g],e=Bg(f,d,!0,a)&&e;f=a.currentTarget=c;e=Bg(f,d,!0,a)&&e;e=Bg(f,d,!1,a)&&e;if(b)for(g=0;g<b.length;g++)f=a.currentTarget=b[g],e=Bg(f,d,!1,a)&&e;return e}; 
l.F=function(){Ag.ca.F.call(this);if(this.g){var a=this.g;let b=0;for(const c in a.g){const d=a.g[c];for(let e=0;e<d.length;e++)++b,dg(d[e]);delete a.g[c];a.i--}}this.l=null};l.listen=function(a,b,c,d){return this.g.add(String(a),b,!1,c,d)};function Bg(a,b,c,d){b=a.g.g[String(b)];if(!b)return!0;b=b.concat();let e=!0;for(let f=0;f<b.length;++f){const g=b[f];if(g&&!g.aa&&g.capture==c){const h=g.listener,k=g.ia||g.src;g.ga&&hg(a.g,g);e=h.call(k,d)!==!1&&e}}return e&&!d.defaultPrevented};function Cg(a,b){Ag.call(this);this.j=a||1;this.i=b||t;this.u=ma(this.ab,this);this.B=Date.now()}pa(Cg,Ag);l=Cg.prototype;l.enabled=!1;l.I=null;l.setInterval=function(a){this.j=a;this.I&&this.enabled?(this.stop(),this.start()):this.I&&this.stop()};l.ab=function(){if(this.enabled){const a=Date.now()-this.B;a>0&&a<this.j*.8?this.I=this.i.setTimeout(this.u,this.j-a):(this.I&&(this.i.clearTimeout(this.I),this.I=null),this.dispatchEvent("tick"),this.enabled&&(this.stop(),this.start()))}}; 
l.start=function(){this.enabled=!0;this.I||(this.I=this.i.setTimeout(this.u,this.j),this.B=Date.now())};l.stop=function(){this.enabled=!1;this.I&&(this.i.clearTimeout(this.I),this.I=null)};l.F=function(){Cg.ca.F.call(this);this.stop();delete this.i};function Dg(){if(window.googleJsEnvironment&&(window.googleJsEnvironment.environment=="rhino"||window.googleJsEnvironment.environment=="jscore"))return new Eg;if(Tf&&window.googleAdsJsInterface&&"notify"in window.googleAdsJsInterface)try{return window.googleAdsJsInterface.notify("gmsg://mobileads.google.com/noop"),new Eg}catch(a){}else if(window.webkit&&window.webkit.messageHandlers&&window.webkit.messageHandlers.gadGMSGHandler)return new Fg;return new Gg}function Hg(){Ig||(Ig=Dg());return Ig} 
var Ig=null,Jg=class extends L{};function Kg(a){const b=tc(a.parameters);b["google.afma.Notify_dt"]=(new Date).getTime();return Zf(new S(a.messageName,b)).toString()} 
var Lg=class extends Jg{constructor(a){super();this.u=a;this.l=[];this.j=new Cg(1);this.B=new xg(this);this.B.listen(this.j,"tick",this.C)}sendMessage(a){this.l.push(a);this.j.enabled||(a=this.l.shift(),this.u(a),this.j.start())}C(){const a=this.l.shift();a?this.u(a):this.j.stop()}},Gg=class extends Lg{constructor(){super(a=>{var b=this.g[this.i];b||(b=md(document,"IFRAME"),b.id="afma-notify-"+(new Date).getTime(),b.style.display="none",this.g[this.i]=b);this.i=(this.i+1)%25;const c=tc(a.parameters); 
c["google.afma.Notify_dt"]=(new Date).getTime();var d=b;a=Zf(new S(a.messageName,c));d.src=Cc(a).toString();b.parentNode||document.body.appendChild(b)});this.g=[];this.i=0}F(){this.g.forEach(nd);this.g=[];super.F()}},Eg=class extends Jg{sendMessage(a){a=Kg(a);window.googleAdsJsInterface&&window.googleAdsJsInterface.notify&&(window.googleAdsJsInterface.notify(a),window.googleAdsJsInterface.DEBUG&&console.log(a))}},Fg=class extends Jg{sendMessage(a){a=Kg(a);window.webkit&&window.webkit.messageHandlers&& 
window.webkit.messageHandlers.gadGMSGHandler&&window.webkit.messageHandlers.gadGMSGHandler.postMessage(a)}};var Ng=class extends Ag{constructor(){super();this.j=Hg();this.j=Hg();M(this,na(We,this.j));this.i={};this.u=new Yf}sendMessage(a,b){let c;typeof a==="string"?c=new S(a,b):a instanceof S&&(c=a);document.readyState=="loading"?mg(t,"DOMContentLoaded",()=>this.j.sendMessage(c),!1,this):this.j.sendMessage(c)}receiveMessage(a,b){if(this.shouldForwardMessageToIframe())this.forwardMessage(new S("receive_message_action",new S(a,b)));else{const c=document.getElementById("ad_iframe");c!=void 0&&c.contentWindow!= 
void 0&&c.contentWindow.AFMA_ReceiveMessage!=void 0&&c.contentWindow.AFMA_ReceiveMessage(a,b)}a=="onshow"&&document.readyState=="loading"?mg(t,"DOMContentLoaded",()=>Mg(a,b??void 0)):this.dispatchEvent(new Vf(new S(a,b),this))}addObserver(a,b,c){const d=e=>void c.call(b,e.type,e.params);this.listen(a,d);this.i[a]||(this.i[a]={});this.i[a][b]=d}removeObserver(a,b){this.i[a]&&this.i[a][b]&&(gg(this.g,String(a),this.i[a][b]),delete this.i[a][b])}shouldForwardMessageToIframe(){return this.u.l}forwardMessage(a){Xf(this.u, 
a)}};function T(a,b){t.AFMA_Communicator?t.AFMA_Communicator.sendMessage(a,b):Og(a,b)}function Og(a,b){document.readyState=="loading"?(a=ma(Og,null,a,b),mg(t,"DOMContentLoaded",a,!1)):(a=new S(a,b),Hg().sendMessage(a))}function Mg(a,b){t.AFMA_Communicator.receiveMessage(a,b)}function Pg(a,b,c,d){t.AFMA_Communicator.removeEventListener(a,b,c,d)}function Qg(a,b,c,d){t.AFMA_Communicator.addEventListener(a,b,c,d)}function Rg(a,b,c){t.AFMA_Communicator.addObserver(a,b,c)} 
function Sg(a,b){t.AFMA_Communicator.removeObserver(a,b)}t.AFMA_Communicator||(oa("AFMA_AddEventListener",Qg),oa("AFMA_RemoveEventListener",Pg),oa("AFMA_AddObserver",Rg),oa("AFMA_RemoveObserver",Sg),oa("AFMA_ReceiveMessage",Mg),oa("AFMA_SendMessage",T),t.AFMA_Communicator=new Ng);var Tg=class{constructor(a){this.g=a;Qg("h5adsEvent",b=>void this.g(b))}ta(a,b){T("h5ads",{obj_id:a,action:"create_interstitial_ad",ad_unit:b})}ua(a,b){T("h5ads",{obj_id:a,ad_unit:b,action:"create_rewarded_ad"})}dispose(a){T("h5ads",{obj_id:a,action:"dispose"})}};class U{constructor(){this.promise=new Promise((a,b)=>{this.resolve=a;this.reject=b})}};function Ug(a){a.extras===void 0&&(a.extras={});a.extras.highfive="1";return encodeURIComponent(JSON.stringify(a))}class Vg extends L{constructor(a,b){super();this.id=a;this.g=b}load(a,b){this.o||(this.listener=b,b=this.id,a=Ug(a),T("h5ads",{obj_id:b,action:"load_interstitial_ad",ad_request:a}))}show(){if(!this.o){if(this.listener==null)throw Error("load must be called before show");T("h5ads",{obj_id:this.id,action:"show_interstitial_ad"})}}F(){this.g.j.dispose(this.id);super.F()}} 
class Wg extends L{constructor(a,b){super();this.id=a;this.g=b}load(a,b){this.o||(this.listener=b,b=this.id,a=Ug(a),T("h5ads",{obj_id:b,action:"load_rewarded_ad",ad_request:a}))}show(){if(!this.o){if(this.listener==null)throw Error("load must be called before show");T("h5ads",{obj_id:this.id,action:"show_rewarded_ad"})}}F(){this.g.j.dispose(this.id);super.F()}}function Xg(a){const b=a.l;a.l+=1;return b} 
var Yg=class{constructor(){this.l=0;this.ads=new Map;this.g=new Map;this.o=new U;this.i=0;this.j=new Tg(a=>{a=a.params;switch(a.eventCategory){case "initialize":this.ads.clear();this.g.clear();this.i=3;this.o.resolve(this);break;case "creation":var b=a.objectId;switch(a.event){case "nativeObjectCreated":if(a=this.g.get(b))this.g.delete(b),this.ads.set(b,a.ad),a.U.resolve(a.ad);return;case "nativeObjectNotCreated":if(a=this.g.get(b))this.g.delete(b),a.ad.dispose(),a.U.reject(Error("Native object not created")); 
return;default:return}case "interstitial":if((b=this.ads.get(a.objectId))&&b instanceof Vg&&b.listener)switch(a.event){case "onAdLoaded":b.listener.X?.(b);break;case "onAdFailedToLoad":b.listener.W?.(b,a.errorCode);break;case "onAdOpened":b.listener.Ua?.(b);break;case "onAdClicked":b.listener.Eb?.(b);break;case "onAdClosed":b.listener.K?.(b);break;case "onNativeAdObjectNotAvailable":b.listener.Y?.(b)}break;case "rewarded":if((b=this.ads.get(a.objectId))&&b instanceof Wg&&b.listener)switch(a.event){case "onRewardedAdLoaded":b.listener.X?.(b); 
break;case "onRewardedAdFailedToLoad":b.listener.W?.(b,a.errorCode);break;case "onRewardedAdOpened":b.listener.Ua?.(b);break;case "onRewardedAdFailedToShow":b.listener.Ta?.(b,a.errorCode);break;case "onUserEarnedReward":b.listener.Va?.(b);break;case "onRewardedAdClosed":b.listener.K?.(b);break;case "onNativeAdObjectNotAvailable":b.listener.Y?.(b)}}})}connect(){switch(this.i){case 3:return Promise.resolve(this);case 1:return this.o.promise;default:return this.i=1,this.o=new U,T("h5ads",{action:"initialize"}), 
setTimeout(()=>{this.i!==3&&(this.i=2,this.o.reject(Error("GmaBridge could not connect to SDK after 10000 ms.")))},1E4),this.o.promise}}ta(a){if(this.i!==3)return Promise.reject(Error("GmaBridge is not connected"));const b=Xg(this),c=new U;this.g.set(b,{U:c,ad:new Vg(b,this)});this.j.ta(b,a);return c.promise}ua(a){if(this.i!==3)return Promise.reject(Error("GmaBridge is not connected"));const b=Xg(this),c=new U;this.g.set(b,{U:c,ad:new Wg(b,this)});this.j.ua(b,a);return c.promise}};let Zg=null;var $b=class extends E{i(){return dc(this,3)}};var $g=class extends E{i(){var a=this.G;if(!(a=Zb(a,a[w]|0))){a=$b;var b;if(!(b=a[Ta])){const c=new a;b=c.G;b[w]|=34;b=a[Ta]=c}a=b}return a}},ah=function(a){return b=>{if(b==null||b=="")b=new a;else{b=JSON.parse(b);if(!Array.isArray(b))throw Error("dnarr");b[w]|=32;b=new a(b)}return b}}($g);var bh=class extends E{};var ch=class{constructor(){const a={};this.i=(b,c)=>a[b]!=null?a[b]:c;this.o=(b,c)=>a[b]!=null?a[b]:c;this.l=(b,c)=>a[b]!=null?a[b]:c;this.u=(b,c)=>a[b]!=null?a[b]:c;this.j=(b,c)=>a[b]!=null?c.concat(a[b]):c;this.g=()=>{}}};function W(a){return J(ch).i(a.g,a.defaultValue)}function X(a){return J(ch).o(a.g,a.defaultValue)};var eh=class{constructor(){this.wasPlaTagProcessed=!1;this.wasReactiveAdConfigReceived={};this.adCount={};this.wasReactiveAdVisible={};this.stateForType={};this.reactiveTypeEnabledInAsfe={};this.wasReactiveTagRequestSent=!1;this.reactiveTypeDisabledByPublisher={};this.tagSpecificState={};this.messageValidationEnabled=!1;this.floatingAdsStacking=new dh;this.sideRailProcessedFixedElements=new Set;this.sideRailAvailableSpace=new Map;this.sideRailPlasParam=new Map;this.sideRailMutationCallbacks=[];this.clickTriggeredInterstitialMayBeDisplayed= 
!1}},dh=class{constructor(){this.maxZIndexRestrictions={};this.nextRestrictionId=0;this.maxZIndexListeners=[]}};function fh(a){const b=gh(a);Ea(a.floatingAdsStacking.maxZIndexListeners,c=>c(b))}function gh(a){a=Uc(a.floatingAdsStacking.maxZIndexRestrictions);return a.length?Math.min.apply(null,a):null} 
var hh=class{constructor(a){a.google_reactive_ads_global_state?(a.google_reactive_ads_global_state.sideRailProcessedFixedElements==null&&(a.google_reactive_ads_global_state.sideRailProcessedFixedElements=new Set),a.google_reactive_ads_global_state.sideRailAvailableSpace==null&&(a.google_reactive_ads_global_state.sideRailAvailableSpace=new Map),a.google_reactive_ads_global_state.sideRailPlasParam==null&&(a.google_reactive_ads_global_state.sideRailPlasParam=new Map),a.google_reactive_ads_global_state.sideRailMutationCallbacks== 
null&&(a.google_reactive_ads_global_state.sideRailMutationCallbacks=[])):a.google_reactive_ads_global_state=new eh;this.floatingAdsStacking=a.google_reactive_ads_global_state.floatingAdsStacking}addListener(a){this.floatingAdsStacking.maxZIndexListeners.push(a);a(gh(this))}removeListener(a){Ha(this.floatingAdsStacking.maxZIndexListeners,b=>b===a)}},ih=class{constructor(a){this.controller=a;this.g=null}};function jh(a){const b={bottom:"auto",clear:"none",display:"inline","float":"none",height:"auto",left:"auto",margin:0,"margin-bottom":0,"margin-left":0,"margin-right":"0","margin-top":0,"max-height":"none","max-width":"none",opacity:1,overflow:"visible",padding:0,"padding-bottom":0,"padding-left":0,"padding-right":0,"padding-top":0,position:"static",right:"auto",top:"auto","vertical-align":"baseline",visibility:"visible",width:"auto","z-index":"auto"};Ea(Object.keys(b),c=>{const d=a.style[Kc(c)]; 
(typeof d!=="undefined"?d:a.style[pd(a,c)])||H(a,c,b[c])});Zc(a)};function kh(a,b){const c=dd("STYLE",a);c.textContent=Jc(Nc`* { pointer-events: none; }`);a?.head.appendChild(c);setTimeout(()=>{a?.head.removeChild(c)},b)}function lh(a,b,c){if(!a.body)return null;const d=new mh;d.apply(a,b);return()=>{var e=c||0;e>0&&kh(b.document,e);H(a.body,{filter:d.g,webkitFilter:d.g,overflow:d.o,position:d.j,top:d.l});b.scrollTo(0,d.i)}} 
class mh{constructor(){this.g=this.l=this.j=this.o=null;this.i=0}apply(a,b){this.o=a.body.style.overflow;this.j=a.body.style.position;this.l=a.body.style.top;this.g=a.body.style.filter?a.body.style.filter:a.body.style.webkitFilter;this.i=b.pageYOffset===void 0?(b.document.documentElement||b.document.body.parentNode||b.document.body).scrollTop:b.pageYOffset;H(a.body,"top",`${-this.i}px`)}};function nh(a,b){var c;if(!a.i)for(a.i=[],c=a.g.parentElement;c;){a.i.push(c);if(a.J(c))break;c=c.parentNode&&c.parentNode.nodeType===1?c.parentNode:null}c=a.i.slice();let d,e;for(d=0;d<c.length;++d)(e=c[d])&&b.call(a,e,d,c)}var oh=class extends L{constructor(a,b,c){super();this.g=a;this.R=b;this.B=c;this.i=null;M(this,()=>this.i=null)}J(a){return this.B===a}};function ph(a,b){const c=a.B;if(c)if(b){b=a.C;if(b.g==null){var d=b.controller;const e=d.floatingAdsStacking.nextRestrictionId++;d.floatingAdsStacking.maxZIndexRestrictions[e]=2147483646;fh(d);b.g=e}Wc(c,{display:"block"});a.u.body&&!a.j&&(a.j=lh(a.u,a.R,a.P));c.setAttribute("tabindex","0");c.setAttribute("aria-hidden","false");a.u.body.setAttribute("aria-hidden","true")}else b=a.C,b.g!=null&&(d=b.controller,delete d.floatingAdsStacking.maxZIndexRestrictions[b.g],fh(d),b.g=null),Wc(c,{display:"none"}), 
a.j&&(a.j(),a.j=null),a.u.body.setAttribute("aria-hidden","false"),c.setAttribute("aria-hidden","true")}function qh(a){ph(a,!1);const b=a.B;if(b){var c=rh(a.L);nh(a,d=>{Wc(d,c);jh(d)});a.g.setAttribute("width","");a.g.setAttribute("height","");H(a.g,c);H(a.g,sh);H(b,th);H(b,{background:"transparent"});Wc(b,{display:"none",position:"fixed"});jh(b);jh(a.g);(Ca()&&pc()?ed(a.L):1)<=1||(H(b,{overflow:"scroll","max-width":"100vw"}),Zc(b))}} 
var uh=class extends oh{constructor(a,b){var c=window,d=X(pf);super(a,c,b);this.L=c;this.P=d;this.j=null;this.u=c.document;a=new hh(c);this.C=new ih(a)}l(){ph(this,!1)}},th={backgroundColor:"white",opacity:"1",position:"fixed",left:"0px",top:"0px",margin:"0px",padding:"0px",display:"none",zIndex:"2147483647"},sh={left:"0",position:"absolute",top:"0"};function rh(a){a=Ca()&&pc()?ed(a):1;a=100*(a<1?1:a);return{width:`${a}vw`,height:`${a}vh`}};var vh=class extends uh{constructor(a,b){super(a,b);qh(this)}J(a){a.classList?a=a.classList.contains("adsbygoogle"):(a=a.classList?a.classList:(typeof a.className=="string"?a.className:a.getAttribute&&a.getAttribute("class")||"").match(/\S+/g)||[],a=Da(a,"adsbygoogle")>=0);return a}};function wh(){const a=window.google_ad_modifications=window.google_ad_modifications||{};a.afg_slotcar_vars||(a.afg_slotcar_vars={});return a.afg_slotcar_vars};function qe(a,b){a.g.size>0||xh(a);const c=a.g.get(0);c?c.push(b):a.g.set(0,[b])}function yh(a,b,c,d){F(b,c,d);M(a,()=>fd(b,c,d))}function zh(a,b){a.state!==1&&(a.state=1,a.g.size>0&&Ah(a,b))} 
function xh(a){a.D.document.visibilityState?yh(a,a.D.document,"visibilitychange",b=>{a.D.document.visibilityState==="hidden"&&zh(a,b);a.D.document.visibilityState==="visible"&&(a.state=0)}):"onpagehide"in a.D?(yh(a,a.D,"pagehide",b=>{zh(a,b)}),yh(a,a.D,"pageshow",()=>{a.state=0})):yh(a,a.D,"beforeunload",b=>{zh(a,b)})}function Ah(a,b){for(let c=9;c>=0;c--)a.g.get(c)?.forEach(d=>{d(b)})}var Bh=class extends L{constructor(a){super();this.D=a;this.state=0;this.g=new Map}};async function Ch(a,b){var c=10;return c<=0?Promise.reject(Error(`wfc bad input ${c} ${200}`)):b()?Promise.resolve():new Promise((d,e)=>{const f=a.setInterval(()=>{--c?b()&&(a.clearInterval(f),d()):(a.clearInterval(f),e(Error(`wfc timed out ${c}`)))},200)})};function Dh(a){const b=a.state.pc;return b!==null&&b!==0?b:a.state.pc=mc(a.D)}function Eh(a){var b=a.state.wpc;if(b===null||b==="")b=a.state,a=a.D,a=a.google_ad_client?String(a.google_ad_client):(a.google_ad_modifications=a.google_ad_modifications||{}).head_tag_slot_vars?.google_ad_client??a.document.querySelector(".adsbygoogle[data-ad-client]")?.getAttribute("data-ad-client")??"",b=b.wpc=a;return b}async function Fh(a){q(await q(Ch(a.D,()=>!(!Dh(a)||!Eh(a)))))} 
async function Gh(a,b){q(await q(Fh(a)));var c=a.i;var d=new Ud;var e=Dh(a);d=C(d,1,Ab(e),"0");e=Eh(a);d=D(d,2,e);d=C(d,3,Ab(a.state.sd),"0");a=C(d,7,Ab(Math.round(a.D.performance.now())),"0");a=C(a,3,Ab(1),"0");b=cc(a,10,Vd,b);me(c,b)}var Hh=class{constructor(a,b){this.D=ld()||window;this.g=b??new Bh(this.D);this.i=a??new se(100,100,!0,this.g);this.state=Se(Pe(),33,()=>{const c=X(Ye);return{sd:c,ssp:c>0&&Sc()<1/c,pc:null,wpc:null,cu:null,le:[],lgdp:[],psi:null,tar:0,cc:null}})}};function Ih(a){var b=window;return a.google_adtest==="on"||a.google_adbreak_test==="on"||b.location.host.endsWith("h5games.usercontent.goog")||b.location.host==="gamesnacks.com"?b.document.querySelector('meta[name="h5-games-eids"]')?.getAttribute("content")?.split(",").map(c=>Math.floor(Number(c))).filter(c=>!isNaN(c)&&c>0)||[]:[]};class Jh{};function Kh(){var a=t.ggeac||(t.ggeac={});ue(J(ve),a);Lh(a);J(Jh);J(ch).g()}function Lh(a){const b=J(ch);b.i=(c,d)=>te(5,a,()=>!1)(c,d,1);b.o=(c,d)=>te(6,a,()=>0)(c,d,1);b.l=(c,d)=>te(7,a,()=>"")(c,d,1);b.u=(c,d)=>te(8,a,()=>[])(c,d,1);b.j=(c,d)=>te(17,a,()=>[])(c,d,1);b.g=()=>{te(15,a,()=>{})(1)}};function Mh(a){const b=J(ve).g();a=Ih(a);return b.concat(a).join(",")};function Nh({Oa:a,Xa:b}){return a||(b==="dev"?"dev":"")};function Oh(a){Ke.j(b=>{b.shv=String(a);b.mjsv=Nh({Oa:ce(),Xa:a});b.eid=Mh(t)})}function Ph(a,b){a=b?.i()?.i()||dc(a,2);Oh(a)};function Qh(){var a=window.adsbygoogle;try{const b=a.pageState;bb(b);return ah(b)}catch(b){return new $g}};var Rh=typeof sttc==="undefined"?void 0:sttc;function Sh(){var a=Ke;try{if(bb(Rh),Rh.length>0)return new bh(JSON.parse(Rh))}catch(b){a.V(838,b instanceof Error?b:Error(String(b)))}return new bh};var Th=class extends L{F(){this.disposeAd();super.F()}},Uh=class extends L{constructor(a){super();this.callback=a}},Vh=class extends L{constructor(a){super();this.i=a;this.g=new Set}fetch(a,b){const c=new Uh(a.callback);this.g.add(c);this.i.fetch({...a,callback:d=>{c.o?d&&d.dispose():c.callback(d);this.g.delete(c)}},b)}F(){for(const a of this.g.values())a.dispose();this.g.clear();super.F()}};var Wh=class{constructor(a){var b=ce();this.l=a;this.u=b;this.o="unset"}ja(a){this.o=a}ba(a){this.g=a.Ia;this.i=a.Ka}N(a){this.j=a}A(a,b={}){b.event=a;b.client=this.o;b.bow_v=this.l;b.js_v=this.u;b.fetcher=this.j?.toString()??"unset";this.g&&(b.admb_iid=this.g);this.i&&(b.admb_rid=this.i);a=J(ve).g();!b.eid&&a.length&&(b.eid=a.toString());Nd(Ie,"slotcar",b,!0,1)}};var Xh=class extends Th{constructor(a,b,c,d){super();this.ad=a;this.j=b;this.l=c;this.i=d;this.g=null;this.u=this.B=!1;this.C=!0}show(a){this.g=a;if(this.C&&this.u)this.ad.show();else if(this.u)this.K();else throw Error("Tried to show AdMobAd before it finished loading.");}disposeAd(){this.ad.dispose()}X(){this.u=!0;this.l(this)}W(){this.l(null);this.dispose()}Y(){this.i.A("admb_na");this.g?this.K():this.C=!1}}; 
function Yh(a){return{X:K(849,()=>{a.X()}),W:K(850,()=>{a.W()}),K:K(851,()=>{a.K()}),Y:K(854,()=>{a.Y()})}}var Zh=class extends Xh{constructor(a,b,c,d){super(a,b,c,d);this.ad=a;this.j=b;this.l=c;this.i=d}request(){this.ad.load(this.j,Yh(this))}K(){(0,this.g)(1)}};function $h(a){return{X:K(849,()=>{a.X()}),W:K(850,()=>{a.W()}),Ta:K(855,()=>{a.i.A("admb_rfs");(0,a.g)(2)}),Va:K(852,()=>{a.B=!0}),K:K(853,()=>{a.K()}),Y:K(854,()=>{a.Y()})}} 
var ai=class extends Xh{constructor(a,b,c,d){super(a,b,c,d);this.ad=a;this.j=b;this.l=c;this.i=d}request(){this.ad.load(this.j,$h(this))}K(){this.B?(0,this.g)(3):(0,this.g)(2)}};function bi(a,b){const c=b.google_adbreak_test==="on";switch(a){case 1:return c?"ca-app-pub-3940256099942544/1033173712":b.google_admob_interstitial_slot;case 2:return c?"ca-app-pub-3940256099942544/5224354917":b.google_admob_rewarded_slot;default:throw Error(`Unknown ad type ${a}`);}}function ci(a,b,c){a.j.error(`Unable to fetch ad: '${b}' is missing from tag.`);c(null)}function di(a){Ne(850,()=>{a(null)})} 
var ei=class{constructor(a,b,c){this.i=a;this.j=b;this.g=c;this.o=mc(window).toString()}fetch(a,b){const c={isTestDevice:!1,httpTimeoutMillis:X(df)*1E3};var d=b.google_tag_for_child_directed_treatment;if(d==="0"||d==="1")c.tagForChildDirectedTreatment=d==="1";d=b.google_tag_for_under_age_of_consent;if(d==="0"||d==="1")c.tagForUnderAgeOfConsent=d==="1";d=b.google_max_ad_content_rating;typeof d==="string"&&(c.maxAdContentRating=d);c.extras??(c.extras={});c.extras.muted=a.wa||a.type===2?"0":"1";this.o&& 
(c.extras.pvsid=this.o);c.extras.correlator=Ve().toString();d=Mh(b);d.length&&(c.extras.slotcar_eids=d);b=bi(a.type,b);a.type===1?typeof b!=="string"?ci(this,"data-admob-interstitial-slot",a.callback):this.i.ta(b).then(e=>{(new Zh(e,c,a.callback,this.g)).request()}).catch(()=>{di(a.callback)}):typeof b!=="string"?ci(this,"data-admob-rewarded-slot",a.callback):this.i.ua(b).then(e=>{(new ai(e,c,a.callback,this.g)).request()}).catch(()=>{di(a.callback)})}};const fi=new Set(["auto","on"]),gi=new Set(["on","off"]),hi=new Set("start pause next browse reward preroll".split(" ")),ii=new Map([["start","interstitial"],["pause","interstitial"],["next","interstitial"],["browse","interstitial"],["reward","reward"],["preroll","preroll"]]),ji=new Map([["interstitial",["type"]],["reward",["type","beforeReward","adDismissed","adViewed"]],["preroll",["type","adBreakDone"]]]),ki=new Map([["interstitial",["beforeReward","adDismissed","adViewed"]],["reward",[]],["preroll", 
["afterAd","beforeReward","adDismissed","adViewed"]]]),li="beforeAd afterAd beforeReward adDismissed adViewed adBreakDone".split(" "),mi=new Map([["beforeBreak","beforeAd"],["afterBreak","afterAd"],["adComplete","adViewed"]]);var ni=new Set("google_ad_client google_ad_host google_ad_channel google_ad_host_channel google_tag_for_under_age_of_consent google_tag_for_child_directed_treatment google_page_url".split(" ")); 
const oi=new Set([...ni,"google_admob_interstitial_slot","google_admob_rewarded_slot","google_max_ad_content_rating"]); 
function pi(a,b){let c=!1;const d=f=>{c=!0;b.error(`Invalid ad config: ${f}.`)};if(a.preloadAdBreaks!=null&&!fi.has(a.preloadAdBreaks)){var e=Array.from(fi).map(f=>`'${f}'`).join(", ");d(`'preloadAdBreaks' must be one of [${e}]`)}a.sound==null||gi.has(a.sound)||(e=Array.from(gi).map(f=>`'${f}'`).join(", "),d(`'sound' must be one of [${e}]`));a.onReady!=null&&typeof a.onReady!=="function"&&d("'onReady' must be a function");if(a.h5AdsConfig!=null)if(typeof a.h5AdsConfig!=="object")d("'h5AdsConfig' must be an object"); 
else for(const [f,g]of Object.entries(a.h5AdsConfig))a=f,e=g,oi.has(a)?typeof e!=="string"&&d(`'h5AdsConfig.${a}' must be a string`):d(`'h5AdsConfig.${a}' is not a valid property`);return!c} 
function qi(a,b,c){for(const [d,e]of mi){const f=d,g=e;if(f in a){c.A("lgc_fld",{field:f});if(g in a)return b.error(`Invalid placement config: '${f}' has been renamed to ${g}. Cannot pass both fields. Please use ${g} only.`),!1;b.warn(`Placement config: '${f}' has been renamed to '${g}'. Please update your code.`);a[g]=a[f];delete a[f]}}return!0} 
function ri(a,b,c){let d=!1;const e=h=>{d=!0;b.error(`Invalid placement config: ${h}.`)};a=Object.assign({},a);if(!qi(a,b,c))return{xa:!1,Aa:a};if(!hi.has(a.type)){var f=Array.from(hi).map(h=>`'${h}'`).join(", ");e(`'type' must be one of [${f}]`);return{xa:!d,Aa:a}}c=ii.get(a.type);const g=ji.get(c).filter(h=>!(h in a));g.length>0&&e("missing required properties "+g.map(h=>`'${h}'`).join(", "));c=ki.get(c).filter(h=>h in a);c.length>0&&e("the following properties are not used for the given ad type: "+ 
c.map(h=>`'${h}'`).join(", "));for(f of li)f in a&&typeof a[f]!=="function"&&e(`'${f}' must be a function`);return{xa:!d,Aa:a}};const si={[1]:10,[2]:11},ti={closed:1,viewed:3,dismissed:2,error:4};var vi=class{constructor(){this.D=window}fetch(a,b){const c={};for(const d in b)ni.has(d)&&(c[d]=b[d]);c.google_reactive_ad_format=si[a.type];c.google_wrap_fullscreen_ad=!0;c.google_video_play_muted=a.type!==2&&!a.wa;c.google_acr=d=>{a.callback(d?new ui(d):null)};c.google_tag_origin="gsc";this.D.adsbygoogle.push({params:c})}},ui=class extends Th{constructor(a){super();this.ad=a}show(a){this.ad.show(b=>{a(ti[b.status])})}disposeAd(){this.ad.disposeAd()}};function wi(){return P('<ins class="adsbygoogle" style="width:100% !important;height:100% !important;" id="fake-interstitial-ins"><iframe style="overflow:hidden;" width="100%" height="100%" frameborder="0" marginwidth="0" marginheight="0" vspace="0" hspace="0" scrolling="no" src="about:blank" id="aswift-fake"></iframe></ins>')} 
function xi(){return P('<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path style="fill:#f5f5f5" d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12 19 6.41z"/><path fill="none" d="M0 0h24v24H0V0z"/></svg>')} 
function yi(a){const b=a.Ra;a=a.Za;return P('<div class="dialog-wrapper" style="width: 100%; height: 100%; position: absolute; top: 0;"><div class="close-confirmation-dialog" id="close-confirmation-dialog" style="width: '+Q(R(Math.floor(a*.78)))+'px"><div class="confirmation-title" style="font-size: '+Q(R(Math.floor(b*.031)))+"px; margin-top: "+Q(R(Math.floor(b*.0375)))+"px; margin-left: "+Q(R(Math.floor(a*.066)))+"px; margin-right: "+Q(R(Math.floor(a*.066)))+'px;">Close Ad?</div><div class="confirmation-message" style="font-size: '+ 
Q(R(Math.floor(b*.025)))+"px; margin-bottom: "+Q(R(Math.floor(b*.0375)))+"px; margin-top: "+Q(R(Math.floor(b*.0375)))+"px; margin-left: "+Q(R(Math.floor(a*.066)))+"px; margin-right: "+Q(R(Math.floor(a*.066)))+'px;">You will lose your reward</div><div class="confirmation-buttons" style="font-size: '+Q(R(Math.floor(b*.0218)))+"px; line-height: "+Q(R(Math.floor(b*.05625)))+"px; margin-right: "+Q(R(Math.floor(b*.0125)))+"px; margin-bottom: "+Q(R(Math.floor(b*.0125)))+'px;"><div class="close-ad-button" id="close-ad-button" style="padding-left: '+ 
Q(R(Math.floor(a*.044)))+"px; padding-right: "+Q(R(Math.floor(a*.044)))+'px;">CLOSE</div><div class="resume-ad-button" id="resume-ad-button" style="padding-left: '+Q(R(Math.floor(a*.044)))+"px; padding-right: "+Q(R(Math.floor(a*.044)))+'px;">RESUME</div></div></div></div>')};var zi=Pc`about:blank`;Cc(zi);var Ai=Pc`javascript:undefined`;Cc(Ai);function Bi(a,b,c){a=a.g;c=b(c||Of,{});b=a||qa||(qa=new od);if(c&&c.g)b=c.g();else{b=md(b.g,"DIV");b:if(ja(c)){if(c.za&&(c=c.za(),c instanceof Dc))break b;c=Oc("zSoyz")}else c=Oc(String(c));b.innerHTML=Fc(c)}b.childNodes.length==1&&(c=b.firstChild,c.nodeType==1&&(b=c));return b}class Ci{constructor(){this.g=qa||(qa=new od)}render(a,b){a=a(b||{},{});return String(a)}};function Di(a,b){if(a.contentDocument||a.contentWindow)b(a);else{const c=()=>{b(a);fd(a,"load",c)};F(a,"load",c)}} 
async function Ei(a){if(a.g==null)throw Error("Tried to show ad before initialized.");const b=new U;var c=a.g.g,d=Math.min(Number(c.clientWidth),Number(c.clientHeight));let e=Math.max(Number(c.clientWidth),Number(c.clientHeight));Fi(a)&&(d*=.5,e*=.5);c=c.contentDocument;a=c.body.appendChild(Bi(a.B,yi,{Za:d,Ra:e}));d=a.querySelector(".resume-ad-button");F(a.querySelector(".close-ad-button"),"click",()=>{b.resolve(0)});F(d,"click",()=>{b.resolve(1)});d=q(await b.promise);c.body.removeChild(a);return d=== 
0}function Fi(a){if(a.g==null)throw Error("Tried to show ad before initialized.");a=a.g.g;return Number(a.clientWidth)>1E3||Number(a.clientHeight)>1E3} 
var Gi=class extends Th{constructor(a,b){super();this.j=b;this.B=new Ci;this.i=10;this.u=!1;this.l=Bi(this.B,wi);this.l.dataset["slotcar"+(b===1?"Interstitial":"Rewarded")]="true";document.documentElement.appendChild(this.l);Di(this.l.firstChild,c=>{var d={};{var e=this.j===2?"Rewarded ad example":"Interstitial ad example";var f=this.j,g=d??{},h=g.wb;const k=g.yb,m=g.sb,n=g.zb,p=g.vb,r=g.xb,A=g.tb;!g.ub&&(h instanceof O?h.getContent():h)?(g=g&&g.Bb,h=P((h instanceof O?h.getContent():h)?"<script"+ 
(g?' nonce="'+Q(Nf(g))+'"':"")+">window['ppConfig'] = {productName: "+Gf(Hf(k??"unknown"))+", deleteIsEnforced: "+Gf(Hf(!!m))+", sealIsEnforced: "+Gf(Hf(!!n))+", heartbeatRate: "+Gf(Hf(p??.5))+", periodicReportingRateMillis: "+Gf(Hf(r??6E4))+", disableAllReporting: "+Gf(Hf(A??!1))+"};"+Gf(Hf(h??""))+"\x3c/script>":"")):h="";h="<!DOCTYPE html><html><head>"+P(h);d=(d=d??{})&&d.Cb;d=P("\n  <style"+(d?' nonce="'+Q(Nf(d))+'"':"")+'>\n    body {\n      padding: 0;\n      margin: 0;\n      background-color: #262626;\n    }\n    .container {\n      width: 100vw;\n      height: 92vh;\n      display: flex;\n      flex-direction: column;\n    }\n    .container .creative {\n      background-color: white;\n      border-style: solid;\n      border-width: thin;\n      border-color:#bdc1c6;\n      height: 250px;\n      margin: 20vh auto auto auto;\n      overflow: hidden;\n      padding: 0;\n      width: 300px;\n    }\n    .header-panel {\n      display: flex;\n      justify-content: center;\n      margin-bottom: 20px;\n      background-color: #424242;\n      border: 1px solid transparent;\n      border-radius: 4px;\n      height: 8vh;\n      color: #f5f5f5;\n      font-family: "Google Sans",Roboto,Arial,sans-serif;\n      font-size: 20px;\n      line-height: 8vh;\n    }\n    .dismiss-button {\n      display: flex;\n      flex-direction: row;\n      height: inherit;\n      align-items: center;\n      padding-right: 4%;\n      cursor: pointer;\n      position: absolute;\n      right: 0;\n    }\n    .count-down-container {\n      display: inline-flex;\n      flex: auto;\n    }\n    .adContainer {\n      display: flex;\n      flex-direction: row;\n      width: 100%;\n      height: 100%;\n      text-align: left;\n      margin: 0;\n    }\n    .adContainer .logo {\n      align-self: center;\n      width: 40px;\n      margin: 0 24px;\n      height: 40px;\n    }\n    .adContainer .logo IMG {\n      height: 40px;\n      width: 40px;\n    }\n    .adContainer .text {\n      margin: auto auto auto 0;\n    }\n    .adContainer .button {\n      align-self: center;\n      height: 100%;\n      max-height: 48px;\n      /* This gives a perceived margin of 32px, due to the margins within the button SVGs. */\n      margin-right: 30px;\n    }\n    .adContainer .button-inner {\n      max-height: 48px;\n      height: 100%;\n    }\n    .adContainer .button-inner SVG {\n      height: 100%;\n      width: auto;\n    }\n    .adText {\n      font-family: "Google Sans",Roboto,Arial,sans-serif;\n      font-size: 18px;\n      font-weight: normal;\n      line-height: 18px;\n      color: #202124;\n      margin-bottom: 4px;\n    }\n    .nativeIframeMessage .text {\n      padding: 0 10px;\n    }\n    .creative a {\n      text-decoration: none;\n    }\n\n    @media (max-height: 44px),\n        (max-height: 150px) and (max-width: 210px) {\n      .adContainer .logo {\n        display: none;\n      }\n      .adContainer .text {\n        margin-left: 5px;\n      }\n    }\n    @media (max-height: 110px) and (max-width: 330px) {\n      .adText {\n        font-size: 13px;\n        line-height: 13px;\n        margin-bottom: 2px;\n      }\n    }\n    @media (max-height: 38px) {\n      .adText {\n        font-size: 17px;\n        line-height: 17px;\n        margin-bottom: 0;\n      }\n    }\n    @media (max-height: 20px) {\n      .adText {\n        font-size: 12px;\n        line-height: 12px;\n        margin-bottom: 0;\n      }\n    }\n\n    /* Vertically stacked assets in cases where creative is not a distictly\n       horizontal rectangle shape */\n    @media (min-height: 240px),\n        (max-width: 65px) and (min-height: 50px),\n        (max-width: 130px) and (min-height: 100px),\n        (max-width: 195px) and (min-height: 150px),\n        (max-width: 260px) and (min-height: 200px) {\n      .adContainer .logo {\n        display: initial;\n      }\n      .adContainer .text {\n        margin-left: 0;\n      }\n      .adContainer {\n        text-align: center;\n        display: flex;\n        flex-direction: column;\n      }\n      .adContainer .logo {\n        margin: 40px auto 24px auto;\n      }\n      .adContainer .text {\n        margin: 0 auto auto auto;\n      }\n      .adContainer .text .adText{\n        margin-bottom: 8px;\n      }\n      .adContainer .button {\n        margin: auto auto 32px auto;\n      }\n      @media (max-height: 200px) {\n        .adContainer .logo {\n          display: none;\n        }\n        .adContainer .text {\n          margin: 10px auto auto auto;\n        }\n      }\n    }\n\n    .x-button {\n      display: flex;\n      align-items: center;\n    }\n\n    .dialog-wrapper {\n      background: rgba(0, 0, 0, .4);\n      height: 100%;\n      left: 0;\n      opacity: 1;\n      pointer-events: auto;\n      position: fixed;\n      top: 0;\n      transition: opacity .15s ease-out;\n      -webkit-transition: opacity .15s ease-out;\n      width: 100%;\n      will-change: opacity;\n      z-index: 2147483647;\n    }\n\n    .close-confirmation-dialog {\n      background: #fff;\n      box-shadow: 0 16px 24px 2px rgba(0, 0, 0, .14),\n        0 6px 30px 5px rgba(0, 0, 0, .12), 0 8px 10px -5px rgba(0, 0, 0, .2);\n      font-family: Roboto, sans-serif;\n      left: 50%;\n      position: fixed;\n      top: 50%;\n      transform: translate(-50%, -50%);\n      -webkit-transform: translate(-50%, -50%);\n    }\n\n    .confirmation-title {\n      color: #000;\n    }\n\n    .confirmation-message {\n      color: #757575;\n    }\n\n    .confirmation-buttons {\n      display: -webkit-box;\n      display: -webkit-flex;\n      display: flex;\n\n      -webkit-box-align: center;\n      -webkit-align-items: center;\n      align-items: center;\n\n      -webkit-box-pack: flex-end;\n      -webkit-justify-content: flex-end;\n      justify-content: flex-end;\n    }\n\n    .close-ad-button,\n    .resume-ad-button {\n      color: #fff;\n      cursor: pointer;\n      font-weight: 500;\n      text-align: center;\n\n      display: -webkit-box;\n      display: -webkit-flex;\n      display: flex;\n    }\n\n    .close-ad-button {\n      color: #3e82f7;\n    }\n\n    .resume-ad-button {\n      background: #3e82f7;\n      border-radius: 2px;\n      box-shadow: 0 2px 2px 0 rgba(0, 0, 0, .24);\n    }\n  </style>\n  '); 
d=h+d+'</head><body><div class="header-panel">';f!==2&&(d+="Ad");d+='<div class="dismiss-button" id="dismiss-button">'+(f===2?'<div class="count-down-container" id="count-down-container"><div id="count-down"><div class="count-down-text" id="count-down-text"></div></div><div class="x-button" id="close-button" style="padding-left: 5px;">'+xi()+"</div></div>":"")+'<div class="x-button" id="dismiss-button-element">'+xi()+'</div></div></div><div class="container"><div class="creative">'+P('<div style="position:relative;float:right;top:1px;right:1px;width:15px;height:15px;"><svg style="fill:#00aecd;" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 15 15"><circle cx="6" cy="6" r="0.67"></circle><path d="M4.2,11.3Q3.3,11.8,3.3,10.75L3.3,4.1Q3.3,3.1,4.3,3.5L10.4,7.0Q12.0,7.5,10.4,8.0L6.65,10.0L6.65,7.75a0.65,0.65,0,1,0,-1.3,0L5.35,10.75a0.9,0.9,0,0,0,1.3,0.8L12.7,8.2Q13.7,7.5,12.7,6.7L3.3,1.6Q2.2,1.3,1.8,2.5L1.8,12.5Q2.2,13.9,3.3,13.3L4.8,12.5A0.3,0.3,0,1,0,4.2,11.3Z"></path></svg></div>')+ 
'<a target="_blank" href="https://developers.google.com/ad-placement"><div class="adContainer"><div class="logo">'+P('<img width="40" height="40" alt="" src="data:image/svg+xml;base64,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"/>')+ 
'</div><div class="text"><div class="adText">'+(e!=null&&e.Z===sf?e:e instanceof Dc?P(Fc(e).toString()):P(String(String(e)).replace(Ff,Ef),wf(e)))+"</div></div></div></a></div></div></body></html>";e=P(d)}e=e.za();f=c.contentDocument||c.contentWindow.document;f.open();f.write(Fc(e));f.close();this.g=new vh(c,this.l);a(this)})}show(a){if(this.g==null)throw Error("Tried to show ad before initialized.");const b=this.g.g.contentDocument,c=b.getElementById("dismiss-button");ph(this.g,!0);if(this.j===2){const d= 
c.querySelector("#dismiss-button-element");d.style.display="none";const e=async()=>{if(this.g==null)throw Error("Failure on rewarded example: Could not find ad frame.");this.u=!0;q(await q(Ei(this)))?(this.g.l(),F(c,"click",e),a(2)):this.u=!1};F(c,"click",e);this.i=X(gf);const f=this.i<0;this.u=!1;const g=b.getElementById("count-down-container"),h=g.querySelector("#count-down-text");h.innerText=`Reward in ${this.i} seconds`;f||(this.C=setInterval(()=>{this.u||(--this.i,h.innerText=`Reward in ${this.i} seconds`); 
if(this.i===0){g.style.display="none";d.style.display="";clearInterval(this.C);const k=async()=>{if(this.g==null)throw Error("Failure on rewarded example: Could not find ad frame.");this.g.l();fd(c,"click",k);a(3)};F(c,"click",k);fd(c,"click",e)}},1E3))}else F(c,"click",()=>{if(this.g==null)throw Error("Failure on rewarded example: Could not find ad frame.");this.g.l();a(1)})}disposeAd(){this.g?.l();nd(this.l)}},Hi=class{fetch(a){new Gi(a.callback,a.type)}};function Ii(a,{La:b,Ha:c}){a.g.addEventListener(a.ima.AdErrorEvent.Type.AD_ERROR,()=>{Ji(a,c)});for(const d of[a.ima.AdEvent.Type.SKIPPED,a.ima.AdEvent.Type.COMPLETE])a.g.addEventListener(d,()=>{Ji(a,b)});a.g.addEventListener(a.ima.AdEvent.Type.USER_CLOSE,()=>{Ji(a,c)})}function Ji(a,b){Wc(a.i,{display:"none","z-index":"0"});a.callback(b)} 
var Ki=class extends Th{constructor(a,b,c,d,e){super();this.ima=a;this.j=b;this.i=c;this.l=d;this.g=e;this.callback=()=>{}}show(a){this.callback=a;switch(this.j){case 1:Ii(this,{La:1,Ha:1});break;case 2:Ii(this,{La:3,Ha:2})}try{this.g.addEventListener(this.ima.AdEvent.Type.STARTED,()=>{Wc(this.i,{display:"block","z-index":"1000000"})}),this.g.start()}catch(b){this.g.discardAdBreak(),Ji(this,this.j===2?2:1)}}disposeAd(){this.l.destroy();this.g.destroy();this.i.remove()}};function Li(a){try{const b=(a||window).document,c=b.compatMode=="CSS1Compat"?b.documentElement:b.body;return(new G(c.clientWidth,c.clientHeight)).round()}catch(b){return new G(-12245933,-12245933)}};function Mi(a){a=a.document.createElement("div");Wc(a,{top:"0",left:"0",width:"100%",height:"100%",position:"fixed",display:"none","z-index":"0"});return a}function Ni(a,b){a=new a.ima.AdDisplayContainer(b);a.initialize();return a} 
function Oi(a,b,c,d,e,f){const g=new a.ima.AdsRenderingSettings;g.restoreCustomPlaybackStateOnAdBreakComplete=!0;g.enablePreloading=!0;const h=b.getAdsManager({currentTime:0},g),k=Li(a.D);window.addEventListener("resize",()=>{h.resize(k.width,k.height)});h.addEventListener(a.ima.AdEvent.Type.LOADED,()=>{const m=new Ki(a.ima,f.type,c,d,h);e.destroy();f.callback(m)});h.init(k.width,k.height)} 
function Pi(a){a={ad_type:"video_text_image",client:a.O.google_ad_client.replace("ca-","ca-games-"),description_url:encodeURI(a.O.google_page_url||a.document.URL),overlay:"0",ad_rule:"0"};const b=new URL("https://googleads.g.doubleclick.net/pagead/ads");b.search=(new URLSearchParams(a)).toString();return b.toString()} 
var Qi=class{constructor(a){var b=window,c=Pc`https://imasdk.googleapis.com/js/sdkloader/ima3.js`;this.D=b;this.O=a;this.ima=google.ima;this.document=this.D.document;a=this.document.createElement("script");Hc(a,c);const d=new U;a.onload=()=>{this.ima=this.D.google.ima;d.resolve()};this.Ya=d.promise;this.document.documentElement.appendChild(a)}async fetch(a){q(await this.Ya);const b=Mi(this);this.document.body.appendChild(b);const c=Ni(this,b),d=new this.ima.AdsLoader(c);d.addEventListener(this.ima.AdsManagerLoadedEvent.Type.ADS_MANAGER_LOADED, 
g=>{Oi(this,g,b,c,d,a)});d.addEventListener(this.ima.AdErrorEvent.Type.AD_ERROR,()=>{a.callback(null)});const e=new this.ima.AdsRequest;e.adTagUrl=Pi(this);e.setAdWillAutoPlay(!0);e.setAdWillPlayMuted(!(a.type===2||a.wa));e.forceNonLinearFullSlot=!0;const f=Li(this.D);e.linearAdSlotWidth=f.width;e.linearAdSlotHeight=f.height;e.nonLinearAdSlotWidth=f.width;e.nonLinearAdSlotHeight=f.height;d.requestAds(e)}};var Ri=class{constructor(){this.j=J(Hh);this.l={inv_plcnf:1,inv_adcnf:2,adbr_cl:3,adbr_noad:4,adbr_nousitr:5,adbr_usrint:6,adbr_naf:7,adbr_pgad:8,adbr_pgaatd:9,adbr_tepgai:10,adcf_cl:11,adcf_afni:29,adcf_pgad:13,adcf_pgaatd:14,prf_suc:15,prf_fail:16,admb_na:17,admb_rfs:18,admb_fetfail:19,lgc_fld:20,pr_rr:21,pr_to:22,r_to:31,api_ld:23,admb_tm:24,adbr_dn:25,dbl_init:26,sess_m:27,ad_cls:28,ad_rdtr:30};this.u={admob:1,adsense:2,adbreaktest:0,ima:4}}ja(){}ba(a){this.g=a.Ia;this.i=a.Ka}N(a){this.o=this.u[a]}async A(a){var b= 
new Td;a=ec(b,1,this.l[a]).N(this.o);this.g&&D(a,3,this.g);this.i&&D(a,4,this.i);q(await q(Gh(this.j,a)))}},Si=class{constructor(a){this.sa=new Ri;this.ha=a}ja(a){this.ha.ja(a)}ba(a){this.sa.ba(a);this.ha.ba(a)}N(a){this.sa.N(a);this.ha.N(a)}async A(a,b={}){q(await q(this.sa.A(a,b)));this.ha.A(a,b)}};function Ti(a){let b=a.o;a.g!==null&&(b+=(Date.now()-a.g)/1E3);return Math.min(b,a.j)}function Ui(a){const b=Ti(a);if(b<a.i)throw Error("Current tokens in seconds cannot be less than frequency cap in seconds when ad is shown.");a.o=b-a.i}function Vi(a){return a.g!==null&&Date.now()-a.g<a.l*1E3?!1:Ti(a)>=a.i} 
var Wi=class{constructor(a,b){var c=X(mf);this.i=a;this.l=b;this.g=null;if(a<=0)throw Error("Frequency cap cannot be less than or equal to 0.");if(c<1)throw Error("Bucket capacity cannot be less than 1.");if(1.5>c)throw Error("Initial tokens cannot be greater than the bucket capacity.");this.o=1.5*a;this.j=c*a}};const Xi="click mousedown mouseup touchstart touchend pointerdown pointerup keydown keyup scroll".split(" ");var Yi=class extends L{constructor(){var a=window;super();this.g=0;const b=()=>{this.g=Date.now()};for(const c of Xi)a.document.documentElement.addEventListener(c,b,{capture:!0});M(this,()=>{for(const c of Xi)a.document.documentElement.removeEventListener(c,b,{capture:!0})})}};class Zi extends L{constructor(a,b){super();this.U=new U;this.g=!1;this.timeout=setTimeout(K(726,()=>{b()}),a*1E3)}get promise(){return this.U.promise}resolve(a){this.o||(this.g=!0,this.U.resolve(a))}reject(a){this.o||(this.g=!0,this.U.reject(a))}F(){clearTimeout(this.timeout)}} 
function $i(a,b){const c=a.google_adbreak_test;if(c)switch(c){case "on":return new Hi;case "adsense":return new vi;default:throw b.error(`Unsupported data-adbreak-test value '${c}. Supported values: '${"on"}'.`),Error("unsupported test mode");}return W(nf)?new Qi(a):new vi}function aj(a){return["google_admob_interstitial_slot","google_admob_rewarded_slot"].some(b=>typeof bj(b,a)==="string")}function bj(a,b){if(b[a]&&typeof b[a]==="string")return String(b[a])} 
function cj(a,b){Zg==null&&(Zg=new Yg);return Zg.connect().then(c=>new ei(c,a,b))}function dj(a){if(typeof a!=="string")return-1;a=/^(\d+)s$/.exec(a);return a==null?-1:Number(a[1])} 
function ej(a,b){window.addEventListener("onpagehide"in self?"pagehide":"unload",K(938,()=>{if(b.first_slotcar_request_processing_time){var c=Date.now();a.g.A("sess_m",{igsl:c-b.first_slotcar_request_processing_time,afh:String(b.ad_frequency_hint),niab:Number(b.number_of_interstitial_ad_breaks),nias:Number(b.number_of_interstitial_ads_shown),opsl:c-b.adsbygoogle_execution_start_time})}}))} 
function fj(a,b){const c=b.google_admob_ads_only;typeof c==="string"&&(c==="on"?aj(b)?a.oa=!0:a.j.error("Cannot set data-admob-ads-only without providing at least one AdMob ad slot id."):a.j.error(`Unsupported data-admob-ads-only value '${c}'. Supported value: 'on'.`))}function gj(a,b){if(a.Ba)a="adbreaktest";else{var c;a.R?c="admob":c=W(nf)&&!b.google_adbreak_test?"ima":"adsense";a=c}return a} 
function hj(a,b){for(const c of[1,2]){const d=a.i.get(c);if(d||ij(a,c))d?(d.dispose(),a.i.delete(c)):(a.u.get(c).dispose(),a.u.delete(c)),Y(a,c,0,b)}}function jj(a){if(!a.P||a.Fa){if(!a.pa&&a.l.preloadAdBreaks){var b=W(jf)?[1]:[1,2];for(const c of b)if(!a.i.has(c)&&!a.da.has(c))return}for(a.pa=!0;a.qa.length>0;)b=a.qa.pop(),kj(a,"onReady",b)}} 
function lj(a,b){b=b.google_ad_frequency_hint;const c=X(hf);if(typeof b!=="string")return c;const d=/^(\d+)s$/.exec(b);return d==null?(a.j.error(`Invalid data-ad-frequency-hint value: '${b}'. It must be in format 'Xs' where X is a number.`),c):Math.max(X(kf),Number(d[1]))}function mj(a,b,c){b={cvw:Number(b.M.width),cvh:Number(b.M.height),ovw:Number(b.T.width),ovh:Number(b.T.height)};c!==void 0&&(b.adType=Number(c));a.g.A("ad_rdtr",b)} 
function nj(a,b){for(const c in b)if(b[c]!==a.O[c])return!0;return!1}function oj(a,b){!a.oa||a.R?b():a.g.A("adcf_afni")}function pj(a,b,c,d=!0){const e=a.i.get(b);e&&(e.dispose(),Y(a,b,10,c),d&&a.i.delete(b))}function ij(a,b){var c;if(c=a.u.has(b))c=!a.u.get(b).g;return c}function Y(a,b,c,d){if(ij(a,b))throw Error("already scheduled");c=new Zi(c,()=>{qj(a,b,d)});a.u.set(b,c);return c}function kj(a,b,c){bd(()=>{rj(a,b,c)})} 
function Z(a,b,c,d){const e={breakType:b.type,breakFormat:c===2?"reward":b.type==="preroll"?"preroll":"interstitial",breakStatus:d};b.name&&(e.breakName=b.name);a.g.A("adbr_dn",{breakType:e.breakType,breakFormat:e.breakFormat,breakStatus:e.breakStatus,breakName:e.breakName??""});const f=b.adBreakDone;f!=null&&kj(a,"adBreakDone",()=>{f(e)})} 
async function sj(a,b){if(a.ea)return a.g.A("pr_rr"),Z(a,b,1,"frequencyCapped"),!1;a.ea=!0;const c=q(await q(tj(a,1,X(ef),2)));return c===1?(a.g.A("adbr_noad"),Z(a,b,1,"noAdPreloaded"),!1):c===2?(a.g.A("pr_to",{source:"slotcar"}),Z(a,b,1,"timeout"),!1):!0}async function uj(a,b){const c=q(await q(tj(a,2,X(ff),3)));return c===1?(a.g.A("adbr_noad"),Z(a,b,2,"noAdPreloaded"),!1):c===3?(a.g.A("r_to",{}),Z(a,b,2,"timeout"),!1):!0} 
async function vj(a,b){const c=new U;a.Ca=c;rj(a,"beforeReward",()=>{b.beforeReward(()=>{c.resolve(0)})});return q(await c.promise)===0}function rj(a,b,c){if(c)try{c()}catch(d){return a.j.error(`'${b}' callback threw an error:`,d),!1}return!0}function wj(a,b){return W(af)&&b===1&&a.L!==null}async function tj(a,b,c,d){a.P&&q(await a.la);a=ij(a,b)?a.u.get(b):Y(a,b,0,2);return q(await q(Promise.race([a.promise,cd(c*1E3,d)])))} 
function xj(a,b,c,d,e){const f=a.Na.get(c),g=b?1:-1,h=f.length>0?f[f.length-1]:0;Math.sign(h)===g?f[f.length-1]=h+g:f.push(g);a.g.A(b?"prf_suc":"prf_fail",{type:c,src:d,stats:f.join(","),timing:Date.now()-e})} 
function qj(a,b,c){const d=Date.now(),e=new G(window.innerWidth,window.innerHeight);a.B.fetch({type:b,wa:a.l.sound==="on",callback:f=>{a.da.delete(b);const g=a.u.get(b);f?(g.resolve(0),a.i.set(b,f),M(f,()=>{a.i.delete(b)}),a.ra.set(b,e)):(g.resolve(1),a.da.add(b),Y(a,b,X(lf),5));xj(a,f!=null,b,c,d);c!==1&&c!==7||jj(a)}},a.O)} 
var yj=class extends L{constructor(a,b){super();this.j=a;this.g=b;this.qa=[];this.l={sound:"on"};this.i=new Map;this.u=new Map;this.ra=new Map;this.Ea=new Yi;this.B=null;this.ka="";this.ma=this.ea=this.P=this.pa=this.Ba=!1;this.C=0;this.fa=!1;this.Ca=null;this.J=new G(window.innerWidth,window.innerHeight);this.oa=this.Fa=this.R=!1;this.na=0;this.la=Promise.resolve();this.Da=0;this.O={};this.L=null;this.da=new Set;this.Na=new Map([[1,[]],[2,[]]]);M(this,na(We,this.Ea))}init(a){this.ka=String(a.google_ad_client); 
if(this.B!=null)this.g.A("dbl_init",{ad_client:this.ka});else{this.O={...a};var b=wh();b.in_game_session_length=0;b.number_of_interstitial_ad_breaks=0;b.number_of_interstitial_ads_shown=0;b.ad_frequency_hint=a.google_ad_frequency_hint?String(a.google_ad_frequency_hint):"";ej(this,b);b=navigator.userAgent;var c=RegExp("\\bwv\\b");this.P=b.includes("Android")&&c.test(b);a.google_adbreak_test==="on"&&(this.Ba=!0);fj(this,a);this.g.ja(this.ka);this.B=new Vh($i(a,this.j));this.g.N(gj(this,a));if(aj(a)){this.g.ba({Ia:bj("google_admob_interstitial_slot", 
a),Ka:bj("google_admob_rewarded_slot",a)});const e=Date.now();if(!W(Ze)||this.P)b=cj(this.j,this.g).then(f=>{this.B!=null&&this.B.dispose();this.B=new Vh(f);this.R=!0;this.g.N(gj(this,a));hj(this,7)}).catch(f=>{this.g.A("admb_fetfail",{error:f})}).finally(()=>{this.g.A("admb_tm",{timing:Date.now()-e})}),this.P&&(this.la=Promise.race([b,cd(X(cf)*1E3)]),this.la.finally(()=>{this.Fa=!0;jj(this)}))}this.C=lj(this,a);W(af)&&(this.L=new Wi(this.C,W(of)?X(bf):this.C/2));this.na=dj(a.google_ad_start_delay_hint); 
this.J=new G(window.innerWidth,window.innerHeight);var d=oc(K(791,()=>{if(this.J.width!==window.innerWidth||this.J.height!==window.innerHeight)if(!this.R||this.J.width!==window.innerWidth){var e={M:new G(window.innerWidth,window.innerHeight),T:this.J};if(W($e)){e=e.M;for(const g of this.i.keys()){var f={M:e,T:this.ra.get(g)};if(f.M.width<f.T.width||f.M.height<f.T.height)pj(this,g,4,!1),mj(this,f,g),this.ra.delete(g),this.i.delete(g)}}else if(e.M.width<e.T.width||e.M.height<e.T.height){mj(this,e); 
for(f of this.i.keys())pj(this,f,4,!1);this.i.clear()}this.J=new G(window.innerWidth,window.innerHeight)}}));window.addEventListener("resize",d);M(this,()=>{window.removeEventListener("resize",d)});this.Da=Date.now()}}handleAdConfig(a){pi(a,this.j)?(this.g.A("adcf_cl",{preloadAdBreaks:a.preloadAdBreaks||"",sound:a.sound||"",onReady:a.onReady?"true":"false",h5AdsConfig:a.h5AdsConfig?"true":"false"}),a.h5AdsConfig&&(nj(this,a.h5AdsConfig)&&(this.O={...this.O,...a.h5AdsConfig},oj(this,()=>{hj(this,6)}), 
this.pa=!1),this.ma=this.ea=!1),a.sound&&this.l.sound!==a.sound&&(this.l.sound=a.sound,oj(this,()=>{pj(this,1,6)})),a.preloadAdBreaks&&!this.l.preloadAdBreaks?oj(this,()=>{this.l.preloadAdBreaks=a.preloadAdBreaks;if(this.l.preloadAdBreaks==="on"){const b=W(jf)?[1]:[1,2];for(const c of b)this.i.has(c)||ij(this,c)||Y(this,c,0,1)}}):a.preloadAdBreaks&&this.l.preloadAdBreaks&&this.j.error("'adConfig' was already called to set 'preloadAdBreaks' with value "+`'${this.l.preloadAdBreaks}'`),a.onReady&&(this.qa.push(a.onReady), 
jj(this))):this.g.A("inv_adcnf")}async handleAdBreak(a,b){a=ri(a,this.j,this.g);if(a.xa){var c=a.Aa,d=c.type==="reward"?2:1;if(!this.oa||this.R)if(d!==1||this.na<=0||Date.now()-this.Da>this.na*1E3)if(a=wh(),d===1&&a.number_of_interstitial_ad_breaks++,this.g.A("adbr_cl",{type:c.type,name:c.name||"",frequency_cap:d===2?0:this.C,last_intr:Date.now()-this.Ea.g}),b&&c.type!=="preroll")Z(this,c,d,"notReady");else{if(d===2&&this.Ca?.resolve(1),this.i.get(d)||c.type!=="preroll"||q(await q(sj(this,c)))){if(W(jf)&& 
c.type==="reward"&&!this.i.get(d)&&!this.ma&&(this.ma=!0,!q(await q(uj(this,c)))))return;var e=this.i.get(d);if(e)if(d!==2||q(await q(vj(this,c))))if(this.fa)this.j.error("Cannot show ad while another ad is already visible."),Z(this,c,d,"frequencyCapped");else if(rj(this,"beforeAd",c.beforeAd))if(wj(this,d)&&!Vi(this.L))this.g.A("adbr_noad"),Z(this,c,d,"frequencyCapped");else{this.fa=!0;d===1&&a.number_of_interstitial_ads_shown++;this.ea=!0;var f=Date.now(),g=h=>{this.fa=!1;wj(this,d)&&(this.L.g= 
Date.now());h===2||d===2&&h===4?kj(this,"adDismissed",c.adDismissed):h===3&&kj(this,"adViewed",c.adViewed);kj(this,"afterAd",c.afterAd);d===1?Z(this,c,d,"viewed"):Z(this,c,d,h===4?"other":h===2?"dismissed":"viewed");if(h!==4)if(e.dispose(),wj(this,d)){const k=Math.max(0,(W(of)?X(bf):this.C/2)-5);Y(this,d,k,3)}else Y(this,d,d===2?0:this.C,3);wj(this,d);this.g.A("ad_cls",{result:h,adType:d,dur:Date.now()-f})};M(e,()=>{this.fa&&g(4)});wj(this,d)&&Ui(this.L);e.show(g)}else kj(this,"afterAd",c.afterAd), 
Z(this,c,d,"error");else Z(this,c,d,"ignored");else ij(this,d)?(this.g.A("adbr_noad"),Z(this,c,d,this.da.has(d)?"other":"frequencyCapped")):(Y(this,d,0,2),Z(this,c,d,"noAdPreloaded"))}}else this.g.A("adbr_tepgai"),Z(this,c,d,"other");else this.g.A("adbr_naf"),Z(this,c,d,"other")}else this.g.A("inv_plcnf")}handleAdBreakBeforeReady(a){return this.handleAdBreak(a,!0)}F(){for(const a of this.u.values())a.dispose();this.u.clear();for(const a of this.i.values())a.dispose();this.i.clear();this.B&&this.B.dispose(); 
super.F()}};function zj(a){{t.google_llp||(t.google_llp={});var b=t.google_llp;let c=b[7];if(!c){const {promise:d,resolve:e}=new U;c={promise:d,resolve:e};b[7]=c}b=c}b.resolve(a)};Ne(723,()=>{const a=new se;try{Pa(d=>{Ce(a,1196,d)})}catch(d){}var b=Sh(),c=Qh();Ph(b,c);Kh();b=c.i().i()||dc(b,2);b=new Si(new Wh(b));c={error(...d){console.error("[Ad Placement API]",...d)},warn(...d){console.warn("[Ad Placement API]",...d)}};(Ba()?0:v("Trident")||v("MSIE"))?c.warn("Internet Explorer is not supported."):zj(new yj(c,b))}); 
}).call(this,"");
