{"version": "0.2.0", "configurations": [{"name": "EduVerse API", "type": "coreclr", "request": "launch", "preLaunchTask": "build", "program": "${workspaceFolder}/EduVerse/src/EduVerse.API/bin/Debug/net8.0/EduVerse.API.dll", "args": [], "cwd": "${workspaceFolder}/EduVerse/src/EduVerse.API", "stopAtEntry": false, "serverReadyAction": {"action": "openExternally", "pattern": "\\bNow listening on:\\s+(https?://\\S+)", "uriFormat": "%s"}, "env": {"ASPNETCORE_ENVIRONMENT": "Development", "ASPNETCORE_URLS": "http://localhost:5000"}, "sourceFileMap": {"/Views": "${workspaceFolder}/Views"}}]}