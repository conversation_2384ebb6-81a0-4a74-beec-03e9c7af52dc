using EduVerse.Application.DTOs;
using EduVerse.Application.DTOs.Admin;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace EduVerse.Application.Interfaces
{
    /// <summary>
    /// Interface for admin service
    /// </summary>
    public interface IAdminService
    {
        /// <summary>
        /// Get dashboard statistics
        /// </summary>
        /// <returns>Dashboard statistics</returns>
        Task<DashboardStatsDto> GetDashboardStatsAsync();

        /// <summary>
        /// Get recent enrollments
        /// </summary>
        /// <param name="limit">Number of enrollments to return</param>
        /// <returns>List of recent enrollments</returns>
        Task<IEnumerable<EnrollmentDto>> GetRecentEnrollmentsAsync(int limit = 5);

        /// <summary>
        /// Get revenue chart data
        /// </summary>
        /// <param name="period">Period for the chart (daily, weekly, monthly, yearly)</param>
        /// <returns>Revenue chart data</returns>
        Task<ChartDataDto> GetRevenueChartDataAsync(string period = "monthly");

        /// <summary>
        /// Get user growth chart data
        /// </summary>
        /// <param name="period">Period for the chart (daily, weekly, monthly, yearly)</param>
        /// <returns>User growth chart data</returns>
        Task<ChartDataDto> GetUserGrowthChartDataAsync(string period = "monthly");

        /// <summary>
        /// Get all users
        /// </summary>
        /// <returns>List of all users</returns>
        Task<IEnumerable<UserDto>> GetAllUsersAsync();

        /// <summary>
        /// Get user by ID
        /// </summary>
        /// <param name="id">User ID</param>
        /// <returns>User details</returns>
        Task<UserDto> GetUserByIdAsync(int id);

        /// <summary>
        /// Update user
        /// </summary>
        /// <param name="userDto">Updated user data</param>
        /// <returns>Result with updated user</returns>
        Task<ApiResponseDto<UserDto>> UpdateUserAsync(UserUpdateDto userDto);

        /// <summary>
        /// Delete user
        /// </summary>
        /// <param name="id">User ID</param>
        /// <returns>Result of the operation</returns>
        Task<ApiResponseDto> DeleteUserAsync(int id);

        /// <summary>
        /// Get all courses with admin details
        /// </summary>
        /// <returns>List of all courses with admin details</returns>
        Task<AdminCoursesResponseDto> GetAllCoursesWithDetailsAsync();

        /// <summary>
        /// Create a new course
        /// </summary>
        /// <param name="courseDto">Course data</param>
        /// <returns>Result with created course</returns>
        Task<ApiResponseDto<CourseDto>> CreateCourseAsync(CourseCreateDto courseDto);

        /// <summary>
        /// Update a course
        /// </summary>
        /// <param name="courseDto">Updated course data</param>
        /// <returns>Result with updated course</returns>
        Task<ApiResponseDto<CourseDto>> UpdateCourseAsync(CourseUpdateDto courseDto);

        /// <summary>
        /// Delete a course
        /// </summary>
        /// <param name="id">Course ID</param>
        /// <returns>Result of the operation</returns>
        Task<ApiResponseDto> DeleteCourseAsync(int id);

        /// <summary>
        /// Get all payments
        /// </summary>
        /// <param name="page">Page number</param>
        /// <param name="limit">Items per page</param>
        /// <returns>Paginated list of payments</returns>
        Task<PaginatedResponseDto<PaymentDto>> GetAllPaymentsAsync(int page = 1, int limit = 10);

        /// <summary>
        /// Get payment by ID
        /// </summary>
        /// <param name="id">Payment ID</param>
        /// <returns>Payment details</returns>
        Task<PaymentDto> GetPaymentByIdAsync(int id);

        /// <summary>
        /// Update payment status
        /// </summary>
        /// <param name="id">Payment ID</param>
        /// <param name="status">New status</param>
        /// <returns>Result with updated payment</returns>
        Task<ApiResponseDto<PaymentDto>> UpdatePaymentStatusAsync(int id, string status);
    }
}
