<!DOCTYPE html>
<html>
<head>
    <link rel="shortcut icon" type="png" href="images/icon/favicon.png">
    <title>Admin Login - EduVerse</title>
    <link rel="stylesheet" type="text/css" href="admin/css/admin-login.css">
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.4.1/jquery.min.js"></script>
    <script src="frontend/js/api-service.js"></script>
    <script>
    // Define API_BASE_URL directly to ensure it's available
    const API_BASE_URL = 'http://localhost:5217';
</script>
<script src="admin/js/admin-auth.js"></script>
<script>
    // Override the dashboard redirect path
    document.addEventListener('DOMContentLoaded', function() {
        // Check if already logged in as admin
        const user = JSON.parse(localStorage.getItem('user') || '{}');
        const token = localStorage.getItem('token');

        if (token && (user.role === 'Admin' || user.Role === 'Admin')) {
            console.log('Already logged in as admin, redirecting to simplified dashboard');
            window.location.href = 'admin/simple-dashboard.html';
            return;
        }

        const adminLoginForm = document.getElementById('admin-login');
        if (adminLoginForm) {
            adminLoginForm.addEventListener('submit', async function(event) {
                event.preventDefault();

                const username = document.getElementById('admin-email').value;
                const password = document.getElementById('admin-password').value;

                // Show loading state
                document.querySelector('.submit-btn').textContent = 'Logging in...';

                // Simple mock login for testing
                if (username === 'admin' && password === 'Admin@123') {
                    // Create a mock admin user
                    const mockAdminUser = {
                        id: 1,
                        username: 'admin',
                        fullName: 'Admin User',
                        email: '<EMAIL>',
                        Role: 'Admin',
                        role: 'Admin'
                    };

                    // Create a mock token
                    const mockToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxIiwibmFtZSI6IkFkbWluIFVzZXIiLCJyb2xlIjoiQWRtaW4ifQ.8tat9AtQmHePmf';

                    // Store in localStorage
                    localStorage.setItem('user', JSON.stringify(mockAdminUser));
                    localStorage.setItem('token', mockToken);

                    // Redirect to simplified admin dashboard
                    console.log('Admin login successful, redirecting to simplified dashboard');
                    window.location.href = 'admin/simple-dashboard.html';
                } else {
                    document.querySelector('.submit-btn').textContent = 'Login to Admin Portal';
                    alert('Invalid username or password. Try admin/Admin@123');
                }
            });
        }
    });
</script>
</head>
<body>
    <div class="admin-login-container">
        <div class="login-header">
            <img src="images/icon/logo.png" alt="EduVerse Logo">
            <h1>Admin Portal</h1>
        </div>
        <div class="form-box">
            <form id="admin-login" class="input-group">
                <div class="inp">
                    <img src="images/icon/user.png">
                    <input type="text" id="admin-email" class="input-field" placeholder="Admin Username" required="required">
                </div>
                <div class="inp">
                    <img src="images/icon/password.png">
                    <input type="password" id="admin-password" class="input-field" placeholder="Password" required="required">
                </div>
                <div class="remember-me">
                    <input type="checkbox" class="check-box">
                    <span>Remember Password</span>
                </div>
                <button type="submit" class="submit-btn">Login to Admin Portal</button>
            </form>
            <div class="login-footer">
                <p>EduVerse Learning Hub - Admin Access Only</p>
                <a href="index.html">Return to Main Site</a>
            </div>
        </div>
    </div>
</body>
</html>
