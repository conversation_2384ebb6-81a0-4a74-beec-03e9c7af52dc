using EduVerse.Application.DTOs;
using EduVerse.Application.DTOs.Admin;
using EduVerse.Application.DTOs.Payment;
using System.Threading.Tasks;

namespace EduVerse.Application.Interfaces
{
    /// <summary>
    /// Interface for payment service
    /// </summary>
    public interface IPaymentService
    {
        /// <summary>
        /// Create a new payment order
        /// </summary>
        /// <param name="request">Order creation request</param>
        /// <returns>Order creation response</returns>
        Task<ApiResponseDto<CreateOrderResponseDto>> CreateOrderAsync(CreateOrderRequestDto request);

        /// <summary>
        /// Verify payment after completion
        /// </summary>
        /// <param name="request">Payment verification request</param>
        /// <returns>Payment verification response</returns>
        Task<ApiResponseDto<PaymentVerificationResponseDto>> VerifyPaymentAsync(PaymentVerificationRequestDto request);

        /// <summary>
        /// Handle Razorpay webhook events
        /// </summary>
        /// <param name="payload">Webhook payload</param>
        /// <param name="signature">Webhook signature</param>
        /// <returns>Success response</returns>
        Task<ApiResponseDto<string>> HandleWebhookAsync(string payload, string signature);

        /// <summary>
        /// Get payment details by ID
        /// </summary>
        /// <param name="id">Payment ID</param>
        /// <returns>Payment details</returns>
        Task<ApiResponseDto<PaymentDto>> GetPaymentByIdAsync(int id);

        /// <summary>
        /// Get user's payment history
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="page">Page number</param>
        /// <param name="limit">Items per page</param>
        /// <returns>Paginated list of payments</returns>
        Task<PaginatedResponseDto<PaymentDto>> GetUserPaymentsAsync(int userId, int page = 1, int limit = 10);
    }
}
