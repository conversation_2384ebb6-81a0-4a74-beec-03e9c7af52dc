// Feedback Management JavaScript

// Mock data for feedback
const mockFeedback = [
    { id: 1, userId: 2, userName: '<PERSON>', courseId: 1, courseName: 'Web Development Bootcamp', rating: 5, message: 'Great course! I learned a lot and the instructor was very helpful.', status: 'Responded', response: 'Thank you for your feedback! We\'re glad you enjoyed the course.', createdAt: '2023-05-01', respondedAt: '2023-05-02' },
    { id: 2, userId: 3, userName: '<PERSON>', courseId: 2, courseName: 'Data Science Fundamentals', rating: 4, message: 'Very informative course. Could use more practical examples.', status: 'Pending', response: '', createdAt: '2023-05-02', respondedAt: null },
    { id: 3, userId: 4, userName: '<PERSON>', courseId: 3, courseName: 'Mobile App Development', rating: 5, message: 'Excellent course! The instructor explained complex concepts in a simple way.', status: 'Responded', response: 'Thank you for your kind words! We\'re happy to hear you found the course helpful.', createdAt: '2023-05-03', respondedAt: '2023-05-04' },
    { id: 4, userId: 5, userName: '<PERSON>', courseId: 4, courseName: 'UI/UX Design Principles', rating: 3, message: 'The course content was good, but the pace was too fast for beginners.', status: 'Pending', response: '', createdAt: '2023-05-04', respondedAt: null },
    { id: 5, userId: 6, userName: '<PERSON>', courseId: 5, courseName: 'Python Programming', rating: 5, message: 'One of the best programming courses I\'ve taken. Very comprehensive and well-structured.', status: 'Responded', response: 'We appreciate your feedback! We put a lot of effort into structuring our courses for optimal learning.', createdAt: '2023-05-05', respondedAt: '2023-05-06' },
    { id: 6, userId: 7, userName: 'David Miller', courseId: 1, courseName: 'Web Development Bootcamp', rating: 4, message: 'Good course overall. The projects were challenging but rewarding.', status: 'Pending', response: '', createdAt: '2023-05-06', respondedAt: null }
];

// Global variables
let feedback = [];
let filteredFeedback = [];
let currentPage = 1;
const feedbackPerPage = 6;
let selectedFeedbackId = null;

// DOM Elements
document.addEventListener('DOMContentLoaded', function() {
    // Initialize the page
    initFeedbackPage();
    
    // Setup event listeners
    setupEventListeners();
    
    // Update admin info
    updateAdminInfo();
});

// Initialize the feedback page
async function initFeedbackPage() {
    try {
        // Load feedback
        await loadFeedback();
        
        // Update summary cards
        updateSummaryCards();
    } catch (error) {
        console.error('Error initializing feedback page:', error);
    }
}

// Load feedback from API or mock data
async function loadFeedback() {
    try {
        // In a real application, you would fetch from an API
        // For now, use mock data
        feedback = [...mockFeedback];
        
        // Apply filters and render feedback
        applyFilters();
    } catch (error) {
        console.error('Error loading feedback:', error);
    }
}

// Apply filters
function applyFilters() {
    const searchTerm = document.getElementById('search-feedback').value.toLowerCase();
    const statusFilter = document.getElementById('status-filter').value;
    const ratingFilter = document.getElementById('rating-filter').value;
    
    filteredFeedback = feedback.filter(item => {
        // Search filter
        const matchesSearch = 
            item.userName.toLowerCase().includes(searchTerm) ||
            item.courseName.toLowerCase().includes(searchTerm) ||
            item.message.toLowerCase().includes(searchTerm);
        
        // Status filter
        const matchesStatus = statusFilter === '' || item.status === statusFilter;
        
        // Rating filter
        const matchesRating = ratingFilter === '' || item.rating === parseInt(ratingFilter);
        
        return matchesSearch && matchesStatus && matchesRating;
    });
    
    // Reset to first page when filters change
    currentPage = 1;
    
    // Render feedback
    renderFeedback();
    updatePagination();
}

// Render feedback
function renderFeedback() {
    const feedbackCards = document.getElementById('feedback-cards');
    feedbackCards.innerHTML = '';
    
    // Calculate pagination
    const startIndex = (currentPage - 1) * feedbackPerPage;
    const endIndex = Math.min(startIndex + feedbackPerPage, filteredFeedback.length);
    const paginatedFeedback = filteredFeedback.slice(startIndex, endIndex);
    
    // Check if there is feedback to display
    if (paginatedFeedback.length === 0) {
        feedbackCards.innerHTML = '<div class="no-feedback">No feedback found</div>';
    } else {
        // Add feedback cards
        paginatedFeedback.forEach(item => {
            const card = document.createElement('div');
            card.className = 'feedback-card';
            
            // Generate stars HTML
            let starsHtml = '';
            for (let i = 1; i <= 5; i++) {
                if (i <= item.rating) {
                    starsHtml += '<i class="fas fa-star star"></i>';
                } else {
                    starsHtml += '<i class="far fa-star star"></i>';
                }
            }
            
            // Format dates
            const createdDate = new Date(item.createdAt).toLocaleDateString();
            
            // Determine status class
            const statusClass = item.status === 'Responded' ? 'status-responded' : 'status-pending';
            
            card.innerHTML = `
                <div class="feedback-header">
                    <div class="user-info">
                        <img src="../images/icon/user.png" alt="${item.userName}" class="user-avatar">
                        <span class="user-name">${item.userName}</span>
                    </div>
                    <div class="feedback-date">${createdDate}</div>
                </div>
                <div class="feedback-rating">
                    ${starsHtml}
                </div>
                <div class="feedback-content">
                    ${item.message}
                </div>
                <div class="feedback-meta">
                    <div class="feedback-course">${item.courseName}</div>
                    <div class="feedback-status ${statusClass}">${item.status}</div>
                </div>
                <div class="feedback-actions">
                    ${item.status === 'Responded' ? 
                        `<button class="btn btn-secondary view-response-btn" data-id="${item.id}">View Response</button>` : 
                        `<button class="btn btn-primary respond-btn" data-id="${item.id}">Respond</button>`
                    }
                </div>
            `;
            
            feedbackCards.appendChild(card);
        });
    }
    
    // Setup action buttons
    setupActionButtons();
}

// Update pagination
function updatePagination() {
    const pagination = document.getElementById('pagination');
    pagination.innerHTML = '';
    
    const totalPages = Math.ceil(filteredFeedback.length / feedbackPerPage);
    
    // Previous button
    const prevButton = document.createElement('button');
    prevButton.innerHTML = '&laquo; Previous';
    prevButton.disabled = currentPage === 1;
    prevButton.addEventListener('click', () => {
        if (currentPage > 1) {
            currentPage--;
            renderFeedback();
            updatePagination();
        }
    });
    pagination.appendChild(prevButton);
    
    // Page buttons
    for (let i = 1; i <= totalPages; i++) {
        const pageButton = document.createElement('button');
        pageButton.textContent = i;
        pageButton.classList.toggle('active', i === currentPage);
        pageButton.addEventListener('click', () => {
            currentPage = i;
            renderFeedback();
            updatePagination();
        });
        pagination.appendChild(pageButton);
    }
    
    // Next button
    const nextButton = document.createElement('button');
    nextButton.innerHTML = 'Next &raquo;';
    nextButton.disabled = currentPage === totalPages || totalPages === 0;
    nextButton.addEventListener('click', () => {
        if (currentPage < totalPages) {
            currentPage++;
            renderFeedback();
            updatePagination();
        }
    });
    pagination.appendChild(nextButton);
}

// Update summary cards
function updateSummaryCards() {
    // Calculate summary statistics
    const totalFeedback = feedback.length;
    
    const totalRating = feedback.reduce((sum, item) => sum + item.rating, 0);
    const averageRating = totalFeedback > 0 ? (totalRating / totalFeedback).toFixed(1) : '0.0';
    
    const pendingResponses = feedback.filter(item => item.status === 'Pending').length;
    
    const responseRate = totalFeedback > 0 ? Math.round(((totalFeedback - pendingResponses) / totalFeedback) * 100) : 0;
    
    // Update UI
    document.getElementById('total-feedback').textContent = totalFeedback;
    document.getElementById('average-rating').textContent = averageRating;
    document.getElementById('pending-responses').textContent = pendingResponses;
    document.getElementById('response-rate').textContent = `${responseRate}%`;
}

// Setup action buttons
function setupActionButtons() {
    // Respond buttons
    document.querySelectorAll('.respond-btn').forEach(button => {
        button.addEventListener('click', function() {
            const feedbackId = parseInt(this.getAttribute('data-id'));
            showResponseModal(feedbackId);
        });
    });
    
    // View response buttons
    document.querySelectorAll('.view-response-btn').forEach(button => {
        button.addEventListener('click', function() {
            const feedbackId = parseInt(this.getAttribute('data-id'));
            showResponseModal(feedbackId, true);
        });
    });
}

// Show response modal
function showResponseModal(feedbackId, viewOnly = false) {
    const feedbackItem = feedback.find(item => item.id === feedbackId);
    if (!feedbackItem) return;
    
    // Set selected feedback ID
    selectedFeedbackId = feedbackId;
    
    // Update modal title
    document.querySelector('#response-modal .modal-header h2').textContent = 
        viewOnly ? 'View Response' : 'Respond to Feedback';
    
    // Generate stars HTML
    let starsHtml = '';
    for (let i = 1; i <= 5; i++) {
        if (i <= feedbackItem.rating) {
            starsHtml += '<i class="fas fa-star star"></i>';
        } else {
            starsHtml += '<i class="far fa-star star"></i>';
        }
    }
    
    // Format dates
    const createdDate = new Date(feedbackItem.createdAt).toLocaleDateString();
    
    // Update feedback details
    const feedbackDetails = document.getElementById('feedback-details');
    feedbackDetails.innerHTML = `
        <div class="feedback-card" style="box-shadow: none; margin-bottom: 20px;">
            <div class="feedback-header">
                <div class="user-info">
                    <img src="../images/icon/user.png" alt="${feedbackItem.userName}" class="user-avatar">
                    <span class="user-name">${feedbackItem.userName}</span>
                </div>
                <div class="feedback-date">${createdDate}</div>
            </div>
            <div class="feedback-rating">
                ${starsHtml}
            </div>
            <div class="feedback-content">
                ${feedbackItem.message}
            </div>
            <div class="feedback-meta">
                <div class="feedback-course">${feedbackItem.courseName}</div>
            </div>
        </div>
    `;
    
    // Update response text
    const responseText = document.getElementById('response-text');
    responseText.value = feedbackItem.response || '';
    
    // Set readonly if view only
    responseText.readOnly = viewOnly;
    
    // Show/hide submit button
    document.getElementById('submit-response-btn').style.display = viewOnly ? 'none' : 'block';
    
    // Show the modal
    document.getElementById('response-modal').style.display = 'block';
}

// Submit response
function submitResponse() {
    if (!selectedFeedbackId) return;
    
    const responseText = document.getElementById('response-text').value.trim();
    
    if (responseText === '') {
        alert('Please enter a response.');
        return;
    }
    
    const feedbackIndex = feedback.findIndex(item => item.id === selectedFeedbackId);
    
    if (feedbackIndex !== -1) {
        // Update feedback with response
        feedback[feedbackIndex].response = responseText;
        feedback[feedbackIndex].status = 'Responded';
        feedback[feedbackIndex].respondedAt = new Date().toISOString().split('T')[0];
        
        // Refresh the feedback list
        applyFilters();
        
        // Update summary cards
        updateSummaryCards();
        
        // Close the modal
        document.getElementById('response-modal').style.display = 'none';
        
        // Show success message
        alert('Response submitted successfully!');
    }
}

// Setup event listeners
function setupEventListeners() {
    // Search input
    document.getElementById('search-feedback').addEventListener('input', debounce(applyFilters, 300));
    
    // Status filter
    document.getElementById('status-filter').addEventListener('change', applyFilters);
    
    // Rating filter
    document.getElementById('rating-filter').addEventListener('change', applyFilters);
    
    // Export button
    document.getElementById('export-btn').addEventListener('click', function() {
        alert('Exporting feedback...');
        // In a real application, this would generate and download a file
    });
    
    // Close modal button
    document.querySelector('.close').addEventListener('click', function() {
        document.getElementById('response-modal').style.display = 'none';
    });
    
    // Cancel button
    document.getElementById('cancel-btn').addEventListener('click', function() {
        document.getElementById('response-modal').style.display = 'none';
    });
    
    // Submit response button
    document.getElementById('submit-response-btn').addEventListener('click', submitResponse);
    
    // Toggle sidebar
    document.querySelector('.toggle-menu').addEventListener('click', function() {
        document.querySelector('.admin-container').classList.toggle('sidebar-collapsed');
    });
    
    // Admin logout
    document.getElementById('admin-logout').addEventListener('click', function(event) {
        event.preventDefault();
        
        // Clear localStorage
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        
        // Redirect to login page
        window.location.href = '../admin-login.html';
    });
}

// Update admin info
function updateAdminInfo() {
    const user = JSON.parse(localStorage.getItem('user') || '{}');
    const adminNameElement = document.querySelector('.admin-name');
    
    if (adminNameElement && user.fullName) {
        adminNameElement.textContent = user.fullName;
    }
}

// Debounce function for search input
function debounce(func, delay) {
    let timeout;
    return function() {
        const context = this;
        const args = arguments;
        clearTimeout(timeout);
        timeout = setTimeout(() => func.apply(context, args), delay);
    };
}
