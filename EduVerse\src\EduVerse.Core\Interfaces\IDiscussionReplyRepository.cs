using EduVerse.Core.Entities;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace EduVerse.Core.Interfaces
{
    /// <summary>
    /// Interface for discussion reply repository
    /// </summary>
    public interface IDiscussionReplyRepository : IRepository<DiscussionReply>
    {
        /// <summary>
        /// Get replies by discussion ID
        /// </summary>
        /// <param name="discussionId">Discussion ID</param>
        /// <returns>List of replies</returns>
        Task<IEnumerable<DiscussionReply>> GetByDiscussionIdAsync(int discussionId);
        
        /// <summary>
        /// Get replies by user ID
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>List of replies</returns>
        Task<IEnumerable<DiscussionReply>> GetByUserIdAsync(int userId);
        
        /// <summary>
        /// Mark reply as answer
        /// </summary>
        /// <param name="id">Reply ID</param>
        /// <returns>True if successful</returns>
        Task<bool> MarkAsAnswerAsync(int id);
        
        /// <summary>
        /// Get nested replies
        /// </summary>
        /// <param name="parentReplyId">Parent reply ID</param>
        /// <returns>List of nested replies</returns>
        Task<IEnumerable<DiscussionReply>> GetNestedRepliesAsync(int parentReplyId);
    }
}
