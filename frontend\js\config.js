// Configuration settings for EduVerse Learning Hub

// API base URL
const API_BASE_URL = 'http://localhost:5217';

// Razorpay configuration
const RAZORPAY_CONFIG = {
    // Test mode keys (for development)
    test: {
        keyId: 'rzp_test_RXnO6xGp4g5JFJ',
        keySecret: 'jfuEWVBTqsgtjwJl8amjCOeG' // Note: This should be kept secure on the server side
    },
    // Production mode keys (for live payments)
    production: {
        keyId: 'YOUR_LIVE_KEY_ID',
        keySecret: 'YOUR_LIVE_SECRET_KEY' // Note: This should be kept secure on the server side
    },
    // Current mode (test or production)
    mode: 'test'
};

// Get current Razorpay key based on mode
function getRazorpayKey() {
    return RAZORPAY_CONFIG[RAZORPAY_CONFIG.mode].keyId;
}

// Make getRazorpayKey globally available
window.getRazorpayKey = getRazorpayKey;

// Application settings
const APP_CONFIG = {
    appName: 'EduVerse Learning Hub',
    appVersion: '1.0.0',
    supportEmail: '<EMAIL>',
    supportPhone: '+91 1234567890',
    enableRazorpay: true,
    defaultCurrency: 'INR',
    taxRate: 0.18 // 18% GST
};
