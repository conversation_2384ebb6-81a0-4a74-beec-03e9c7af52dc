using EduVerse.Core.Entities;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace EduVerse.Core.Interfaces
{
    /// <summary>
    /// Interface for course repository
    /// </summary>
    public interface ICourseRepository : IRepository<Course>
    {
        /// <summary>
        /// Get courses by category
        /// </summary>
        /// <param name="categoryId">Category ID</param>
        /// <returns>List of courses</returns>
        Task<IReadOnlyList<Course>> GetCoursesByCategoryAsync(int categoryId);

        /// <summary>
        /// Get course with details
        /// </summary>
        /// <param name="courseId">Course ID</param>
        /// <returns>Course with details</returns>
        Task<Course> GetCourseWithDetailsAsync(int courseId);

        /// <summary>
        /// Get all courses with admin details
        /// </summary>
        /// <returns>List of courses with admin details</returns>
        Task<IReadOnlyList<Course>> GetAllCoursesWithDetailsAsync();

        /// <summary>
        /// Get total number of courses
        /// </summary>
        /// <returns>Total number of courses</returns>
        Task<int> GetTotalCoursesAsync();
    }
}
