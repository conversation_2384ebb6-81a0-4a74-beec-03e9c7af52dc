using EduVerse.Application.DTOs;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace EduVerse.Application.Interfaces
{
    public interface ICourseService
    {
        Task<IEnumerable<CourseDto>> GetAllCoursesAsync();
        Task<CourseDto> GetCourseByIdAsync(int id);
        Task<IEnumerable<CourseDto>> GetCoursesByCategoryAsync(int categoryId);
        Task<IEnumerable<CourseCategoryDto>> GetAllCategoriesAsync();
        Task<CourseCategoryDto> GetCategoryByIdAsync(int id);
    }
}
