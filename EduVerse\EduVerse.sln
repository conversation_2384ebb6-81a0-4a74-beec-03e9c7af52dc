﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{8F7E83F8-1E72-4751-8AFD-1A3F3CEA759B}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "EduVerse.API", "src\EduVerse.API\EduVerse.API.csproj", "{5E4168D4-0D1B-4B30-BE3A-2EAF22D93DF9}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "EduVerse.Core", "src\EduVerse.Core\EduVerse.Core.csproj", "{4EDD51B0-6B72-47EA-A948-B61C90086BE8}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "EduVerse.Application", "src\EduVerse.Application\EduVerse.Application.csproj", "{416FFE15-CFC4-433E-9BC7-448C03CD6866}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "EduVerse.Infrastructure", "src\EduVerse.Infrastructure\EduVerse.Infrastructure.csproj", "{5C0922F6-1744-4C16-B32D-2E3EDA1B6BB8}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "tests", "tests", "{150A8A02-317D-4878-9F9C-2457475C064E}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "EduVerse.Tests", "tests\EduVerse.Tests\EduVerse.Tests.csproj", "{51E5463E-8E28-4EBF-AB8E-317343C326E6}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{5E4168D4-0D1B-4B30-BE3A-2EAF22D93DF9}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5E4168D4-0D1B-4B30-BE3A-2EAF22D93DF9}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5E4168D4-0D1B-4B30-BE3A-2EAF22D93DF9}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5E4168D4-0D1B-4B30-BE3A-2EAF22D93DF9}.Release|Any CPU.Build.0 = Release|Any CPU
		{4EDD51B0-6B72-47EA-A948-B61C90086BE8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4EDD51B0-6B72-47EA-A948-B61C90086BE8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4EDD51B0-6B72-47EA-A948-B61C90086BE8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{4EDD51B0-6B72-47EA-A948-B61C90086BE8}.Release|Any CPU.Build.0 = Release|Any CPU
		{416FFE15-CFC4-433E-9BC7-448C03CD6866}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{416FFE15-CFC4-433E-9BC7-448C03CD6866}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{416FFE15-CFC4-433E-9BC7-448C03CD6866}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{416FFE15-CFC4-433E-9BC7-448C03CD6866}.Release|Any CPU.Build.0 = Release|Any CPU
		{5C0922F6-1744-4C16-B32D-2E3EDA1B6BB8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5C0922F6-1744-4C16-B32D-2E3EDA1B6BB8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5C0922F6-1744-4C16-B32D-2E3EDA1B6BB8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5C0922F6-1744-4C16-B32D-2E3EDA1B6BB8}.Release|Any CPU.Build.0 = Release|Any CPU
		{51E5463E-8E28-4EBF-AB8E-317343C326E6}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{51E5463E-8E28-4EBF-AB8E-317343C326E6}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{51E5463E-8E28-4EBF-AB8E-317343C326E6}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{51E5463E-8E28-4EBF-AB8E-317343C326E6}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{5E4168D4-0D1B-4B30-BE3A-2EAF22D93DF9} = {8F7E83F8-1E72-4751-8AFD-1A3F3CEA759B}
		{4EDD51B0-6B72-47EA-A948-B61C90086BE8} = {8F7E83F8-1E72-4751-8AFD-1A3F3CEA759B}
		{416FFE15-CFC4-433E-9BC7-448C03CD6866} = {8F7E83F8-1E72-4751-8AFD-1A3F3CEA759B}
		{5C0922F6-1744-4C16-B32D-2E3EDA1B6BB8} = {8F7E83F8-1E72-4751-8AFD-1A3F3CEA759B}
		{51E5463E-8E28-4EBF-AB8E-317343C326E6} = {150A8A02-317D-4878-9F9C-2457475C064E}
	EndGlobalSection
EndGlobal
