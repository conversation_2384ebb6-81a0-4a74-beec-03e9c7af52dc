using EduVerse.Core.Entities;
using Microsoft.EntityFrameworkCore;
using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace EduVerse.Infrastructure.Data
{
    public class ApplicationDbContext : DbContext
    {
        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options) : base(options)
        {
        }

        public DbSet<User> Users { get; set; }
        public DbSet<Course> Courses { get; set; }
        public DbSet<CourseCategory> CourseCategories { get; set; }
        public DbSet<Quiz> Quizzes { get; set; }
        public DbSet<QuizQuestion> QuizQuestions { get; set; }
        public DbSet<QuizAttempt> QuizAttempts { get; set; }
        public DbSet<QuizAnswer> QuizAnswers { get; set; }
        public DbSet<Feedback> Feedback { get; set; }
        public DbSet<UserStatistics> UserStatistics { get; set; }
        public DbSet<Payment> Payments { get; set; }
        public DbSet<Enrollment> Enrollments { get; set; }
        public DbSet<Discussion> Discussions { get; set; }
        public DbSet<DiscussionReply> DiscussionReplies { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Configure Feedback entity
            modelBuilder.Entity<Feedback>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).IsRequired();
                entity.Property(e => e.Email).IsRequired();
                entity.Property(e => e.Message).IsRequired();
                entity.Property(e => e.CreatedAt).IsRequired();
            });

            // Configure Payment entity
            modelBuilder.Entity<Payment>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Amount).IsRequired().HasColumnType("decimal(18,2)");
                entity.Property(e => e.Date).IsRequired();
                entity.Property(e => e.PaymentMethod).IsRequired();
                entity.Property(e => e.Status).IsRequired();
            });

            // Configure Enrollment entity
            modelBuilder.Entity<Enrollment>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.EnrollmentDate).IsRequired();
                entity.Property(e => e.ProgressPercentage).IsRequired();
            });

            // User
            modelBuilder.Entity<User>()
                .HasIndex(u => u.Email)
                .IsUnique();

            modelBuilder.Entity<User>()
                .HasIndex(u => u.Username)
                .IsUnique();

            // User - UserStatistics (1:1)
            modelBuilder.Entity<User>()
                .HasOne(u => u.Statistics)
                .WithOne(s => s.User)
                .HasForeignKey<UserStatistics>(s => s.UserId);

            // User - QuizAttempt (1:N)
            modelBuilder.Entity<User>()
                .HasMany(u => u.QuizAttempts)
                .WithOne(a => a.User)
                .HasForeignKey(a => a.UserId)
                .OnDelete(DeleteBehavior.Cascade);

            // User - Enrollment (1:N)
            modelBuilder.Entity<User>()
                .HasMany(u => u.Enrollments)
                .WithOne(e => e.User)
                .HasForeignKey(e => e.UserId)
                .OnDelete(DeleteBehavior.Cascade);

            // User - Payment (1:N)
            modelBuilder.Entity<User>()
                .HasMany(u => u.Payments)
                .WithOne(p => p.User)
                .HasForeignKey(p => p.UserId)
                .OnDelete(DeleteBehavior.Cascade);

            // User - Feedback (1:N)
            modelBuilder.Entity<User>()
                .HasMany(u => u.Feedback)
                .WithOne(f => f.User)
                .HasForeignKey(f => f.UserId)
                .OnDelete(DeleteBehavior.Restrict);

            // CourseCategory - Course (1:N)
            modelBuilder.Entity<CourseCategory>()
                .HasMany(c => c.Courses)
                .WithOne(c => c.Category)
                .HasForeignKey(c => c.CategoryId)
                .OnDelete(DeleteBehavior.Cascade);

            // Course - Quiz (1:N)
            modelBuilder.Entity<Course>()
                .HasMany(c => c.Quizzes)
                .WithOne(q => q.Course)
                .HasForeignKey(q => q.CourseId)
                .OnDelete(DeleteBehavior.Cascade);

            // Course - Enrollment (1:N)
            modelBuilder.Entity<Course>()
                .HasMany(c => c.Enrollments)
                .WithOne(e => e.Course)
                .HasForeignKey(e => e.CourseId)
                .OnDelete(DeleteBehavior.Cascade);

            // Course - Payment (1:N)
            modelBuilder.Entity<Course>()
                .HasMany(c => c.Payments)
                .WithOne(p => p.Course)
                .HasForeignKey(p => p.CourseId)
                .OnDelete(DeleteBehavior.Restrict);

            // Quiz - QuizQuestion (1:N)
            modelBuilder.Entity<Quiz>()
                .HasMany(q => q.Questions)
                .WithOne(q => q.Quiz)
                .HasForeignKey(q => q.QuizId)
                .OnDelete(DeleteBehavior.Cascade);

            // Quiz - QuizAttempt (1:N)
            modelBuilder.Entity<Quiz>()
                .HasMany(q => q.Attempts)
                .WithOne(a => a.Quiz)
                .HasForeignKey(a => a.QuizId)
                .OnDelete(DeleteBehavior.Cascade);

            // QuizAttempt - QuizAnswer (1:N)
            modelBuilder.Entity<QuizAttempt>()
                .HasMany(a => a.Answers)
                .WithOne(a => a.Attempt)
                .HasForeignKey(a => a.AttemptId)
                .OnDelete(DeleteBehavior.Cascade);

            // QuizQuestion - QuizAnswer (1:N)
            modelBuilder.Entity<QuizQuestion>()
                .HasMany(q => q.Answers)
                .WithOne(a => a.Question)
                .HasForeignKey(a => a.QuestionId)
                .OnDelete(DeleteBehavior.Restrict);

            // Payment - Enrollment (1:1)
            modelBuilder.Entity<Enrollment>()
                .HasOne(e => e.Payment)
                .WithOne(p => p.Enrollment)
                .HasForeignKey<Enrollment>(e => e.PaymentId);

            // Configure Discussion entity
            modelBuilder.Entity<Discussion>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Title).IsRequired().HasMaxLength(200);
                entity.Property(e => e.Content).IsRequired();
                entity.Property(e => e.CreatedAt).IsRequired();
                entity.Property(e => e.IsResolved).IsRequired().HasDefaultValue(false);
            });

            // Configure DiscussionReply entity
            modelBuilder.Entity<DiscussionReply>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Content).IsRequired();
                entity.Property(e => e.CreatedAt).IsRequired();
                entity.Property(e => e.IsAnswer).IsRequired().HasDefaultValue(false);
            });

            // User - Discussion (1:N)
            modelBuilder.Entity<User>()
                .HasMany(u => u.Discussions)
                .WithOne(d => d.User)
                .HasForeignKey(d => d.UserId)
                .OnDelete(DeleteBehavior.Cascade);

            // User - DiscussionReply (1:N)
            modelBuilder.Entity<User>()
                .HasMany(u => u.DiscussionReplies)
                .WithOne(r => r.User)
                .HasForeignKey(r => r.UserId)
                .OnDelete(DeleteBehavior.Cascade);

            // Course - Discussion (1:N)
            modelBuilder.Entity<Course>()
                .HasMany(c => c.Discussions)
                .WithOne(d => d.Course)
                .HasForeignKey(d => d.CourseId)
                .OnDelete(DeleteBehavior.SetNull);

            // Discussion - DiscussionReply (1:N)
            modelBuilder.Entity<Discussion>()
                .HasMany(d => d.Replies)
                .WithOne(r => r.Discussion)
                .HasForeignKey(r => r.DiscussionId)
                .OnDelete(DeleteBehavior.Cascade);

            // DiscussionReply - DiscussionReply (1:N) for nested replies
            modelBuilder.Entity<DiscussionReply>()
                .HasMany<DiscussionReply>()
                .WithOne(r => r.ParentReply)
                .HasForeignKey(r => r.ParentReplyId)
                .OnDelete(DeleteBehavior.Restrict);
        }

        public override int SaveChanges()
        {
            AddTimestamps();
            return base.SaveChanges();
        }

        public override Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
        {
            AddTimestamps();
            return base.SaveChangesAsync(cancellationToken);
        }

        private void AddTimestamps()
        {
            var entities = ChangeTracker.Entries()
                .Where(x => x.Entity is Course || x.Entity is Quiz || x.Entity is Feedback ||
                           x.Entity is User || x.Entity is Payment || x.Entity is Enrollment ||
                           x.Entity is Discussion || x.Entity is DiscussionReply)
                .Where(x => x.State == EntityState.Added || x.State == EntityState.Modified);

            foreach (var entity in entities)
            {
                var now = DateTime.UtcNow;

                if (entity.State == EntityState.Added)
                {
                    if (entity.Entity is Course course)
                        course.CreatedAt = now;
                    else if (entity.Entity is Quiz quiz)
                        quiz.CreatedAt = now;
                    else if (entity.Entity is Feedback feedback)
                        feedback.CreatedAt = now;
                    else if (entity.Entity is User user)
                        user.CreatedAt = now;
                    else if (entity.Entity is Payment payment)
                        payment.Date = now;
                    else if (entity.Entity is Enrollment enrollment)
                        enrollment.EnrollmentDate = now;
                    else if (entity.Entity is Discussion discussion)
                        discussion.CreatedAt = now;
                    else if (entity.Entity is DiscussionReply reply)
                        reply.CreatedAt = now;
                }
                else
                {
                    if (entity.Entity is Course course)
                        course.UpdatedAt = now;
                    else if (entity.Entity is Quiz quiz)
                        quiz.UpdatedAt = now;
                    else if (entity.Entity is Feedback feedback && feedback.Response != null && feedback.ResponseDate == null)
                        feedback.ResponseDate = now;
                    else if (entity.Entity is Discussion discussion)
                        discussion.UpdatedAt = now;
                    else if (entity.Entity is DiscussionReply reply)
                        reply.UpdatedAt = now;
                }
            }
        }
    }
}
