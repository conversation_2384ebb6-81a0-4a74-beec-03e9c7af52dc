using EduVerse.Core.Entities;
using Microsoft.EntityFrameworkCore;
using EduVerse.Infrastructure.Data;

namespace EduVerse.Infrastructure.Data
{
    public static class QuizSeeder
    {
        public static async Task SeedQuizzes(ApplicationDbContext context)
        {
            if (!await context.Quizzes.AnyAsync())
            {
                // Get the courses from the database
                var javaCourse = await context.Courses.FirstOrDefaultAsync(c => c.Title == "Java Programming");
                var pythonCourse = await context.Courses.FirstOrDefaultAsync(c => c.Title == "Python Programming");
                var jsCourse = await context.Courses.FirstOrDefaultAsync(c => c.Title == "JavaScript");

                if (javaCourse == null || pythonCourse == null || jsCourse == null)
                {
                    return; // Don't seed quizzes if courses don't exist
                }

                // Java Quiz
                var javaQuiz = new Quiz
                {
                    Title = "Java Fundamentals Quiz",
                    Description = "Test your knowledge of Java programming basics",
                    CourseId = javaCourse.Id,
                    Questions = new List<QuizQuestion>
                    {
                        new QuizQuestion
                        {
                            QuestionText = "What is the main method signature in Java?",
                            OptionA = "public static void main(String[] args)",
                            OptionB = "public void main(String[] args)",
                            OptionC = "static void main(String[] args)",
                            OptionD = "public static main(String[] args)",
                            CorrectOption = "A",
                            Points = 10
                        },
                        new QuizQuestion
                        {
                            QuestionText = "Which of these is not a primitive data type in Java?",
                            OptionA = "int",
                            OptionB = "boolean",
                            OptionC = "String",
                            OptionD = "char",
                            CorrectOption = "C",
                            Points = 10
                        }
                    }
                };

                // Python Quiz
                var pythonQuiz = new Quiz
                {
                    Title = "Python Basics Quiz",
                    Description = "Test your knowledge of Python programming fundamentals",
                    CourseId = pythonCourse.Id,
                    Questions = new List<QuizQuestion>
                    {
                        new QuizQuestion
                        {
                            QuestionText = "What is the correct way to create a list in Python?",
                            OptionA = "list = []",
                            OptionB = "list = list()",
                            OptionC = "Both A and B",
                            OptionD = "None of the above",
                            CorrectOption = "C",
                            Points = 10
                        },
                        new QuizQuestion
                        {
                            QuestionText = "Which of these is not a Python data type?",
                            OptionA = "List",
                            OptionB = "Dictionary",
                            OptionC = "Array",
                            OptionD = "Tuple",
                            CorrectOption = "C",
                            Points = 10
                        }
                    }
                };

                // JavaScript Quiz
                var jsQuiz = new Quiz
                {
                    Title = "JavaScript Essentials Quiz",
                    Description = "Test your knowledge of JavaScript programming",
                    CourseId = jsCourse.Id,
                    Questions = new List<QuizQuestion>
                    {
                        new QuizQuestion
                        {
                            QuestionText = "What is the correct way to declare a variable in JavaScript?",
                            OptionA = "var x = 5;",
                            OptionB = "let x = 5;",
                            OptionC = "const x = 5;",
                            OptionD = "All of the above",
                            CorrectOption = "D",
                            Points = 10
                        },
                        new QuizQuestion
                        {
                            QuestionText = "Which of these is not a JavaScript framework?",
                            OptionA = "React",
                            OptionB = "Angular",
                            OptionC = "Django",
                            OptionD = "Vue",
                            CorrectOption = "C",
                            Points = 10
                        }
                    }
                };

                await context.Quizzes.AddRangeAsync(javaQuiz, pythonQuiz, jsQuiz);
                await context.SaveChangesAsync();
            }
        }
    }
} 