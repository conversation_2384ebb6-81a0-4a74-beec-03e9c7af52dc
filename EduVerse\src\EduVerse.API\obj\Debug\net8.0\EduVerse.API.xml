<?xml version="1.0"?>
<doc>
    <assembly>
        <name>EduVerse.API</name>
    </assembly>
    <members>
        <member name="T:EduVerse.API.Controllers.AdminController">
            <summary>
            Controller for admin-specific operations
            </summary>
        </member>
        <member name="M:EduVerse.API.Controllers.AdminController.#ctor(EduVerse.Application.Interfaces.IAdminService,EduVerse.Application.Interfaces.IAuthService,EduVerse.Application.Interfaces.ICourseService,EduVerse.Application.Interfaces.IFeedbackService)">
            <summary>
            Constructor for AdminController
            </summary>
        </member>
        <member name="M:EduVerse.API.Controllers.AdminController.Login(EduVerse.Application.DTOs.LoginDto)">
            <summary>
            Admin login
            </summary>
            <param name="loginDto">Login credentials</param>
            <returns>Authentication response with JWT token if successful</returns>
        </member>
        <member name="M:EduVerse.API.Controllers.AdminController.GetDashboardStats">
            <summary>
            Get dashboard statistics
            </summary>
            <returns>Dashboard statistics</returns>
        </member>
        <member name="M:EduVerse.API.Controllers.AdminController.GetRecentEnrollments(System.Int32)">
            <summary>
            Get recent enrollments
            </summary>
            <param name="limit">Number of enrollments to return</param>
            <returns>List of recent enrollments</returns>
        </member>
        <member name="M:EduVerse.API.Controllers.AdminController.GetRecentFeedback(System.Int32)">
            <summary>
            Get recent feedback
            </summary>
            <param name="limit">Number of feedback items to return</param>
            <returns>List of recent feedback</returns>
        </member>
        <member name="M:EduVerse.API.Controllers.AdminController.GetRevenueChartData(System.String)">
            <summary>
            Get revenue chart data
            </summary>
            <param name="period">Period for the chart (daily, weekly, monthly, yearly)</param>
            <returns>Revenue chart data</returns>
        </member>
        <member name="M:EduVerse.API.Controllers.AdminController.GetUserGrowthChartData(System.String)">
            <summary>
            Get user growth chart data
            </summary>
            <param name="period">Period for the chart (daily, weekly, monthly, yearly)</param>
            <returns>User growth chart data</returns>
        </member>
        <member name="M:EduVerse.API.Controllers.AdminController.GetAllUsers">
            <summary>
            Get all users
            </summary>
            <returns>List of all users</returns>
        </member>
        <member name="M:EduVerse.API.Controllers.AdminController.GetUserById(System.Int32)">
            <summary>
            Get user by ID
            </summary>
            <param name="id">User ID</param>
            <returns>User details</returns>
        </member>
        <member name="M:EduVerse.API.Controllers.AdminController.UpdateUser(System.Int32,EduVerse.Application.DTOs.Admin.UserUpdateDto)">
            <summary>
            Update user
            </summary>
            <param name="id">User ID</param>
            <param name="userDto">Updated user data</param>
            <returns>Updated user</returns>
        </member>
        <member name="M:EduVerse.API.Controllers.AdminController.DeleteUser(System.Int32)">
            <summary>
            Delete user
            </summary>
            <param name="id">User ID</param>
            <returns>Success response</returns>
        </member>
        <member name="M:EduVerse.API.Controllers.AdminController.GetAllCourses">
            <summary>
            Get all courses with admin details
            </summary>
            <returns>List of all courses with admin details</returns>
        </member>
        <member name="M:EduVerse.API.Controllers.AdminController.CreateCourse(EduVerse.Application.DTOs.Admin.CourseCreateDto)">
            <summary>
            Create a new course
            </summary>
            <param name="courseDto">Course data</param>
            <returns>Created course</returns>
        </member>
        <member name="M:EduVerse.API.Controllers.AdminController.GetCourse(System.Int32)">
            <summary>
            Get course by ID
            </summary>
            <param name="id">Course ID</param>
            <returns>Course details</returns>
        </member>
        <member name="M:EduVerse.API.Controllers.AdminController.UpdateCourse(System.Int32,EduVerse.Application.DTOs.Admin.CourseUpdateDto)">
            <summary>
            Update a course
            </summary>
            <param name="id">Course ID</param>
            <param name="courseDto">Updated course data</param>
            <returns>Updated course</returns>
        </member>
        <member name="M:EduVerse.API.Controllers.AdminController.DeleteCourse(System.Int32)">
            <summary>
            Delete a course
            </summary>
            <param name="id">Course ID</param>
            <returns>Success response</returns>
        </member>
        <member name="M:EduVerse.API.Controllers.AdminController.GetAllPayments(System.Int32,System.Int32)">
            <summary>
            Get all payments
            </summary>
            <param name="page">Page number</param>
            <param name="limit">Items per page</param>
            <returns>Paginated list of payments</returns>
        </member>
        <member name="M:EduVerse.API.Controllers.AdminController.GetPaymentById(System.Int32)">
            <summary>
            Get payment by ID
            </summary>
            <param name="id">Payment ID</param>
            <returns>Payment details</returns>
        </member>
        <member name="M:EduVerse.API.Controllers.AdminController.UpdatePaymentStatus(System.Int32,EduVerse.Application.DTOs.Admin.PaymentStatusUpdateDto)">
            <summary>
            Update payment status
            </summary>
            <param name="id">Payment ID</param>
            <param name="statusDto">Status update data</param>
            <returns>Updated payment</returns>
        </member>
        <member name="M:EduVerse.API.Controllers.AdminController.GetAllFeedback(System.Int32,System.Int32)">
            <summary>
            Get all feedback
            </summary>
            <param name="page">Page number</param>
            <param name="limit">Items per page</param>
            <returns>Paginated list of feedback</returns>
        </member>
        <member name="M:EduVerse.API.Controllers.AdminController.RespondToFeedback(System.Int32,EduVerse.Application.DTOs.Admin.FeedbackResponseDto)">
            <summary>
            Respond to feedback
            </summary>
            <param name="id">Feedback ID</param>
            <param name="responseDto">Response data</param>
            <returns>Updated feedback</returns>
        </member>
        <member name="T:EduVerse.API.Controllers.AuthController">
            <summary>
            Controller for handling authentication operations
            </summary>
        </member>
        <member name="M:EduVerse.API.Controllers.AuthController.#ctor(EduVerse.Application.Interfaces.IAuthService)">
            <summary>
            Constructor for AuthController
            </summary>
            <param name="authService">The authentication service</param>
        </member>
        <member name="M:EduVerse.API.Controllers.AuthController.Register(EduVerse.Application.DTOs.RegisterDto)">
            <summary>
            Register a new user
            </summary>
            <param name="registerDto">User registration information</param>
            <returns>Authentication response with JWT token if successful</returns>
            <response code="200">Returns the authentication response with token</response>
            <response code="400">If the registration information is invalid</response>
        </member>
        <member name="M:EduVerse.API.Controllers.AuthController.Login(EduVerse.Application.DTOs.LoginDto)">
            <summary>
            Login with existing credentials
            </summary>
            <param name="loginDto">Login credentials</param>
            <returns>Authentication response with JWT token if successful</returns>
            <response code="200">Returns the authentication response with token</response>
            <response code="400">If the credentials are invalid</response>
        </member>
        <member name="M:EduVerse.API.Controllers.AuthController.GetCurrentUser">
            <summary>
            Get the current authenticated user's information
            </summary>
            <returns>User information</returns>
            <response code="200">Returns the user information</response>
            <response code="401">If the user is not authenticated</response>
            <response code="404">If the user is not found</response>
        </member>
        <member name="T:EduVerse.API.Controllers.DiscussionController">
            <summary>
            Controller for discussion functionality
            </summary>
        </member>
        <member name="M:EduVerse.API.Controllers.DiscussionController.#ctor(EduVerse.Application.Interfaces.IDiscussionService)">
            <summary>
            Constructor
            </summary>
            <param name="discussionService">Discussion service</param>
        </member>
        <member name="M:EduVerse.API.Controllers.DiscussionController.GetAll">
            <summary>
            Get all discussions
            </summary>
            <returns>List of discussions</returns>
        </member>
        <member name="M:EduVerse.API.Controllers.DiscussionController.GetById(System.Int32)">
            <summary>
            Get discussion by ID
            </summary>
            <param name="id">Discussion ID</param>
            <returns>Discussion</returns>
        </member>
        <member name="M:EduVerse.API.Controllers.DiscussionController.GetByUserId(System.Int32)">
            <summary>
            Get discussions by user ID
            </summary>
            <param name="userId">User ID</param>
            <returns>List of discussions</returns>
        </member>
        <member name="M:EduVerse.API.Controllers.DiscussionController.GetByCourseId(System.Int32)">
            <summary>
            Get discussions by course ID
            </summary>
            <param name="courseId">Course ID</param>
            <returns>List of discussions</returns>
        </member>
        <member name="M:EduVerse.API.Controllers.DiscussionController.Create(EduVerse.Application.DTOs.CreateDiscussionDto)">
            <summary>
            Create a new discussion
            </summary>
            <param name="discussionDto">Discussion data</param>
            <returns>Created discussion</returns>
        </member>
        <member name="M:EduVerse.API.Controllers.DiscussionController.Update(System.Int32,EduVerse.Application.DTOs.UpdateDiscussionDto)">
            <summary>
            Update a discussion
            </summary>
            <param name="id">Discussion ID</param>
            <param name="discussionDto">Discussion data</param>
            <returns>Updated discussion</returns>
        </member>
        <member name="M:EduVerse.API.Controllers.DiscussionController.Delete(System.Int32)">
            <summary>
            Delete a discussion
            </summary>
            <param name="id">Discussion ID</param>
            <returns>No content</returns>
        </member>
        <member name="M:EduVerse.API.Controllers.DiscussionController.MarkAsResolved(System.Int32)">
            <summary>
            Mark discussion as resolved
            </summary>
            <param name="id">Discussion ID</param>
            <returns>No content</returns>
        </member>
        <member name="M:EduVerse.API.Controllers.DiscussionController.GetReplies(System.Int32)">
            <summary>
            Get replies for discussion
            </summary>
            <param name="discussionId">Discussion ID</param>
            <returns>List of replies</returns>
        </member>
        <member name="M:EduVerse.API.Controllers.DiscussionController.AddReply(System.Int32,EduVerse.Application.DTOs.CreateDiscussionReplyDto)">
            <summary>
            Add reply to discussion
            </summary>
            <param name="discussionId">Discussion ID</param>
            <param name="replyDto">Reply data</param>
            <returns>Created reply</returns>
        </member>
        <member name="M:EduVerse.API.Controllers.DiscussionController.MarkReplyAsAnswer(System.Int32)">
            <summary>
            Mark reply as answer
            </summary>
            <param name="replyId">Reply ID</param>
            <returns>No content</returns>
        </member>
        <member name="T:EduVerse.API.Controllers.HealthController">
            <summary>
            Controller for health checks
            </summary>
        </member>
        <member name="M:EduVerse.API.Controllers.HealthController.Get">
            <summary>
            Check if the API is running
            </summary>
            <returns>Health status</returns>
        </member>
        <member name="T:EduVerse.API.Controllers.PaymentsController">
            <summary>
            Controller for payment operations
            </summary>
        </member>
        <member name="M:EduVerse.API.Controllers.PaymentsController.#ctor(EduVerse.Application.Interfaces.IPaymentService,Microsoft.Extensions.Logging.ILogger{EduVerse.API.Controllers.PaymentsController})">
            <summary>
            Constructor
            </summary>
        </member>
        <member name="M:EduVerse.API.Controllers.PaymentsController.CreateOrder(EduVerse.Application.DTOs.Payment.CreateOrderRequestDto)">
            <summary>
            Create a new payment order
            </summary>
            <param name="request">Order creation request</param>
            <returns>Order creation response</returns>
        </member>
        <member name="M:EduVerse.API.Controllers.PaymentsController.VerifyPayment(EduVerse.Application.DTOs.Payment.PaymentVerificationRequestDto)">
            <summary>
            Verify payment after completion
            </summary>
            <param name="request">Payment verification request</param>
            <returns>Payment verification response</returns>
        </member>
        <member name="M:EduVerse.API.Controllers.PaymentsController.HandleWebhook">
            <summary>
            Handle Razorpay webhook events
            </summary>
            <returns>Success response</returns>
        </member>
        <member name="M:EduVerse.API.Controllers.PaymentsController.GetPaymentHistory(System.Int32,System.Int32)">
            <summary>
            Get user's payment history
            </summary>
            <param name="page">Page number</param>
            <param name="limit">Items per page</param>
            <returns>Paginated list of payments</returns>
        </member>
        <member name="M:EduVerse.API.Controllers.PaymentsController.GetPayment(System.Int32)">
            <summary>
            Get payment details by ID
            </summary>
            <param name="id">Payment ID</param>
            <returns>Payment details</returns>
        </member>
        <member name="T:EduVerse.API.Mapping.DiscussionMappingProfile">
            <summary>
            AutoMapper profile for discussion entities
            </summary>
        </member>
        <member name="M:EduVerse.API.Mapping.DiscussionMappingProfile.#ctor">
            <summary>
            Constructor
            </summary>
        </member>
    </members>
</doc>
